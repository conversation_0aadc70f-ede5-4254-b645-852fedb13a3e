#!/bin/bash

# BenefitLens API Security Test Suite
# Tests all endpoints to ensure proper access control

BASE_URL="${BASE_URL:-https://benefitlens.de}"
BROWSER_HEADERS='-H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" -H "Referer: $BASE_URL/" -H "Origin: $BASE_URL"'

echo "🔒 BenefitLens API Security Test Suite"
echo "Using BASE_URL=$BASE_URL"
echo "======================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to test endpoint
test_endpoint() {
    local endpoint=$1
    local test_name=$2
    local expected_direct=$3  # Expected status for direct access
    local expected_browser=$4 # Expected status for browser access

    TOTAL_TESTS=$((TOTAL_TESTS + 2))

    echo -e "${BLUE}Testing: $test_name${NC}"

    # Test 1: Direct API access (should be blocked)
    echo -n "  Direct access: "
    direct_status=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$endpoint")
    if [ "$direct_status" = "$expected_direct" ]; then
        echo -e "${GREEN}✓ BLOCKED ($direct_status)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAILED (got $direct_status, expected $expected_direct)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # Test 2: Browser-like access (should be allowed)
    echo -n "  Browser access: "
    browser_status=$(eval "curl -s -o /dev/null -w \"%{http_code}\" $BROWSER_HEADERS \"$BASE_URL$endpoint\"")
    if [ "$browser_status" = "$expected_browser" ]; then
        echo -e "${GREEN}✓ ALLOWED ($browser_status)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAILED (got $browser_status, expected $expected_browser)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    echo ""
}

# Function to test POST endpoint with JSON payload
test_post_endpoint() {
    local endpoint=$1
    local test_name=$2
    local json_payload=$3
    local expected_direct=$4  # Expected status for direct access
    local expected_browser=$5 # Expected status for browser access

    TOTAL_TESTS=$((TOTAL_TESTS + 2))

    echo -e "${BLUE}Testing: $test_name (POST)${NC}"

    # Test 1: Direct API access (should be blocked)
    echo -n "  Direct access: "
    direct_status=$(curl -s -o /dev/null -w "%{http_code}" -X POST -H "Content-Type: application/json" -d "$json_payload" "$BASE_URL$endpoint")
    if [ "$direct_status" = "$expected_direct" ]; then
        echo -e "${GREEN}✓ BLOCKED ($direct_status)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAILED (got $direct_status, expected $expected_direct)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    # Test 2: Browser-like access (should be allowed)
    echo -n "  Browser access: "
    browser_status=$(eval "curl -s -o /dev/null -w \"%{http_code}\" -X POST -H \"Content-Type: application/json\" -d \"$json_payload\" $BROWSER_HEADERS \"$BASE_URL$endpoint\"")
    if [ "$browser_status" = "$expected_browser" ]; then
        echo -e "${GREEN}✓ ALLOWED ($browser_status)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAILED (got $browser_status, expected $expected_browser)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    echo ""
}

# Function to test public endpoint
test_public_endpoint() {
    local endpoint=$1
    local test_name=$2
    local expected_status=$3
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${BLUE}Testing: $test_name (Public)${NC}"
    echo -n "  Public access: "
    status=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$endpoint")
    if [ "$status" = "$expected_status" ]; then
        echo -e "${GREEN}✓ ACCESSIBLE ($status)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAILED (got $status, expected $expected_status)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    echo ""
}

echo "🚀 Starting API Security Tests..."
echo ""

# Test protected data endpoints (should block direct, allow browser)
test_endpoint "/api/companies" "Companies API" "403" "200"
test_endpoint "/api/benefits" "Benefits API" "403" "200"
test_endpoint "/api/search?q=test" "Search API" "403" "200"
test_endpoint "/api/filter-options/benefits" "Filter Options - Benefits" "403" "200"
test_endpoint "/api/filter-options/sizes" "Filter Options - Sizes" "403" "200"
test_endpoint "/api/filter-options/industries" "Filter Options - Industries" "403" "200"
test_endpoint "/api/benefit-categories" "Benefit Categories" "403" "200"
test_endpoint "/api/locations/suggestions?q=Berlin" "Location Suggestions" "403" "200"
test_endpoint "/api/saved-companies" "Saved Companies API" "403" "307"
test_endpoint "/api/user/saved-companies" "User Saved Companies API" "403" "401"
test_endpoint "/api/benefit-removal-disputes" "Benefit Disputes API" "403" "307"

# Test new batch and dispute endpoints
echo -e "${YELLOW}🔍 Testing New Batch and Dispute APIs${NC}"
echo ""

# Batch endpoints - now properly protected with requireWebUIOrAdmin
# Note: These may return 500 with invalid test data, but the important thing is they block direct access (403)
test_post_endpoint "/api/benefit-verifications/batch" "Benefit Verifications Batch" '{"companyBenefitIds":["test"]}' "403" "500"
test_post_endpoint "/api/benefit-removal-disputes/batch" "Benefit Disputes Batch" '{"companyBenefitIds":["test"]}' "403" "500"
test_post_endpoint "/api/benefit-verifications/user/batch" "User Benefit Verifications Batch" '{"companyBenefitIds":["test"]}' "403" "500"

# Individual dispute endpoints - these should be properly protected
test_endpoint "/api/benefit-removal-disputes/nonexistent" "Individual Benefit Dispute" "403" "500"
test_post_endpoint "/api/benefit-removal-disputes/cancel" "Cancel Benefit Dispute (Auth Required)" '{"disputeId":"test"}' "307" "307"

# Test analytics endpoints (mixed access control)
test_public_endpoint "/api/analytics/overview" "Analytics Overview (Public)" "200"
test_public_endpoint "/api/analytics/benefit-trends" "Analytics Benefit Trends (Auth Required)" "401"
test_public_endpoint "/api/analytics/company-insights" "Analytics Company Insights (Auth Required)" "401"

# Test admin endpoints (should require authentication - 401 from middleware or 307 from auth errors)
test_public_endpoint "/api/admin/stats" "Admin Stats (Middleware Protected)" "401"
test_public_endpoint "/api/admin/companies" "Admin Companies (Auth Protected)" "307"
test_public_endpoint "/api/admin/users" "Admin Users (Auth Protected)" "307"
test_public_endpoint "/api/admin/activities" "Admin Activities (Auth Protected)" "307"

# Test impersonation endpoints (admin-only access)
test_post_endpoint "/api/admin/impersonate" "Admin Impersonate (Admin Required)" '{"userId":"test-user-id"}' "307" "307"
test_post_endpoint "/api/admin/exit-impersonation" "Admin Exit Impersonation (Auth Required)" '{}' "401" "401"

# Test health endpoints (should be public)
test_public_endpoint "/api/health" "Health Check" "200"

# Test auth endpoints (should be public)
test_public_endpoint "/api/auth/me" "Auth Me" "401"

echo "📊 Test Results Summary"
echo "======================"
echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"
echo ""

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed! API security is working correctly.${NC}"
    exit 0
else
    echo -e "${RED}❌ Some tests failed. Please review the security implementation.${NC}"
    exit 1
fi
