#!/bin/bash

# Safari Testing Script for BenefitLens
# This script runs Safari/WebKit tests in a Docker container with all necessary dependencies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
    print_success "docker-compose is available"
}

# Function to build the Playwright Docker image
build_playwright_image() {
    print_status "Building Playwright Docker image with Safari support..."
    docker-compose -f docker-compose.playwright.yml build playwright
    print_success "Playwright Docker image built successfully"
}

# Function to start test infrastructure
start_test_infrastructure() {
    print_status "Starting test infrastructure (PostgreSQL, App)..."
    docker-compose -f docker-compose.playwright.yml up -d postgres-test app-test
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if app is responding
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
            print_success "Application is ready"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_error "Application failed to start after $max_attempts attempts"
            docker-compose -f docker-compose.playwright.yml logs app-test
            exit 1
        fi
        
        print_status "Waiting for application... (attempt $attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
}

# Function to run Safari tests
run_safari_tests() {
    local test_pattern="$1"
    local headed="$2"
    
    print_status "Running Safari/WebKit tests in Docker..."
    
    local cmd="npx playwright test --config=playwright.docker.config.ts --project=webkit"
    
    if [ -n "$test_pattern" ]; then
        cmd="$cmd --grep=\"$test_pattern\""
    fi
    
    if [ "$headed" = "true" ]; then
        cmd="$cmd --headed"
    fi
    
    docker-compose -f docker-compose.playwright.yml --profile testing run --rm playwright $cmd
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up test infrastructure..."
    docker-compose -f docker-compose.playwright.yml down
    print_success "Cleanup completed"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [TEST_PATTERN]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -b, --build         Build the Playwright Docker image"
    echo "  -c, --cleanup       Cleanup test infrastructure"
    echo "  -r, --reset         Reset test infrastructure (cleanup and rebuild)"
    echo "  --headed            Run tests in headed mode (visible browser)"
    echo "  --setup-only        Only setup infrastructure, don't run tests"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run all Safari tests"
    echo "  $0 \"Filter Dropdown\"                # Run tests matching pattern"
    echo "  $0 --headed \"Cross-Browser\"         # Run tests in headed mode"
    echo "  $0 --build                           # Build Docker image and run tests"
    echo "  $0 --cleanup                         # Cleanup test infrastructure"
}

# Parse command line arguments
BUILD=false
CLEANUP_ONLY=false
RESET=false
HEADED=false
SETUP_ONLY=false
TEST_PATTERN=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -b|--build)
            BUILD=true
            shift
            ;;
        -c|--cleanup)
            CLEANUP_ONLY=true
            shift
            ;;
        -r|--reset)
            RESET=true
            shift
            ;;
        --headed)
            HEADED=true
            shift
            ;;
        --setup-only)
            SETUP_ONLY=true
            shift
            ;;
        *)
            if [ -z "$TEST_PATTERN" ]; then
                TEST_PATTERN="$1"
            else
                print_error "Unknown option: $1"
                show_usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Main execution
main() {
    print_status "🧪 BenefitLens Safari Testing Script"
    print_status "======================================"
    
    # Check prerequisites
    check_docker
    check_docker_compose
    
    # Handle cleanup-only mode
    if [ "$CLEANUP_ONLY" = true ]; then
        cleanup
        exit 0
    fi
    
    # Handle reset mode
    if [ "$RESET" = true ]; then
        cleanup
        BUILD=true
    fi
    
    # Build image if requested
    if [ "$BUILD" = true ]; then
        build_playwright_image
    fi
    
    # Start test infrastructure
    start_test_infrastructure
    
    # Run tests unless setup-only mode
    if [ "$SETUP_ONLY" = false ]; then
        run_safari_tests "$TEST_PATTERN" "$HEADED"
        print_success "Safari tests completed!"
    else
        print_success "Test infrastructure is ready. You can now run tests manually."
        print_status "To run Safari tests: npm run test:e2e:safari"
        print_status "To cleanup: $0 --cleanup"
    fi
}

# Trap to ensure cleanup on script exit
trap cleanup EXIT

# Run main function
main "$@"
