#!/usr/bin/env node

/**
 * German Employee Benefits Import Script
 * 
 * This script imports comprehensive German employee benefits into the BenefitLens database.
 * It includes statutory benefits, wellness programs, and modern German company perks.
 * 
 * Usage:
 *   node scripts/import-german-benefits.js [--dry-run] [--category=CATEGORY]
 * 
 * Options:
 *   --dry-run: Preview changes without actually importing
 *   --category: Import specific category only (health, financial, time_off, wellness, work_life, development, other)
 */

const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  user: process.env.DB_USER || 'workwell_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'workwell',
  password: process.env.DB_PASSWORD || 'workwell_password',
  port: process.env.DB_PORT || 5432,
});

// Comprehensive German Employee Benefits
const GERMAN_BENEFITS = [
  // Health & Medical Benefits
  {
    name: 'Statutory Health Insurance (Gesetzliche Krankenversicherung)',
    category: 'health',
    icon: '🏥',
    description: 'Mandatory health insurance coverage as required by German law'
  },
  {
    name: 'Private Health Insurance (Private Krankenversicherung)',
    category: 'health',
    icon: '🏥',
    description: 'Enhanced private health insurance coverage'
  },
  {
    name: 'Dental Insurance Plus (Zahnzusatzversicherung)',
    category: 'health',
    icon: '🦷',
    description: 'Additional dental coverage beyond statutory insurance'
  },
  {
    name: 'Company Health Check-ups (Betriebsärztliche Untersuchungen)',
    category: 'health',
    icon: '🩺',
    description: 'Regular preventive health examinations provided by company'
  },
  {
    name: 'Mental Health Support (Psychologische Betreuung)',
    category: 'health',
    icon: '🧠',
    description: 'Employee assistance programs and mental health counseling'
  },
  {
    name: 'Occupational Health Services (Arbeitsmedizin)',
    category: 'health',
    icon: '⚕️',
    description: 'Workplace health and safety medical services'
  },

  // Financial Benefits
  {
    name: 'Christmas Bonus (Weihnachtsgeld)',
    category: 'financial',
    icon: '🎄',
    description: '13th month salary typically paid in November/December'
  },
  {
    name: 'Holiday Bonus (Urlaubsgeld)',
    category: 'financial',
    icon: '🏖️',
    description: 'Additional payment for vacation expenses'
  },
  {
    name: 'Company Pension Scheme (Betriebliche Altersvorsorge)',
    category: 'financial',
    icon: '💰',
    description: 'Employer-sponsored retirement savings plan'
  },
  {
    name: 'Capital-Forming Benefits (Vermögenswirksame Leistungen)',
    category: 'financial',
    icon: '💎',
    description: 'Employer contributions to employee savings plans'
  },
  {
    name: 'Profit Sharing (Gewinnbeteiligung)',
    category: 'financial',
    icon: '📈',
    description: 'Share in company profits distributed to employees'
  },
  {
    name: 'Employee Stock Purchase Plan (Mitarbeiterbeteiligung)',
    category: 'financial',
    icon: '📊',
    description: 'Opportunity to purchase company shares at discounted rates'
  },

  // Time Off Benefits
  {
    name: 'Statutory Vacation (Gesetzlicher Urlaub)',
    category: 'time_off',
    icon: '🏖️',
    description: 'Minimum 24-30 working days of paid vacation per year'
  },
  {
    name: 'Extended Vacation Days (Zusätzliche Urlaubstage)',
    category: 'time_off',
    icon: '🌴',
    description: 'Additional vacation days beyond statutory minimum'
  },
  {
    name: 'Sick Leave (Lohnfortzahlung im Krankheitsfall)',
    category: 'time_off',
    icon: '🤒',
    description: 'Continued salary payment during illness up to 6 weeks'
  },
  {
    name: 'Parental Leave (Elternzeit)',
    category: 'time_off',
    icon: '👶',
    description: 'Up to 3 years of parental leave with job protection'
  },
  {
    name: 'Sabbatical Leave (Sabbatjahr)',
    category: 'time_off',
    icon: '🎒',
    description: 'Extended leave for personal development or travel'
  },
  {
    name: 'Special Leave Days (Sonderurlaub)',
    category: 'time_off',
    icon: '📅',
    description: 'Additional days off for special occasions (wedding, moving, etc.)'
  },

  // Wellness Benefits
  {
    name: 'Urban Sports Club Membership',
    category: 'wellness',
    icon: '🏃‍♂️',
    description: 'Access to fitness studios, pools, and sports activities across Germany'
  },
  {
    name: 'EGYM Wellpass',
    category: 'wellness',
    icon: '💪',
    description: 'Digital fitness and wellness platform with gym access'
  },
  {
    name: 'Hansefit Membership',
    category: 'wellness',
    icon: '🏋️‍♀️',
    description: 'Corporate fitness program with nationwide gym access'
  },
  {
    name: 'Company Sports Teams (Betriebssport)',
    category: 'wellness',
    icon: '⚽',
    description: 'Organized company sports activities and teams'
  },
  {
    name: 'Massage Services (Massagen am Arbeitsplatz)',
    category: 'wellness',
    icon: '💆‍♀️',
    description: 'On-site massage therapy services'
  },
  {
    name: 'Ergonomic Workplace Setup (Ergonomischer Arbeitsplatz)',
    category: 'wellness',
    icon: '🪑',
    description: 'Ergonomic furniture and equipment for healthy working'
  },

  // Work-Life Balance
  {
    name: 'Flexible Working Hours (Gleitzeit)',
    category: 'work_life',
    icon: '⏰',
    description: 'Flexible start and end times within core hours'
  },
  {
    name: 'Remote Work (Homeoffice)',
    category: 'work_life',
    icon: '🏠',
    description: 'Option to work from home or other remote locations'
  },
  {
    name: 'Part-Time Work Options (Teilzeitarbeit)',
    category: 'work_life',
    icon: '⏱️',
    description: 'Reduced working hours with proportional salary'
  },
  {
    name: 'Job Sharing (Arbeitsplatz-Teilung)',
    category: 'work_life',
    icon: '👥',
    description: 'Sharing one full-time position between two employees'
  },
  {
    name: 'Compressed Work Week (Verdichtete Arbeitszeit)',
    category: 'work_life',
    icon: '📊',
    description: 'Working full hours in fewer days per week'
  },
  {
    name: 'Viertage Woche (4-Day Work Week)',
    category: 'work_life',
    icon: '📅',
    description: 'Four-day work week with full salary - modern work-life balance approach'
  },

  // Professional Development
  {
    name: 'Training Budget (Weiterbildungsbudget)',
    category: 'development',
    icon: '📚',
    description: 'Annual budget for professional development and training'
  },
  {
    name: 'Language Learning Support (Sprachkurse)',
    category: 'development',
    icon: '🗣️',
    description: 'Company-sponsored language courses and learning programs'
  },
  {
    name: 'Conference Attendance (Konferenz-Teilnahme)',
    category: 'development',
    icon: '🎤',
    description: 'Paid attendance at industry conferences and events'
  },
  {
    name: 'Internal Training Programs (Interne Schulungen)',
    category: 'development',
    icon: '🎓',
    description: 'Company-organized training and skill development programs'
  },
  {
    name: 'Mentoring Programs (Mentoring-Programme)',
    category: 'development',
    icon: '👨‍🏫',
    description: 'Structured mentoring and coaching programs'
  },
  {
    name: 'Study Leave (Bildungsurlaub)',
    category: 'development',
    icon: '📖',
    description: 'Paid leave for educational purposes and skill development'
  },

  // Transportation & Mobility
  {
    name: 'Job Ticket (Jobticket)',
    category: 'other',
    icon: '🚊',
    description: 'Subsidized public transportation pass'
  },
  {
    name: 'Company Car (Dienstwagen)',
    category: 'other',
    icon: '🚗',
    description: 'Company vehicle for business and private use'
  },
  {
    name: 'Bike Leasing (Dienstfahrrad)',
    category: 'other',
    icon: '🚲',
    description: 'Leased bicycles or e-bikes for commuting'
  },
  {
    name: 'Parking Allowance (Parkplatz-Zuschuss)',
    category: 'other',
    icon: '🅿️',
    description: 'Subsidized or free parking at workplace'
  },
  {
    name: 'Travel Allowance (Reisekostenzuschuss)',
    category: 'other',
    icon: '✈️',
    description: 'Reimbursement for business travel expenses'
  },

  // Food & Catering
  {
    name: 'Meal Vouchers (Essensgutscheine)',
    category: 'other',
    icon: '🍽️',
    description: 'Vouchers for meals at restaurants or company cafeteria'
  },
  {
    name: 'Company Cafeteria (Betriebskantine)',
    category: 'other',
    icon: '🍱',
    description: 'Subsidized meals in company cafeteria'
  },
  {
    name: 'Free Coffee & Snacks (Kostenlose Verpflegung)',
    category: 'other',
    icon: '☕',
    description: 'Complimentary beverages and snacks at workplace'
  },
  {
    name: 'Catered Meals (Catering-Service)',
    category: 'other',
    icon: '🥗',
    description: 'Regular catered meals or lunch delivery service'
  },

  // Family & Childcare
  {
    name: 'Childcare Support (Kinderbetreuung)',
    category: 'other',
    icon: '👨‍👩‍👧‍👦',
    description: 'Company daycare or childcare subsidies'
  },
  {
    name: 'Family Events (Familienfeste)',
    category: 'other',
    icon: '🎉',
    description: 'Company events and activities for employees and families'
  },
  {
    name: 'Emergency Childcare (Notfall-Kinderbetreuung)',
    category: 'other',
    icon: '🆘',
    description: 'Emergency childcare services for unexpected situations'
  },

  // Technology & Equipment
  {
    name: 'Mobile Phone Allowance (Handy-Zuschuss)',
    category: 'other',
    icon: '📱',
    description: 'Company mobile phone or monthly allowance'
  },
  {
    name: 'Home Office Equipment (Homeoffice-Ausstattung)',
    category: 'other',
    icon: '💻',
    description: 'Equipment and furniture for home office setup'
  },
  {
    name: 'Internet Allowance (Internet-Zuschuss)',
    category: 'other',
    icon: '🌐',
    description: 'Reimbursement for home internet costs'
  }
];

// Validation constants
const VALID_CATEGORIES = ['health', 'time_off', 'financial', 'development', 'wellness', 'work_life', 'other'];

// Logging utility
class Logger {
  constructor(isDryRun = false) {
    this.isDryRun = isDryRun;
    this.stats = {
      processed: 0,
      imported: 0,
      skipped: 0,
      errors: 0
    };
  }

  info(message) {
    console.log(`[INFO] ${message}`);
  }

  warn(message) {
    console.log(`[WARN] ${message}`);
  }

  error(message) {
    console.log(`[ERROR] ${message}`);
  }

  success(message) {
    console.log(`[SUCCESS] ${message}`);
  }

  dryRun(message) {
    if (this.isDryRun) {
      console.log(`[DRY-RUN] ${message}`);
    }
  }

  printStats() {
    console.log('\n=== Import Statistics ===');
    console.log(`Processed: ${this.stats.processed}`);
    console.log(`Imported: ${this.stats.imported}`);
    console.log(`Skipped: ${this.stats.skipped}`);
    console.log(`Errors: ${this.stats.errors}`);
    console.log('========================\n');
  }
}

// Validation function
function validateBenefit(benefit) {
  const errors = [];

  if (!benefit.name || benefit.name.trim().length === 0) {
    errors.push('Benefit name is required');
  }

  if (!benefit.category || !VALID_CATEGORIES.includes(benefit.category)) {
    errors.push(`Category must be one of: ${VALID_CATEGORIES.join(', ')}`);
  }

  if (benefit.name && benefit.name.length > 255) {
    errors.push('Benefit name must be 255 characters or less');
  }

  return errors;
}

// Database operations
async function checkDuplicateBenefit(benefit) {
  const client = await pool.connect();
  try {
    const query = `
      SELECT id, name, category 
      FROM benefits 
      WHERE LOWER(name) = LOWER($1)
    `;
    const result = await client.query(query, [benefit.name]);
    return result.rows.length > 0 ? result.rows[0] : null;
  } finally {
    client.release();
  }
}

async function insertBenefit(benefit, logger) {
  const client = await pool.connect();
  try {
    const query = `
      INSERT INTO benefits (name, category, icon, description)
      VALUES ($1, $2, $3, $4)
      RETURNING id, name
    `;

    const values = [
      benefit.name.trim(),
      benefit.category,
      benefit.icon || null,
      benefit.description || null
    ];

    if (logger.isDryRun) {
      logger.dryRun(`Would insert benefit: ${benefit.name} (${benefit.category})`);
      return { id: 'dry-run-id', name: benefit.name };
    }

    const result = await client.query(query, values);
    return result.rows[0];
  } finally {
    client.release();
  }
}

// Main import function
async function importBenefits(benefits, logger) {
  logger.info(`Starting import of ${benefits.length} German benefits...`);

  for (const benefit of benefits) {
    logger.stats.processed++;

    try {
      // Validate benefit data
      const validationErrors = validateBenefit(benefit);
      if (validationErrors.length > 0) {
        logger.error(`Validation failed for ${benefit.name}: ${validationErrors.join(', ')}`);
        logger.stats.errors++;
        continue;
      }

      // Check for duplicates
      const duplicate = await checkDuplicateBenefit(benefit);
      if (duplicate) {
        logger.warn(`Skipping duplicate benefit: ${benefit.name} (existing: ${duplicate.name})`);
        logger.stats.skipped++;
        continue;
      }

      // Insert benefit
      const result = await insertBenefit(benefit, logger);
      logger.success(`Imported: ${result.name} (ID: ${result.id})`);
      logger.stats.imported++;

    } catch (error) {
      logger.error(`Failed to import ${benefit.name}: ${error.message}`);
      logger.stats.errors++;
    }
  }

  logger.printStats();
}

// CLI argument parsing
function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: false,
    category: null
  };

  for (const arg of args) {
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg.startsWith('--category=')) {
      options.category = arg.split('=')[1];
    } else if (arg === '--help' || arg === '-h') {
      printHelp();
      process.exit(0);
    }
  }

  return options;
}

function printHelp() {
  console.log(`
German Employee Benefits Import Script

Usage:
  node scripts/import-german-benefits.js [options]

Options:
  --dry-run              Preview changes without actually importing
  --category=CATEGORY    Import specific category only
  --help, -h            Show this help message

Categories:
  health                Health & Medical benefits
  financial             Financial benefits (bonuses, pension, etc.)
  time_off              Time off and leave benefits
  wellness              Fitness and wellness programs
  work_life             Work-life balance benefits
  development           Professional development benefits
  other                 Transportation, food, and other benefits

Examples:
  node scripts/import-german-benefits.js
  node scripts/import-german-benefits.js --dry-run
  node scripts/import-german-benefits.js --category=health
  node scripts/import-german-benefits.js --category=wellness --dry-run

German Benefits Overview:
  - Statutory benefits (health insurance, vacation, sick leave)
  - Financial benefits (Christmas bonus, pension schemes)
  - Wellness programs (Urban Sports Club, EGYM Wellpass, Hansefit)
  - Work-life balance (flexible hours, remote work, sabbatical)
  - Professional development (training budget, language courses)
  - Transportation (job ticket, company car, bike leasing)
  - Food & catering (meal vouchers, company cafeteria)
  - Family support (childcare, parental leave)
`);
}

// Main execution
async function main() {
  try {
    const options = parseArguments();
    const logger = new Logger(options.dryRun);

    logger.info('German Employee Benefits Import Script');
    logger.info(`Category filter: ${options.category || 'all'}`);
    logger.info(`Dry run: ${options.dryRun}`);

    if (options.dryRun) {
      logger.info('DRY RUN MODE - No changes will be made to the database');
    }

    // Filter benefits by category if specified
    let benefitsToImport = GERMAN_BENEFITS;
    if (options.category) {
      if (!VALID_CATEGORIES.includes(options.category)) {
        throw new Error(`Invalid category: ${options.category}. Valid categories: ${VALID_CATEGORIES.join(', ')}`);
      }
      benefitsToImport = GERMAN_BENEFITS.filter(benefit => benefit.category === options.category);
      logger.info(`Filtered to ${benefitsToImport.length} benefits in category: ${options.category}`);
    }

    // Import benefits
    await importBenefits(benefitsToImport, logger);

    logger.info('Import completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error(`[FATAL] ${error.message}`);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  importBenefits,
  validateBenefit,
  GERMAN_BENEFITS
};
