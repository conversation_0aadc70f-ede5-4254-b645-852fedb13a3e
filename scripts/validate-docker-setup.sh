#!/bin/bash

# Validation script for Docker Safari testing setup
# This script validates that all Docker components are working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    print_status "Checking Docker..."
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if docker-compose is available
check_docker_compose() {
    print_status "Checking docker-compose..."
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
    print_success "docker-compose is available"
}

# Function to validate Docker files
validate_docker_files() {
    print_status "Validating Docker configuration files..."
    
    local files=(
        "Dockerfile.playwright"
        "docker-compose.playwright.yml"
        "playwright.docker.config.ts"
        "scripts/test-safari.sh"
    )
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Required file missing: $file"
            exit 1
        fi
        print_success "Found: $file"
    done
}

# Function to validate docker-compose configuration
validate_compose_config() {
    print_status "Validating docker-compose configuration..."
    
    if docker-compose -f docker-compose.playwright.yml config > /dev/null 2>&1; then
        print_success "docker-compose configuration is valid"
    else
        print_error "docker-compose configuration is invalid"
        docker-compose -f docker-compose.playwright.yml config
        exit 1
    fi
}

# Function to test building the Playwright image
test_build_image() {
    print_status "Testing Playwright Docker image build..."
    
    if docker-compose -f docker-compose.playwright.yml build playwright > /dev/null 2>&1; then
        print_success "Playwright Docker image built successfully"
    else
        print_error "Failed to build Playwright Docker image"
        exit 1
    fi
}

# Function to test starting services
test_start_services() {
    print_status "Testing service startup..."
    
    # Start services
    docker-compose -f docker-compose.playwright.yml up -d postgres-test app-test > /dev/null 2>&1
    
    # Wait for services to be ready
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for services to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
            print_success "Application is ready"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_error "Application failed to start after $max_attempts attempts"
            docker-compose -f docker-compose.playwright.yml logs app-test
            exit 1
        fi
        
        sleep 2
        ((attempt++))
    done
}

# Function to test Safari test execution
test_safari_execution() {
    print_status "Testing Safari test execution..."
    
    # Run a simple test to verify Safari works
    if docker-compose -f docker-compose.playwright.yml --profile testing run --rm playwright npx playwright --version > /dev/null 2>&1; then
        print_success "Playwright is working in Docker"
    else
        print_error "Playwright is not working in Docker"
        exit 1
    fi
    
    # Test WebKit browser availability
    if docker-compose -f docker-compose.playwright.yml --profile testing run --rm playwright npx playwright install webkit > /dev/null 2>&1; then
        print_success "WebKit browser is available"
    else
        print_warning "WebKit browser installation had issues (this might be normal)"
    fi
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up test infrastructure..."
    docker-compose -f docker-compose.playwright.yml down > /dev/null 2>&1 || true
    print_success "Cleanup completed"
}

# Function to run full validation
run_validation() {
    print_status "🧪 Docker Safari Testing Setup Validation"
    print_status "=========================================="
    
    check_docker
    check_docker_compose
    validate_docker_files
    validate_compose_config
    test_build_image
    test_start_services
    test_safari_execution
    
    print_success "🎉 All validations passed! Docker Safari testing setup is working correctly."
    print_status ""
    print_status "You can now run Safari tests with:"
    print_status "  npm run test:e2e:safari"
    print_status "  ./scripts/test-safari.sh"
    print_status ""
    print_status "For more information, see: docs/SAFARI_DOCKER_TESTING.md"
}

# Trap to ensure cleanup on script exit
trap cleanup EXIT

# Parse command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--help]"
        echo ""
        echo "This script validates the Docker Safari testing setup."
        echo "It checks all components and ensures Safari tests can run in Docker."
        exit 0
        ;;
    *)
        run_validation
        ;;
esac
