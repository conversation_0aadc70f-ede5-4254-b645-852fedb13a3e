#!/bin/bash

# Migration Status Checker
# Compares migrations in filesystem vs applied migrations in database

set -e

echo "🔍 Migration Status Check"
echo "========================"

# Get list of migration files
echo "📁 Scanning migration files..."
MIGRATION_FILES=$(find database/migrations -name "*.sql" -type f | grep -E '^database/migrations/[0-9]' | sort)
FILE_COUNT=$(echo "$MIGRATION_FILES" | wc -l)

echo "Found $FILE_COUNT migration files"

# Get applied migrations from database
echo "🗄️  Checking applied migrations in database..."
APPLIED_MIGRATIONS=$(docker exec benefitlens-postgres psql -U benefitlens_user -d benefitlens -t -c "SELECT migration_name FROM migration_log ORDER BY migration_name;" 2>/dev/null | sed 's/^[[:space:]]*//' | grep -v '^$' || echo "")

if [ -z "$APPLIED_MIGRATIONS" ]; then
    echo "⚠️  No migration_log table found or no migrations applied"
    APPLIED_COUNT=0
else
    APPLIED_COUNT=$(echo "$APPLIED_MIGRATIONS" | wc -l)
    echo "Found $APPLIED_COUNT applied migrations"
fi

echo ""
echo "📊 Migration Analysis"
echo "===================="

# Check each migration file
echo "🔍 Checking migration files vs database..."

MISSING_MIGRATIONS=""
UNAPPLIED_COUNT=0

for migration_file in $MIGRATION_FILES; do
    # Extract migration name from filename
    migration_name=$(basename "$migration_file" .sql)
    
    # Check if this migration is applied
    if echo "$APPLIED_MIGRATIONS" | grep -q "^$migration_name$"; then
        echo "✅ $migration_name"
    else
        echo "❌ $migration_name (NOT APPLIED)"
        MISSING_MIGRATIONS="$MISSING_MIGRATIONS\n  - $migration_name"
        UNAPPLIED_COUNT=$((UNAPPLIED_COUNT + 1))
    fi
done

echo ""
echo "📈 Summary"
echo "=========="
echo "Migration files: $FILE_COUNT"
echo "Applied migrations: $APPLIED_COUNT"
echo "Unapplied migrations: $UNAPPLIED_COUNT"

if [ $UNAPPLIED_COUNT -eq 0 ]; then
    echo ""
    echo "🎉 All migrations are applied!"
    echo "✅ Database is up to date"
else
    echo ""
    echo "⚠️  Unapplied migrations found:"
    echo -e "$MISSING_MIGRATIONS"
    echo ""
    echo "🔧 To apply missing migrations:"
    
    for migration_file in $MIGRATION_FILES; do
        migration_name=$(basename "$migration_file" .sql)
        if ! echo "$APPLIED_MIGRATIONS" | grep -q "^$migration_name$"; then
            echo "   docker exec -i benefitlens-postgres psql -U benefitlens_user -d benefitlens -f /dev/stdin < $migration_file"
        fi
    done
    
    echo ""
    echo "📝 After applying migrations, run:"
    echo "   npm run schema:update"
    echo "   npm run schema:check"
fi

# Check for extra applied migrations (migrations in DB but not in filesystem)
echo ""
echo "🔍 Checking for orphaned migrations..."

ORPHANED_MIGRATIONS=""
ORPHANED_COUNT=0

if [ -n "$APPLIED_MIGRATIONS" ]; then
    for applied_migration in $APPLIED_MIGRATIONS; do
        # Check if file exists
        if [ ! -f "database/migrations/${applied_migration}.sql" ]; then
            echo "⚠️  $applied_migration (file missing)"
            ORPHANED_MIGRATIONS="$ORPHANED_MIGRATIONS\n  - $applied_migration"
            ORPHANED_COUNT=$((ORPHANED_COUNT + 1))
        fi
    done
fi

if [ $ORPHANED_COUNT -eq 0 ]; then
    echo "✅ No orphaned migrations found"
else
    echo "⚠️  Found $ORPHANED_COUNT orphaned migrations:"
    echo -e "$ORPHANED_MIGRATIONS"
    echo "These migrations are applied in the database but the files are missing"
fi

echo ""
echo "🎯 Overall Status:"
if [ $UNAPPLIED_COUNT -eq 0 ] && [ $ORPHANED_COUNT -eq 0 ]; then
    echo "✅ Migration state is clean and up to date"
else
    echo "⚠️  Migration state needs attention"
    if [ $UNAPPLIED_COUNT -gt 0 ]; then
        echo "   - $UNAPPLIED_COUNT migrations need to be applied"
    fi
    if [ $ORPHANED_COUNT -gt 0 ]; then
        echo "   - $ORPHANED_COUNT orphaned migrations found"
    fi
fi
