#!/bin/bash

# Simple Schema Check - Just verify the schemas are functionally identical
# This avoids the complex normalization that was causing false positives

set -e

echo "🔍 Simple Schema Check"
echo "====================="

# Create temp files
TEMP_PROD="/tmp/prod-schema-simple.sql"
TEMP_FILE="/tmp/file-schema-simple.sql"

# Extract production schema in same format as schema.sql
echo "📥 Extracting production schema..."
docker exec benefitlens-postgres pg_dump \
    -U benefitlens_user \
    -d benefitlens \
    --schema-only \
    --no-owner \
    --no-privileges \
    > "$TEMP_PROD.raw"

# Filter out PostgreSQL artifacts and copy files
grep -v -E '^\\(restrict|unrestrict)' "$TEMP_PROD.raw" > "$TEMP_PROD"
cp database/schema.sql "$TEMP_FILE"

echo "🔍 Comparing schemas..."

# Simple diff check
if diff -q "$TEMP_PROD" "$TEMP_FILE" > /dev/null 2>&1; then
    echo "✅ Schemas are identical!"
    rm -f "$TEMP_PROD" "$TEMP_FILE"
    exit 0
fi

echo "⚠️  Schemas have differences"
echo ""

# Check for critical differences
echo "🔍 Checking critical components..."

# Check tables
PROD_TABLES=$(grep "^CREATE TABLE" "$TEMP_PROD" | wc -l)
FILE_TABLES=$(grep "^CREATE TABLE" "$TEMP_FILE" | wc -l)

echo "Tables: Production=$PROD_TABLES, Schema file=$FILE_TABLES"

if [ "$PROD_TABLES" = "$FILE_TABLES" ]; then
    echo "✅ Table count matches"
else
    echo "❌ Table count differs"
fi

# Check auth_logs specifically
if grep -q "CREATE TABLE.*auth_logs" "$TEMP_PROD" && grep -q "CREATE TABLE.*auth_logs" "$TEMP_FILE"; then
    echo "✅ auth_logs table present in both"
elif grep -q "CREATE TABLE.*auth_logs" "$TEMP_PROD"; then
    echo "❌ auth_logs missing from schema file"
elif grep -q "CREATE TABLE.*auth_logs" "$TEMP_FILE"; then
    echo "❌ auth_logs missing from production"
else
    echo "❌ auth_logs missing from both"
fi

# Check activity_log constraint
PROD_CONSTRAINT=$(grep -c "activity_log_event_type_check" "$TEMP_PROD" || echo "0")
FILE_CONSTRAINT=$(grep -c "activity_log_event_type_check" "$TEMP_FILE" || echo "0")

if [ "$PROD_CONSTRAINT" = "1" ] && [ "$FILE_CONSTRAINT" = "1" ]; then
    echo "✅ activity_log constraint present in both"
else
    echo "❌ activity_log constraint issue: prod=$PROD_CONSTRAINT, file=$FILE_CONSTRAINT"
fi

echo ""
echo "📊 Difference Summary:"
echo "====================="

# Show line count differences
PROD_LINES=$(wc -l < "$TEMP_PROD")
FILE_LINES=$(wc -l < "$TEMP_FILE")
echo "Lines: Production=$PROD_LINES, Schema file=$FILE_LINES"

# Show first few differences
echo ""
echo "First 5 differences:"
diff "$TEMP_PROD" "$TEMP_FILE" | head -10

echo ""
echo "💡 Analysis:"
if [ "$PROD_TABLES" = "$FILE_TABLES" ] && [ "$PROD_CONSTRAINT" = "1" ] && [ "$FILE_CONSTRAINT" = "1" ]; then
    echo "✅ Core structure appears correct - differences may be cosmetic"
    echo "✅ Your application should work correctly"
else
    echo "⚠️  Structural differences detected"
    echo "🔧 Consider running: npm run schema:update"
fi

# Cleanup
rm -f "$TEMP_PROD" "$TEMP_PROD.raw" "$TEMP_FILE"
