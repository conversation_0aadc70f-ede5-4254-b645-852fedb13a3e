-- Sc<PERSON>t to apply missing migrations to production
-- Run this on production to sync with dev environment

-- Apply migration 023: Remove benefit events from activity log
-- Drop the existing check constraint
ALTER TABLE activity_log DROP CONSTRAINT IF EXISTS activity_log_event_type_check;

-- Add the updated constraint with company_deleted event type (maintaining alphabetical order)
ALTER TABLE activity_log ADD CONSTRAINT activity_log_event_type_check 
CHECK (event_type IN (
  'benefit_automatically_removed',
  'benefit_disputed',
  'benefit_removal_dispute_approved',
  'benefit_removal_dispute_cancelled',
  'benefit_removal_dispute_rejected',
  'benefit_removal_dispute_submitted',
  'benefit_verified',
  'cache_refresh',
  'company_added',
  'company_deleted',
  'session_cleanup',
  'user_deleted',
  'user_registered'
));

-- Update the comment to reflect all event types including company_deleted
COMMENT ON COLUMN activity_log.event_type IS 'Type of event: benefit_automatically_removed, benefit_disputed, benefit_removal_dispute_approved, benefit_removal_dispute_cancelled, benefit_removal_dispute_rejected, benefit_removal_dispute_submitted, benefit_verified, cache_refresh, company_added, company_deleted, session_cleanup, user_deleted, user_registered';

-- Log this migration
INSERT INTO migration_log (id, migration_name, description) 
VALUES (23, '023-add-company-deleted-event-type', 'Add company_deleted event type to activity log for tracking admin company deletions')
ON CONFLICT (id) DO NOTHING;

-- Apply migration 028: Add industry comment
COMMENT ON COLUMN companies.industry IS 'Standardized industry categories: Aerospace & Defense, Automotive, Banking & Financial Services, Biotechnology & Pharmaceuticals, Chemicals, Construction & Materials, Consumer Goods, E-commerce & Retail, Education, Energy & Utilities, Engineering & Manufacturing, Healthcare & Medical Technology, Insurance, Logistics & Transportation, Media & Entertainment, Mining & Metals, Real Estate, Software & Technology, Telecommunications, Tourism & Hospitality';

-- Apply migration 022: Add auth logs comments
COMMENT ON TABLE auth_logs IS 'Detailed logs of authentication events for security monitoring and admin review';
COMMENT ON COLUMN auth_logs.event_type IS 'Type of authentication event (sign_in_request, sign_up_request, etc.)';
COMMENT ON COLUMN auth_logs.status IS 'Success or failure status of the authentication event';
COMMENT ON COLUMN auth_logs.error_type IS 'Categorized error type for failed events';
COMMENT ON COLUMN auth_logs.failure_reason IS 'Human-readable explanation of why the authentication failed';
COMMENT ON COLUMN auth_logs.token_used IS 'Partial magic link token (first 8 chars) for tracking';
COMMENT ON COLUMN auth_logs.additional_context IS 'Additional context data in JSON format';

-- Log migration 022
INSERT INTO migration_log (migration_name, description) 
VALUES ('022-add-auth-logs-comments', 'Add missing documentation comments to auth_logs table and columns')
ON CONFLICT (migration_name) DO NOTHING;

-- Apply migration 024: Add table descriptions
COMMENT ON TABLE public.benefits IS 'Master list of all available employee benefits that companies can offer';
COMMENT ON TABLE public.company_benefits IS 'Junction table linking companies to the benefits they offer, with verification status tracking';
COMMENT ON TABLE public.benefit_verifications IS 'User-submitted verifications confirming or disputing company benefits';
COMMENT ON TABLE public.benefit_removal_disputes IS 'User disputes against automatic benefit removals, requiring admin review';
COMMENT ON TABLE public.users IS 'Registered users of the BenefitLens platform with company associations and payment status';
COMMENT ON TABLE public.user_sessions IS 'Active user sessions for authentication and session management';
COMMENT ON TABLE public.magic_link_tokens IS 'Temporary tokens for passwordless authentication via magic links';
COMMENT ON TABLE public.magic_link_rate_limits IS 'Rate limiting for magic link requests to prevent abuse';
COMMENT ON TABLE public.company_verification_tokens IS 'Tokens for company email domain verification process';
COMMENT ON TABLE public.search_queries IS 'User search queries for analytics and search optimization';
COMMENT ON TABLE public.company_page_views IS 'Individual page view events for company profile analytics';
COMMENT ON TABLE public.company_analytics_summary IS 'Daily aggregated analytics data per company for performance tracking';
COMMENT ON TABLE public.daily_analytics_summary IS 'Platform-wide daily analytics aggregations for admin dashboard';
COMMENT ON TABLE public.benefit_search_interactions IS 'User interactions with benefits in search results for engagement tracking';
COMMENT ON TABLE public.saved_companies IS 'Companies saved by users for quick access and comparison';
COMMENT ON TABLE public.user_benefit_rankings IS 'User-defined benefit priority rankings for personalized recommendations';
COMMENT ON TABLE public.missing_company_reports IS 'User reports requesting addition of missing companies to the platform';
COMMENT ON TABLE public.company_locations IS 'Geographic locations where companies operate, with normalized city data';
COMMENT ON TABLE public.cache_store IS 'Application-level caching system for improved performance';
COMMENT ON TABLE public.csrf_tokens IS 'CSRF protection tokens for secure form submissions';
COMMENT ON TABLE public.rate_limits IS 'General rate limiting system for API and feature access control';
COMMENT ON TABLE public.session_config IS 'Configuration settings for session management and security';
COMMENT ON TABLE public.migration_log IS 'Database migration tracking for deployment and rollback management';

-- Log migration 024
INSERT INTO migration_log (id, migration_name, description)
VALUES (24, '024-add-table-descriptions', 'Add comprehensive descriptions to all database tables for documentation and maintenance')
ON CONFLICT (id) DO NOTHING;

-- Add missing career_url comment
COMMENT ON COLUMN companies.career_url IS 'URL to company careers/jobs page for Apply Now functionality';

-- Fix constraint syntax to match expected format
-- Fix data_deletion_requests constraint syntax
ALTER TABLE data_deletion_requests DROP CONSTRAINT IF EXISTS data_deletion_requests_status_check;
ALTER TABLE data_deletion_requests ADD CONSTRAINT data_deletion_requests_status_check
CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'processing'::character varying, 'completed'::character varying, 'cancelled'::character varying])::text[])));

-- Fix data_retention_log constraint syntax
ALTER TABLE data_retention_log DROP CONSTRAINT IF EXISTS data_retention_log_status_check;
ALTER TABLE data_retention_log ADD CONSTRAINT data_retention_log_status_check
CHECK (((status)::text = ANY ((ARRAY['running'::character varying, 'completed'::character varying, 'failed'::character varying])::text[])));

-- Fix users_payment_status constraint syntax
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_payment_status_check;
ALTER TABLE users ADD CONSTRAINT users_payment_status_check
CHECK (((payment_status)::text = ANY (ARRAY[('free'::character varying)::text, ('paying'::character varying)::text])));
