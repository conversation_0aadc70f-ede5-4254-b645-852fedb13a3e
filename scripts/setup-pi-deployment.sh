#!/bin/bash

# Raspberry Pi Deployment Setup Script for BenefitLens
# This script sets up your Raspberry Pi for automated deployments

set -e

echo "🍓 Setting up Raspberry Pi for BenefitLens deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on Raspberry Pi
if ! grep -q "Raspberry Pi" /proc/device-tree/model 2>/dev/null; then
    print_warning "This doesn't appear to be a Raspberry Pi. Continuing anyway..."
fi

# Update system
print_status "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install required packages
print_status "Installing required packages..."
sudo apt install -y \
    curl \
    wget \
    git \
    htop \
    vim \
    unzip \
    ca-certificates \
    gnupg \
    lsb-release

# Install Docker
print_status "Installing Docker..."
if ! command -v docker &> /dev/null; then
    sudo apt install -y docker
    print_success "Docker installed successfully"
else
    print_success "Docker is already installed"
fi

# Install Docker Compose
print_status "Installing Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    sudo apt install -y docker-compose
    print_success "Docker Compose installed successfully"
else
    print_success "Docker Compose is already installed"
fi

# Enable Docker service
print_status "Enabling Docker service..."
sudo systemctl enable docker
sudo systemctl start docker

# Create application directory
print_status "Creating application directory..."
APP_DIR="/home/<USER>/benefitlens"
if [ ! -d "$APP_DIR" ]; then
    mkdir -p "$APP_DIR"
    print_success "Created application directory: $APP_DIR"
else
    print_success "Application directory already exists: $APP_DIR"
fi

# Set up GitHub Actions runner directory
print_status "Setting up GitHub Actions runner directory..."
RUNNER_DIR="/home/<USER>/actions-runner"
if [ ! -d "$RUNNER_DIR" ]; then
    mkdir -p "$RUNNER_DIR"
    print_success "Created runner directory: $RUNNER_DIR"
else
    print_success "Runner directory already exists: $RUNNER_DIR"
fi

# Configure Docker daemon for better performance on Pi
print_status "Configuring Docker daemon..."
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF

sudo systemctl restart docker

# # Install Node.js (for potential local builds)
# print_status "Installing Node.js..."
# if ! command -v node &> /dev/null; then
#     curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
#     sudo apt-get install -y nodejs
#     print_success "Node.js installed successfully"
# else
#     print_success "Node.js is already installed"
# fi

# Create systemd service for automatic startup
print_status "Creating systemd service for BenefitLens..."
sudo tee /etc/systemd/system/benefitlens.service > /dev/null <<EOF
[Unit]
Description=BenefitLens Application
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
# WorkingDirectory=/home/<USER>/actions-runner/_work/workwell/workwell
WorkingDirectory=/home/<USER>/benefitlens
ExecStart=/usr/bin/docker-compose -p benefitlens -f docker-compose.prod.yml --env-file .env.prod up -d --remove-orphans
ExecStop=/usr/bin/docker-compose -p benefitlens -f docker-compose.prod.yml --env-file .env.prod down
TimeoutStartSec=0
User=$USER
Group=docker

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable benefitlens.service

print_success "SystemD service created and enabled"

# Set up log rotation
print_status "Setting up log rotation..."
sudo tee /etc/logrotate.d/benefitlens > /dev/null <<EOF
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    size=1M
    missingok
    delaycompress
    copytruncate
}
EOF

print_success "Log rotation configured"

# Create backup script
print_status "Creating backup script..."
tee "$APP_DIR/backup.sh" > /dev/null <<EOF
#!/bin/bash
# BenefitLens Backup Script

BACKUP_DIR="/home/<USER>/backups"
DATE=\$(date +%Y%m%d_%H%M%S)

mkdir -p "\$BACKUP_DIR"

# Backup database
docker exec benefitlens-postgres pg_dump -U benefitlens_user benefitlens > "\$BACKUP_DIR/benefitlens_db_\$DATE.sql"

# Backup environment files
cp .env.prod "\$BACKUP_DIR/env_\$DATE.backup"

# Keep only last 7 days of backups
find "\$BACKUP_DIR" -name "*.sql" -mtime +7 -delete
find "\$BACKUP_DIR" -name "*.backup" -mtime +7 -delete

echo "Backup completed: \$DATE"
EOF

chmod +x "$APP_DIR/backup.sh"

# Add backup to crontab
print_status "Setting up automated backups..."
(crontab -l 2>/dev/null; echo "0 2 * * * $APP_DIR/backup.sh >> $APP_DIR/backup.log 2>&1") | crontab -

print_success "Automated daily backups configured"

print_success "🎉 Raspberry Pi setup completed!"
echo ""
echo "Next steps:"
echo "1. Reboot your Pi: sudo reboot"
echo "2. Clone your repository to $APP_DIR"
echo "3. Set up GitHub Actions self-hosted runner"
echo "4. Configure your environment variables"
echo "5. Run your first deployment"
echo ""
echo "Useful commands:"
echo "- Check Docker status: sudo systemctl status docker"
echo "- Check BenefitLens service: sudo systemctl status benefitlens"
echo "- View application logs: docker-compose -f $APP_DIR/docker-compose.prod.yml logs -f"
echo "- Access Portainer: http://localhost:9000"
echo "- Access Adminer: http://localhost:8080"
echo ""
print_warning "Please reboot your Pi before proceeding with the next steps!"

# Enable persistent journaling
sudo mkdir -p /var/log/journal
sudo sed -i 's/^#\?Storage=.*/Storage=persistent/' /etc/systemd/journald.conf
sudo systemctl restart systemd-journald