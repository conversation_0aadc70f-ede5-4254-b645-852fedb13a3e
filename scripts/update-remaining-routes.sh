#!/bin/bash

# Script to help update remaining API routes to use the new error handling system
# This provides a prioritized list and migration guidance

echo "🔄 API Routes Migration Status Report"
echo "====================================="

echo ""
echo "✅ COMPLETED ROUTES (Using withErrorHandling):"
echo "----------------------------------------------"
grep -r "withErrorHandling" src/app/api/ --include="*.ts" -l | sort | sed 's|src/app/api/||' | sed 's|/route.ts||'

echo ""
echo "🔄 HIGH PRIORITY ROUTES (Need immediate attention):"
echo "---------------------------------------------------"
echo "These routes use requireWebUIOrAdmin and generate verbose logs:"

# Find routes using requireWebUIOrAdmin but not withErrorHandling
for file in $(grep -r "requireWebUIOrAdmin" src/app/api/ --include="*.ts" -l); do
    if ! grep -q "withErrorHandling" "$file"; then
        echo "  - $(echo $file | sed 's|src/app/api/||' | sed 's|/route.ts||')"
    fi
done

echo ""
echo "📊 ADMIN ROUTES (Medium priority):"
echo "----------------------------------"
echo "Admin routes that still use console.error:"

grep -r "console.error" src/app/api/admin/ --include="*.ts" -l | while read file; do
    if ! grep -q "withErrorHandling" "$file"; then
        echo "  - $(echo $file | sed 's|src/app/api/||' | sed 's|/route.ts||')"
    fi
done

echo ""
echo "🔍 AUTH ROUTES (Medium priority):"
echo "---------------------------------"
echo "Authentication routes that still use console.error:"

grep -r "console.error" src/app/api/auth/ --include="*.ts" -l | while read file; do
    if ! grep -q "withErrorHandling" "$file"; then
        echo "  - $(echo $file | sed 's|src/app/api/||' | sed 's|/route.ts||')"
    fi
done

echo ""
echo "📈 ANALYTICS ROUTES (Lower priority):"
echo "-------------------------------------"
echo "Analytics routes that still use console.error:"

grep -r "console.error" src/app/api/analytics/ --include="*.ts" -l | while read file; do
    if ! grep -q "withErrorHandling" "$file"; then
        echo "  - $(echo $file | sed 's|src/app/api/||' | sed 's|/route.ts||')"
    fi
done

echo ""
echo "📊 MIGRATION STATISTICS:"
echo "------------------------"
total_routes=$(find src/app/api -name "route.ts" | wc -l)
updated_routes=$(grep -r "withErrorHandling" src/app/api/ --include="*.ts" -l | wc -l)
console_error_routes=$(grep -r "console.error" src/app/api/ --include="*.ts" -l | wc -l)

echo "  Total API routes: $total_routes"
echo "  Updated routes: $updated_routes"
echo "  Routes with console.error: $console_error_routes"
echo "  Progress: $(( updated_routes * 100 / total_routes ))%"

echo ""
echo "🛠️  MIGRATION TEMPLATE:"
echo "----------------------"
echo "To update a route, follow this pattern:"
echo ""
echo "1. Add import:"
echo "   import { withErrorHandling } from '@/lib/api-error-handler'"
echo ""
echo "2. Replace:"
echo "   export async function GET(request: NextRequest) {"
echo "     try {"
echo "       // ... your code ..."
echo "     } catch (error) {"
echo "       console.error('...', error)"
echo "       return NextResponse.json({ error: '...' }, { status: 500 })"
echo "     }"
echo "   }"
echo ""
echo "3. With:"
echo "   export const GET = withErrorHandling(async (request: NextRequest) => {"
echo "     // ... your code (remove try/catch) ..."
echo "   })"
echo ""
echo "🎯 NEXT RECOMMENDED ACTIONS:"
echo "---------------------------"
echo "1. Update high-priority routes first (those using requireWebUIOrAdmin)"
echo "2. Focus on frequently accessed routes (companies, benefits, search)"
echo "3. Update admin routes for better admin experience"
echo "4. Update auth routes for cleaner authentication logs"
echo "5. Update analytics routes last (lower traffic)"
