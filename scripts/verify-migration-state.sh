#!/bin/bash

# Simple Migration State Verification
# Checks if all migration files in the filesystem are applied in the database

set -e

echo "🔍 Migration State Verification"
echo "==============================="

# Count migration files
MIGRATION_COUNT=$(find database/migrations -name "*.sql" -type f | wc -l)
echo "📁 Found $MIGRATION_COUNT migration files in filesystem"

# List all migration files
echo ""
echo "📋 Migration files present:"
find database/migrations -name "*.sql" -type f | sort | while read -r file; do
    basename "$file" .sql
done

echo ""
echo "🗄️  Checking database migration log..."

# Get applied migrations from database
APPLIED_MIGRATIONS=$(docker exec benefitlens-postgres psql -U benefitlens_user -d benefitlens -t -c "SELECT migration_name FROM migration_log ORDER BY migration_name;" 2>/dev/null | sed 's/^[[:space:]]*//' | grep -v '^$' || echo "")

if [ -z "$APPLIED_MIGRATIONS" ]; then
    echo "❌ No migration_log table found or no migrations applied"
    exit 1
fi

APPLIED_COUNT=$(echo "$APPLIED_MIGRATIONS" | wc -l)
echo "📊 Found $APPLIED_COUNT applied migrations in database"

echo ""
echo "🔍 Verification Results:"
echo "========================"

# Check each migration file against database
MISSING_COUNT=0
EXTRA_COUNT=0

echo "Checking filesystem migrations against database..."
find database/migrations -name "*.sql" -type f | sort | while read -r file; do
    migration_name=$(basename "$file" .sql)
    if echo "$APPLIED_MIGRATIONS" | grep -q "^$migration_name$"; then
        echo "✅ $migration_name"
    else
        echo "❌ $migration_name (NOT APPLIED)"
        MISSING_COUNT=$((MISSING_COUNT + 1))
    fi
done

echo ""
echo "Checking for extra migrations in database..."
echo "$APPLIED_MIGRATIONS" | while read -r applied_migration; do
    if [ -n "$applied_migration" ]; then
        if [ -f "database/migrations/${applied_migration}.sql" ]; then
            echo "✅ $applied_migration"
        else
            echo "⚠️  $applied_migration (file missing)"
            EXTRA_COUNT=$((EXTRA_COUNT + 1))
        fi
    fi
done

echo ""
echo "📈 Summary:"
echo "==========="
echo "Migration files: $MIGRATION_COUNT"
echo "Applied migrations: $APPLIED_COUNT"

if [ "$MIGRATION_COUNT" -eq "$APPLIED_COUNT" ]; then
    echo ""
    echo "🎉 Migration state appears clean!"
    echo "✅ All filesystem migrations are applied"
    echo "✅ No orphaned migrations detected"
    echo "✅ Ready for future migrations"
else
    echo ""
    echo "⚠️  Migration counts differ - manual review recommended"
    echo "📝 Run 'npm run migration:status' for detailed analysis"
fi
