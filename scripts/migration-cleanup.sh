#!/bin/bash

# Migration Cleanup Script
# This script helps clean up the migration state by either removing unapplied migrations
# or applying them all to reach a consistent state

set -e

echo "🧹 Migration Cleanup Tool"
echo "========================="

# Get current migration status
echo "📊 Analyzing current migration state..."

MIGRATION_FILES=$(find database/migrations -name "*.sql" -type f | grep -E '^database/migrations/[0-9]' | sort)
APPLIED_MIGRATIONS=$(docker exec benefitlens-postgres psql -U benefitlens_user -d benefitlens -t -c "SELECT migration_name FROM migration_log ORDER BY migration_name;" 2>/dev/null | sed 's/^[[:space:]]*//' | grep -v '^$' || echo "")

UNAPPLIED_MIGRATIONS=""
UNAPPLIED_COUNT=0

for migration_file in $MIGRATION_FILES; do
    migration_name=$(basename "$migration_file" .sql)
    if ! echo "$APPLIED_MIGRATIONS" | grep -q "^$migration_name$"; then
        UNAPPLIED_MIGRATIONS="$UNAPPLIED_MIGRATIONS\n$migration_file"
        UNAPPLIED_COUNT=$((UNAPPLIED_COUNT + 1))
    fi
done

echo "Found $UNAPPLIED_COUNT unapplied migrations"

if [ $UNAPPLIED_COUNT -eq 0 ]; then
    echo "✅ All migrations are already applied!"
    exit 0
fi

echo ""
echo "🤔 What would you like to do?"
echo "=============================="
echo "1. 🗑️  Remove unapplied migration files (Clean Slate - Recommended)"
echo "2. ⚡ Apply all unapplied migrations (Risky - may cause conflicts)"
echo "3. 📋 Show detailed analysis first"
echo "4. ❌ Cancel"
echo ""

read -p "Choose an option (1-4): " -n 1 -r
echo

case $REPLY in
    1)
        echo ""
        echo "🗑️  Clean Slate Approach"
        echo "========================"
        echo "This will:"
        echo "✅ Move unapplied migration files to a backup directory"
        echo "✅ Keep your database in its current working state"
        echo "✅ Prevent future confusion about migration state"
        echo "⚠️  You won't be able to apply these migrations later"
        echo ""
        
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # Create backup directory
            BACKUP_DIR="database/migrations-backup-$(date +%Y%m%d_%H%M%S)"
            mkdir -p "$BACKUP_DIR"
            
            echo "📦 Moving unapplied migrations to $BACKUP_DIR..."
            
            # Move unapplied migrations
            echo -e "$UNAPPLIED_MIGRATIONS" | while read -r migration_file; do
                if [ -n "$migration_file" ]; then
                    echo "  Moving $(basename "$migration_file")"
                    mv "$migration_file" "$BACKUP_DIR/"
                fi
            done
            
            echo ""
            echo "✅ Clean slate completed!"
            echo "📦 Backup created at: $BACKUP_DIR"
            echo "🎯 Your migration state is now clean"
            
            # Show final status
            echo ""
            echo "📊 Final migration status:"
            ./scripts/migration-status.sh
        else
            echo "❌ Operation cancelled"
        fi
        ;;
        
    2)
        echo ""
        echo "⚡ Apply All Migrations"
        echo "======================"
        echo "This will:"
        echo "⚠️  Attempt to apply all $UNAPPLIED_COUNT unapplied migrations"
        echo "⚠️  May cause conflicts if tables/columns already exist"
        echo "⚠️  Could potentially break your working database"
        echo "✅ Bring migration log into sync with filesystem"
        echo ""
        
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "🚀 Applying migrations..."
            
            # Apply each migration
            echo -e "$UNAPPLIED_MIGRATIONS" | while read -r migration_file; do
                if [ -n "$migration_file" ]; then
                    echo "📝 Applying $(basename "$migration_file")..."
                    if docker exec -i benefitlens-postgres psql -U benefitlens_user -d benefitlens -f /dev/stdin < "$migration_file"; then
                        echo "  ✅ Success"
                    else
                        echo "  ❌ Failed - stopping here"
                        exit 1
                    fi
                fi
            done
            
            echo ""
            echo "✅ All migrations applied!"
            echo "📝 Updating schema.sql..."
            npm run schema:update
            
            echo ""
            echo "📊 Final migration status:"
            ./scripts/migration-status.sh
        else
            echo "❌ Operation cancelled"
        fi
        ;;
        
    3)
        echo ""
        echo "📋 Detailed Analysis"
        echo "==================="
        echo "Unapplied migrations:"
        echo -e "$UNAPPLIED_MIGRATIONS" | while read -r migration_file; do
            if [ -n "$migration_file" ]; then
                echo ""
                echo "📄 $(basename "$migration_file"):"
                echo "   Path: $migration_file"
                echo "   Description:"
                head -5 "$migration_file" | grep -E '^--' | sed 's/^--/   /'
            fi
        done
        
        echo ""
        echo "💡 Recommendation:"
        echo "Since your database is working and has all the functionality,"
        echo "the Clean Slate approach (option 1) is recommended."
        ;;
        
    4)
        echo "❌ Operation cancelled"
        ;;
        
    *)
        echo "❌ Invalid option"
        ;;
esac
