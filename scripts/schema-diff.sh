#!/bin/bash

# Schema Diff Tool for BenefitLens
# Compares database/schema.sql with actual production database schema

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCHEMA_FILE="database/schema.sql"
TEMP_DIR="/tmp/benefitlens-schema-diff"
PROD_SCHEMA="$TEMP_DIR/production-schema.sql"
FILE_SCHEMA="$TEMP_DIR/file-schema.sql"

echo -e "${BLUE}🔍 BenefitLens Schema Comparison Tool${NC}"
echo "=================================================="

# Create temp directory
mkdir -p "$TEMP_DIR"

# Function to clean up temp files
cleanup() {
    rm -rf "$TEMP_DIR"
}
trap cleanup EXIT

echo -e "${YELLOW}📥 Extracting production database schema...${NC}"
# Extract production schema (same format as schema.sql file)
docker exec benefitlens-postgres pg_dump \
    -U benefitlens_user \
    -d benefitlens \
    --schema-only \
    --no-owner \
    --no-privileges \
    > "$PROD_SCHEMA.raw"

# Filter out PostgreSQL connection strings and other artifacts
grep -v -E '^\\(restrict|unrestrict)' "$PROD_SCHEMA.raw" > "$PROD_SCHEMA"

echo -e "${YELLOW}📄 Preparing file schema for comparison...${NC}"
# Copy the file schema
cp "$SCHEMA_FILE" "$FILE_SCHEMA"

# Normalize both files for better comparison
normalize_schema() {
    local file="$1"
    local normalized="${file}.normalized"

    # Remove comments, empty lines, and normalize whitespace, but preserve structure
    sed '/^--/d; /^$/d; s/[[:space:]]\+/ /g' "$file" > "$normalized"

    # Sort only the CREATE statements for a separate comparison
    grep -E "^CREATE (TABLE|INDEX|SEQUENCE|FUNCTION)" "$normalized" | sort > "${file}.creates"
}

echo -e "${YELLOW}🔧 Normalizing schemas for comparison...${NC}"
normalize_schema "$PROD_SCHEMA"
normalize_schema "$FILE_SCHEMA"

echo -e "${BLUE}📊 Schema Comparison Results${NC}"
echo "=================================================="

# Check if normalized schemas are identical
if diff -q "${PROD_SCHEMA}.normalized" "${FILE_SCHEMA}.normalized" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Schemas are identical!${NC}"
    exit 0
else
    echo -e "${RED}❌ Schemas differ!${NC}"
    echo ""

    # Show a sample of the differences
    echo -e "${YELLOW}Sample differences (first 10 lines):${NC}"
    diff "${PROD_SCHEMA}.normalized" "${FILE_SCHEMA}.normalized" | head -10
    echo ""
fi

# Show detailed differences
echo -e "${YELLOW}📋 Detailed Differences:${NC}"
echo "=================================================="

# Tables comparison
echo -e "${BLUE}🗂️  Table Differences:${NC}"
grep "^CREATE TABLE" "${PROD_SCHEMA}.normalized" | awk '{print $3}' | sort > "$TEMP_DIR/prod_tables.txt"
grep "^CREATE TABLE" "${FILE_SCHEMA}.normalized" | awk '{print $3}' | sort > "$TEMP_DIR/file_tables.txt"

# Tables only in production
PROD_ONLY=$(comm -23 "$TEMP_DIR/prod_tables.txt" "$TEMP_DIR/file_tables.txt")
if [ -n "$PROD_ONLY" ]; then
    echo -e "${RED}  Tables only in production:${NC}"
    echo "$PROD_ONLY" | sed 's/^/    /'
fi

# Tables only in schema file
FILE_ONLY=$(comm -13 "$TEMP_DIR/prod_tables.txt" "$TEMP_DIR/file_tables.txt")
if [ -n "$FILE_ONLY" ]; then
    echo -e "${RED}  Tables only in schema file:${NC}"
    echo "$FILE_ONLY" | sed 's/^/    /'
fi

# Indexes comparison
echo ""
echo -e "${BLUE}📇 Index Differences:${NC}"
grep "^CREATE.*INDEX" "${PROD_SCHEMA}.normalized" | awk '{print $3}' | sort > "$TEMP_DIR/prod_indexes.txt" 2>/dev/null || touch "$TEMP_DIR/prod_indexes.txt"
grep "^CREATE.*INDEX" "${FILE_SCHEMA}.normalized" | awk '{print $3}' | sort > "$TEMP_DIR/file_indexes.txt" 2>/dev/null || touch "$TEMP_DIR/file_indexes.txt"

PROD_IDX_ONLY=$(comm -23 "$TEMP_DIR/prod_indexes.txt" "$TEMP_DIR/file_indexes.txt")
if [ -n "$PROD_IDX_ONLY" ]; then
    echo -e "${RED}  Indexes only in production:${NC}"
    echo "$PROD_IDX_ONLY" | sed 's/^/    /'
fi

FILE_IDX_ONLY=$(comm -13 "$TEMP_DIR/prod_indexes.txt" "$TEMP_DIR/file_indexes.txt")
if [ -n "$FILE_IDX_ONLY" ]; then
    echo -e "${RED}  Indexes only in schema file:${NC}"
    echo "$FILE_IDX_ONLY" | sed 's/^/    /'
fi

# Show specific table structure differences
echo ""
echo -e "${BLUE}🔍 Detailed Table Structure Differences:${NC}"
echo "=================================================="

# Check for specific known issues
echo -e "${YELLOW}Checking for known issues:${NC}"

# Check auth_logs table
if grep -q "auth_logs" "${PROD_SCHEMA}.normalized" && ! grep -q "auth_logs" "${FILE_SCHEMA}.normalized"; then
    echo -e "${RED}  ❌ auth_logs table missing from schema file${NC}"
elif ! grep -q "auth_logs" "${PROD_SCHEMA}.normalized" && grep -q "auth_logs" "${FILE_SCHEMA}.normalized"; then
    echo -e "${RED}  ❌ auth_logs table missing from production${NC}"
else
    echo -e "${GREEN}  ✅ auth_logs table status consistent${NC}"
fi

# Check activity_log constraint
PROD_ACTIVITY_CONSTRAINT=$(grep "activity_log_event_type_check.*CHECK" "${PROD_SCHEMA}.normalized" || echo "")
FILE_ACTIVITY_CONSTRAINT=$(grep "activity_log_event_type_check.*CHECK" "${FILE_SCHEMA}.normalized" || echo "")

if [ "$PROD_ACTIVITY_CONSTRAINT" != "$FILE_ACTIVITY_CONSTRAINT" ]; then
    echo -e "${RED}  ❌ activity_log event_type constraint differs${NC}"
    echo "    Production constraint event types:"
    echo "$PROD_ACTIVITY_CONSTRAINT" | grep -o "'[^']*'" | sort | sed 's/^/      /'
    echo "    Schema file constraint event types:"
    echo "$FILE_ACTIVITY_CONSTRAINT" | grep -o "'[^']*'" | sort | sed 's/^/      /'
else
    echo -e "${GREEN}  ✅ activity_log constraint consistent${NC}"
fi

echo ""
echo -e "${BLUE}💡 Recommendations:${NC}"
echo "=================================================="
echo "1. Update schema.sql from production:"
echo "   docker exec benefitlens-postgres pg_dump -U benefitlens_user -d benefitlens --schema-only --no-owner --no-privileges > database/schema.sql"
echo ""
echo "2. Add this script to your CI/CD pipeline to catch schema drift"
echo ""
echo "3. Consider using a migration tool that automatically updates schema.sql"

# Offer to update schema.sql
echo ""
read -p "Would you like to update database/schema.sql from production now? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}📝 Updating schema.sql from production...${NC}"
    cp "$SCHEMA_FILE" "${SCHEMA_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    docker exec benefitlens-postgres pg_dump \
        -U benefitlens_user \
        -d benefitlens \
        --schema-only \
        --no-owner \
        --no-privileges \
        > "$SCHEMA_FILE"
    echo -e "${GREEN}✅ schema.sql updated! Backup saved as ${SCHEMA_FILE}.backup.$(date +%Y%m%d_%H%M%S)${NC}"
fi
