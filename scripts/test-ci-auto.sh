#!/bin/bash

# Automated CI testing script (non-interactive)
# Tests the integration tests specifically to verify the auth fix

set -e

echo "🚀 Automated CI Testing - Integration Tests"
echo "==========================================="

# Check if act is installed
if ! command -v act &> /dev/null; then
    echo "❌ 'act' is not installed. Installing it now..."
    
    # Install act based on the operating system
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        curl -s https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install act
        else
            echo "❌ Please install Homebrew first, then run: brew install act"
            exit 1
        fi
    else
        echo "❌ Unsupported OS. Please install 'act' manually: https://github.com/nektos/act"
        exit 1
    fi
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Create .actrc file for act configuration if it doesn't exist
if [ ! -f .actrc ]; then
    echo "📝 Creating .actrc configuration file..."
    cat > .actrc << EOF
# Use medium-sized runner image for better compatibility
-P ubuntu-latest=catthehacker/ubuntu:act-latest

# Set default secrets file
--secret-file .env.local.secrets

# Enable verbose logging
--verbose
EOF
fi

# Create secrets file for local testing if it doesn't exist
if [ ! -f .env.local.secrets ]; then
    echo "📝 Creating .env.local.secrets file..."
    cat > .env.local.secrets << EOF
# Local secrets for GitHub Actions testing
TEST_DATABASE_URL=postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
NEXTAUTH_SECRET=local-test-secret-for-ci-testing
NEXTAUTH_URL=http://localhost:3000
SESSION_SECRET=local-session-secret-for-ci-testing
LHCI_GITHUB_APP_TOKEN=dummy-token-for-local-testing
EOF
fi

echo "🧪 Running Integration Tests with act..."
echo "This will test the specific admin authentication issue we fixed"

# Run only the integration tests job
act -W ".github/workflows/test-pipeline.yml" -j "integration-tests"

echo ""
echo "✅ Automated CI testing completed!"
echo ""
echo "💡 If the integration tests passed, your authentication fix is working!"
echo "🚀 You can now push your changes with confidence."
