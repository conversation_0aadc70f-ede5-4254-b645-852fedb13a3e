// Comprehensive test for the impersonation feature
const BASE_URL = 'http://localhost:3001';

async function testCompleteImpersonationFlow() {
  console.log('🧪 Testing Complete Impersonation Flow...\n');

  try {
    // Step 1: Test starting impersonation
    console.log('1️⃣ Testing impersonation start...');
    const targetUserId = '094cb558-f2e3-4aed-859e-a9e9a8549c63'; // <EMAIL>
    
    const impersonateResponse = await fetch(`${BASE_URL}/api/admin/impersonate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'session_token=test-admin-session-token'
      },
      body: JSON.stringify({ userId: targetUserId })
    });

    console.log(`   Impersonate Status: ${impersonateResponse.status}`);
    
    if (impersonateResponse.status !== 200) {
      const errorText = await impersonateResponse.text();
      console.log(`   Error: ${errorText}`);
      return;
    }

    const impersonateData = await impersonateResponse.json();
    console.log(`   ✅ Impersonation started: ${impersonateData.message}`);
    
    // Extract the new session token from Set-Cookie header
    const setCookieHeader = impersonateResponse.headers.get('set-cookie');
    let newSessionToken = null;
    if (setCookieHeader) {
      const match = setCookieHeader.match(/session_token=([^;]+)/);
      if (match) {
        newSessionToken = match[1];
        console.log(`   📝 New session token: ${newSessionToken.substring(0, 20)}...`);
      }
    }

    if (!newSessionToken) {
      console.log('   ❌ No new session token found in response');
      return;
    }

    // Step 2: Test /api/auth/me with impersonation session
    console.log('\n2️⃣ Testing /api/auth/me with impersonation session...');
    const meResponse = await fetch(`${BASE_URL}/api/auth/me`, {
      headers: {
        'Cookie': `session_token=${newSessionToken}`
      }
    });

    console.log(`   Me Status: ${meResponse.status}`);
    
    if (meResponse.status === 200) {
      const meData = await meResponse.json();
      console.log(`   ✅ Current user: ${meData.email} (ID: ${meData.id})`);
      console.log(`   ✅ Is impersonation: ${meData.isImpersonation}`);
      console.log(`   ✅ Original admin: ${meData.originalAdminEmail}`);
    } else {
      const errorText = await meResponse.text();
      console.log(`   ❌ Error: ${errorText}`);
    }

    // Step 3: Test exit impersonation
    console.log('\n3️⃣ Testing exit impersonation...');
    const exitResponse = await fetch(`${BASE_URL}/api/admin/exit-impersonation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session_token=${newSessionToken}`
      }
    });

    console.log(`   Exit Status: ${exitResponse.status}`);
    
    if (exitResponse.status === 200) {
      const exitData = await exitResponse.json();
      console.log(`   ✅ Exit successful: ${exitData.message}`);
      
      // Extract the admin session token
      const exitSetCookieHeader = exitResponse.headers.get('set-cookie');
      let adminSessionToken = null;
      if (exitSetCookieHeader) {
        const match = exitSetCookieHeader.match(/session_token=([^;]+)/);
        if (match) {
          adminSessionToken = match[1];
          console.log(`   📝 Admin session token: ${adminSessionToken.substring(0, 20)}...`);
        }
      }

      // Step 4: Verify we're back to admin session
      console.log('\n4️⃣ Testing /api/auth/me with admin session...');
      const adminMeResponse = await fetch(`${BASE_URL}/api/auth/me`, {
        headers: {
          'Cookie': `session_token=${adminSessionToken}`
        }
      });

      console.log(`   Admin Me Status: ${adminMeResponse.status}`);
      
      if (adminMeResponse.status === 200) {
        const adminMeData = await adminMeResponse.json();
        console.log(`   ✅ Back to admin: ${adminMeData.email} (Role: ${adminMeData.role})`);
        console.log(`   ✅ Is impersonation: ${adminMeData.isImpersonation || false}`);
      } else {
        const errorText = await adminMeResponse.text();
        console.log(`   ❌ Error: ${errorText}`);
      }
    } else {
      const errorText = await exitResponse.text();
      console.log(`   ❌ Exit error: ${errorText}`);
    }

    console.log('\n🎉 Impersonation flow test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testCompleteImpersonationFlow();
