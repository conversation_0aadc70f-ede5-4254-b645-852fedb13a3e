#!/bin/bash

# Simple integration test runner that mimics CI environment
# This is a lightweight alternative to full GitHub Actions simulation

set -e

echo "🧪 Simple Integration Test Runner"
echo "================================="
echo "This tests the admin authentication fix without requiring 'act'"

# Set CI-like environment variables
export NODE_ENV=test
export DATABASE_URL="postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens"
export CACHE_TYPE="postgresql"
export SESSION_SECRET="test-session-secret-for-integration-tests"
export USE_LOCAL_AUTH="true"
export NEXT_PUBLIC_APP_URL="http://localhost:3000"
export APP_URL="http://localhost:3000"
export LOG_LEVEL="warn"

echo "🔧 Environment configured for CI simulation"

# Check if we have a running PostgreSQL instance
echo "🔍 Checking for PostgreSQL..."

# Try to connect to existing PostgreSQL
if PGPASSWORD=benefitlens_password pg_isready -h localhost -p 5432 -U benefitlens_user -d benefitlens 2>/dev/null; then
    echo "✅ Found existing PostgreSQL instance"
    USE_EXISTING_DB=true
else
    echo "⚠️  No existing PostgreSQL found"
    
    # Check if Docker is available
    if command -v docker &> /dev/null && docker info &> /dev/null; then
        echo "🐳 Starting PostgreSQL with Docker..."
        
        # Stop existing container if running
        docker stop benefitlens-test-postgres 2>/dev/null || true
        docker rm benefitlens-test-postgres 2>/dev/null || true
        
        # Start fresh PostgreSQL container
        docker run -d \
            --name benefitlens-test-postgres \
            -e POSTGRES_DB=benefitlens \
            -e POSTGRES_USER=benefitlens_user \
            -e POSTGRES_PASSWORD=benefitlens_password \
            -p 5432:5432 \
            postgres:15
        
        echo "⏳ Waiting for PostgreSQL to be ready..."
        for i in {1..30}; do
            if PGPASSWORD=benefitlens_password pg_isready -h localhost -p 5432 -U benefitlens_user -d benefitlens 2>/dev/null; then
                echo "✅ PostgreSQL is ready"
                USE_EXISTING_DB=false
                break
            fi
            echo "   Waiting... ($i/30)"
            sleep 2
        done
        
        if [ $i -eq 30 ]; then
            echo "❌ PostgreSQL failed to start"
            exit 1
        fi
    else
        echo "❌ No PostgreSQL available and Docker not found"
        echo "Please either:"
        echo "1. Start PostgreSQL manually, or"
        echo "2. Install Docker, or"
        echo "3. Use the development database: npm run dev:start"
        exit 1
    fi
fi

# Initialize database if we started a new one
if [ "$USE_EXISTING_DB" = "false" ]; then
    echo "🗄️  Initializing database..."
    
    export PGPASSWORD=benefitlens_password
    
    # Initialize schema
    if [ -f "database/init/01-init.sql" ]; then
        psql -h localhost -U benefitlens_user -d benefitlens -f database/init/01-init.sql
        echo "✅ Database schema initialized"
    fi
    
    # Seed test data
    if [ -f "database/seed.sql" ]; then
        psql -h localhost -U benefitlens_user -d benefitlens -f database/seed.sql
        echo "✅ Database seeded"
    fi
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm ci
fi

# Build the application
echo "🔨 Building application..."
npm run build

# Run integration tests
echo "🚀 Running integration tests..."
echo "This will test the admin authentication fix specifically"

npm run test:integration -- --run --reporter=verbose

echo ""
echo "🎉 Integration tests completed!"

# Cleanup if we started our own PostgreSQL
if [ "$USE_EXISTING_DB" = "false" ]; then
    echo "🧹 Cleaning up test database..."
    docker stop benefitlens-test-postgres 2>/dev/null || true
    docker rm benefitlens-test-postgres 2>/dev/null || true
fi

echo ""
echo "✅ Simple integration test completed successfully!"
echo "🚀 Your authentication fix should now work in CI!"
