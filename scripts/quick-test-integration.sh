#!/bin/bash

# Quick script to test integration tests locally before pushing
# This mimics the CI environment as closely as possible

set -e

echo "🧪 Quick Integration Test (Local CI Simulation)"
echo "=============================================="

# Check if required tools are available
check_requirements() {
    echo "🔍 Checking requirements..."
    
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker is required but not installed"
        exit 1
    fi
    
    if ! command -v psql &> /dev/null; then
        echo "❌ PostgreSQL client is required but not installed"
        echo "   Install with: sudo apt-get install postgresql-client (Ubuntu/Debian)"
        echo "   or: brew install postgresql (macOS)"
        exit 1
    fi
    
    echo "✅ Requirements check passed"
}

# Start PostgreSQL container for testing
start_postgres() {
    echo "🐘 Starting PostgreSQL container..."
    
    # Stop existing container if running
    docker stop benefitlens-test-postgres 2>/dev/null || true
    docker rm benefitlens-test-postgres 2>/dev/null || true
    
    # Start fresh PostgreSQL container
    docker run -d \
        --name benefitlens-test-postgres \
        -e POSTGRES_DB=benefitlens \
        -e POSTGRES_USER=benefitlens_user \
        -e POSTGRES_PASSWORD=benefitlens_password \
        -p 5432:5432 \
        postgres:15
    
    echo "⏳ Waiting for PostgreSQL to be ready..."
    for i in {1..30}; do
        if PGPASSWORD=benefitlens_password pg_isready -h localhost -p 5432 -U benefitlens_user -d benefitlens 2>/dev/null; then
            echo "✅ PostgreSQL is ready"
            break
        fi
        echo "   Waiting... ($i/30)"
        sleep 2
    done
    
    if [ $i -eq 30 ]; then
        echo "❌ PostgreSQL failed to start"
        exit 1
    fi
}

# Initialize database
init_database() {
    echo "🗄️  Initializing database..."
    
    export PGPASSWORD=benefitlens_password
    
    # Initialize schema
    if [ -f "database/init/01-init.sql" ]; then
        psql -h localhost -U benefitlens_user -d benefitlens -f database/init/01-init.sql
        echo "✅ Database schema initialized"
    else
        echo "❌ Database init file not found: database/init/01-init.sql"
        exit 1
    fi
    
    # Seed test data
    if [ -f "database/seed.sql" ]; then
        psql -h localhost -U benefitlens_user -d benefitlens -f database/seed.sql
        echo "✅ Database seeded"
    else
        echo "⚠️  Seed file not found: database/seed.sql (continuing without seed data)"
    fi
}

# Set environment variables for testing
setup_env() {
    echo "🔧 Setting up environment..."
    
    export DATABASE_URL="postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens"
    export CACHE_TYPE="postgresql"
    export SESSION_SECRET="test-session-secret-for-local-integration-tests"
    export USE_LOCAL_AUTH="true"
    export NEXT_PUBLIC_APP_URL="http://localhost:3000"
    export APP_URL="http://localhost:3000"
    export LOG_LEVEL="warn"
    export NODE_ENV="test"
    
    echo "✅ Environment configured"
}

# Run the tests
run_tests() {
    echo "🧪 Running integration tests..."
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing dependencies..."
        npm ci
    fi
    
    # Build the application
    echo "🔨 Building application..."
    npm run build
    
    # Run integration tests
    echo "🚀 Running integration tests..."
    npm run test:integration -- --run --reporter=verbose
}

# Cleanup
cleanup() {
    echo "🧹 Cleaning up..."
    docker stop benefitlens-test-postgres 2>/dev/null || true
    docker rm benefitlens-test-postgres 2>/dev/null || true
    echo "✅ Cleanup completed"
}

# Trap cleanup on exit
trap cleanup EXIT

# Main execution
main() {
    echo "Starting local integration test simulation..."
    echo "This mimics the GitHub Actions CI environment"
    echo ""
    
    check_requirements
    start_postgres
    init_database
    setup_env
    run_tests
    
    echo ""
    echo "🎉 Integration tests completed successfully!"
    echo "Your changes should work in CI now."
}

# Run main function
main "$@"
