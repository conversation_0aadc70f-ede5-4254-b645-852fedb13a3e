#!/usr/bin/env node

// Check for expired data that should be cleaned up
// This helps verify if cleanup is working properly

import { config } from 'dotenv'
import { query } from '../src/lib/local-db.js'

// Load environment variables
config({ path: '.env.local' })

async function checkExpiredData() {
  console.log('🔍 Checking for expired data in database...\n')

  try {
    // Check expired sessions
    const expiredSessions = await query(`
      SELECT COUNT(*) as count, MIN(expires_at) as oldest, MAX(expires_at) as newest
      FROM user_sessions 
      WHERE expires_at < NOW()
    `)
    
    console.log('📅 Expired Sessions:')
    console.log(`   Count: ${expiredSessions.rows[0]?.count || 0}`)
    if (expiredSessions.rows[0]?.oldest) {
      console.log(`   Oldest: ${expiredSessions.rows[0].oldest}`)
      console.log(`   Newest: ${expiredSessions.rows[0].newest}`)
    }

    // Check expired cache entries
    const expiredCache = await query(`
      SELECT COUNT(*) as count, MI<PERSON>(expires_at) as oldest, <PERSON><PERSON>(expires_at) as newest
      FROM cache_store 
      WHERE expires_at < NOW()
    `)
    
    console.log('\n💾 Expired Cache Entries:')
    console.log(`   Count: ${expiredCache.rows[0]?.count || 0}`)
    if (expiredCache.rows[0]?.oldest) {
      console.log(`   Oldest: ${expiredCache.rows[0].oldest}`)
      console.log(`   Newest: ${expiredCache.rows[0].newest}`)
    }

    // Check expired CSRF tokens
    const expiredCSRF = await query(`
      SELECT COUNT(*) as count, MIN(expires_at) as oldest, MAX(expires_at) as newest
      FROM csrf_tokens 
      WHERE expires_at < NOW()
    `)
    
    console.log('\n🔐 Expired CSRF Tokens:')
    console.log(`   Count: ${expiredCSRF.rows[0]?.count || 0}`)
    if (expiredCSRF.rows[0]?.oldest) {
      console.log(`   Oldest: ${expiredCSRF.rows[0].oldest}`)
      console.log(`   Newest: ${expiredCSRF.rows[0].newest}`)
    }

    // Check expired rate limits
    const expiredRateLimits = await query(`
      SELECT COUNT(*) as count, MIN(expires_at) as oldest, MAX(expires_at) as newest
      FROM rate_limits 
      WHERE expires_at < NOW()
    `)
    
    console.log('\n⚡ Expired Rate Limits:')
    console.log(`   Count: ${expiredRateLimits.rows[0]?.count || 0}`)
    if (expiredRateLimits.rows[0]?.oldest) {
      console.log(`   Oldest: ${expiredRateLimits.rows[0].oldest}`)
      console.log(`   Newest: ${expiredRateLimits.rows[0].newest}`)
    }

    // Check total expired items
    const totalExpired = (
      parseInt(expiredSessions.rows[0]?.count || 0) +
      parseInt(expiredCache.rows[0]?.count || 0) +
      parseInt(expiredCSRF.rows[0]?.count || 0) +
      parseInt(expiredRateLimits.rows[0]?.count || 0)
    )

    console.log('\n📊 Summary:')
    console.log(`   Total expired items: ${totalExpired}`)
    
    if (totalExpired > 0) {
      console.log('\n💡 To clean up expired data, run:')
      console.log('   npm run cleanup:all')
      console.log('   or use the admin API: POST /api/admin/cleanup')
    } else {
      console.log('\n✅ No expired data found - cleanup is working properly!')
    }

  } catch (error) {
    console.error('❌ Error checking expired data:', error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⚠️  Check interrupted')
  process.exit(1)
})

process.on('SIGTERM', () => {
  console.log('\n⚠️  Check terminated')
  process.exit(1)
})

checkExpiredData()
  .then(() => {
    console.log('\n🎉 Check completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Check failed:', error)
    process.exit(1)
  })
