#!/bin/bash

# Script to test GitHub Actions workflows locally using act
# This allows testing CI without pushing to GitHub

set -e

echo "🚀 Setting up local CI testing..."

# Check if act is installed
if ! command -v act &> /dev/null; then
    echo "❌ 'act' is not installed. Installing it now..."
    
    # Install act based on the operating system
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        curl https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install act
        else
            echo "❌ Please install Homebrew first, then run: brew install act"
            exit 1
        fi
    else
        echo "❌ Unsupported OS. Please install 'act' manually: https://github.com/nektos/act"
        exit 1
    fi
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Create .actrc file for act configuration if it doesn't exist
if [ ! -f .actrc ]; then
    echo "📝 Creating .actrc configuration file..."
    cat > .actrc << EOF
# Use medium-sized runner image for better compatibility
-P ubuntu-latest=catthehacker/ubuntu:act-latest
-P ubuntu-20.04=catthehacker/ubuntu:act-20.04
-P ubuntu-18.04=catthehacker/ubuntu:act-18.04

# Set default secrets file
--secret-file .env.local.secrets

# Enable verbose logging
--verbose
EOF
fi

# Create secrets file for local testing if it doesn't exist
if [ ! -f .env.local.secrets ]; then
    echo "📝 Creating .env.local.secrets file..."
    cat > .env.local.secrets << EOF
# Local secrets for GitHub Actions testing
# These are used only for local CI testing with 'act'

TEST_DATABASE_URL=postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
NEXTAUTH_SECRET=local-test-secret-for-ci-testing
NEXTAUTH_URL=http://localhost:3000
SESSION_SECRET=local-session-secret-for-ci-testing
LHCI_GITHUB_APP_TOKEN=dummy-token-for-local-testing

# Note: These are dummy values for local testing only
# Real secrets are stored in GitHub repository secrets
EOF
    echo "⚠️  Please review and update .env.local.secrets with appropriate values"
fi

# Function to run specific workflow
run_workflow() {
    local workflow_file=$1
    local job_name=${2:-""}
    
    echo "🧪 Running workflow: $workflow_file"
    
    if [ -n "$job_name" ]; then
        echo "🎯 Running specific job: $job_name"
        act -W "$workflow_file" -j "$job_name"
    else
        echo "🎯 Running all jobs in workflow"
        act -W "$workflow_file"
    fi
}

# Main menu
echo ""
echo "🎯 What would you like to test?"
echo "1. Test Pipeline (integration tests)"
echo "2. Full CI/CD Pipeline"
echo "3. Unit Tests Only"
echo "4. Integration Tests Only"
echo "5. E2E Tests Only"
echo "6. Security Tests Only"
echo "7. Custom job"
echo ""

read -p "Enter your choice (1-7): " choice

case $choice in
    1)
        echo "🧪 Running Test Pipeline..."
        run_workflow ".github/workflows/test-pipeline.yml"
        ;;
    2)
        echo "🧪 Running Full CI/CD Pipeline..."
        run_workflow ".github/workflows/ci.yml"
        ;;
    3)
        echo "🧪 Running Unit Tests Only..."
        run_workflow ".github/workflows/test-pipeline.yml" "unit-tests"
        ;;
    4)
        echo "🧪 Running Integration Tests Only..."
        run_workflow ".github/workflows/test-pipeline.yml" "integration-tests"
        ;;
    5)
        echo "🧪 Running E2E Tests Only..."
        run_workflow ".github/workflows/test-pipeline.yml" "e2e-tests"
        ;;
    6)
        echo "🧪 Running Security Tests Only..."
        run_workflow ".github/workflows/test-pipeline.yml" "security-tests"
        ;;
    7)
        echo "Available workflows:"
        echo "- .github/workflows/test-pipeline.yml"
        echo "- .github/workflows/ci.yml"
        echo ""
        read -p "Enter workflow file path: " workflow_path
        read -p "Enter job name (optional): " job_name
        run_workflow "$workflow_path" "$job_name"
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

echo ""
echo "✅ Local CI testing completed!"
echo ""
echo "💡 Tips:"
echo "- Use 'act --list' to see all available workflows and jobs"
echo "- Use 'act -n' for dry-run mode"
echo "- Use 'act --help' for more options"
echo "- Check .actrc for configuration options"
