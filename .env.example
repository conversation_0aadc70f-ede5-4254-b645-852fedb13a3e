# BenefitLens Environment Configuration

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/benefitlens

# Application Configuration
APP_URL=http://localhost:3000
NODE_ENV=development

# Email Configuration (Mailgun)
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_DOMAIN=your_mailgun_domain

# Rate Limiting Configuration
# Set to 'true' to disable rate limiting (useful for testing)
# WARNING: Only disable rate limiting in test environments!
DISABLE_RATE_LIMITING=false

# Session Configuration
SESSION_SECRET=your_session_secret_key

# Admin Configuration
ADMIN_EMAIL=<EMAIL>

# Testing Configuration
# These are used for E2E and integration tests
TEST_DATABASE_URL=postgresql://username:password@localhost:5432/benefitlens_test
E2E_BASE_URL=http://localhost:3000

# Optional: Analytics Configuration
ANALYTICS_ENABLED=true

# Optional: Logging Configuration
LOG_LEVEL=info
