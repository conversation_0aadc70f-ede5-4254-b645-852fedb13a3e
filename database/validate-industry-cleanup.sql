-- Validation script for industry cleanup
-- Run this BEFORE applying the migration to see current state
-- Run this AFTER applying the migration to verify results

\echo '=== INDUSTRY CLEANUP VALIDATION ==='
\echo ''

-- Show current industry distribution
\echo '📊 Current Industry Distribution:'
SELECT 
    industry,
    COUNT(*) as company_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM companies 
WHERE industry IS NOT NULL AND industry != ''
GROUP BY industry 
ORDER BY company_count DESC, industry;

\echo ''
\echo '📈 Industry Statistics:'
SELECT 
    COUNT(*) as total_companies,
    COUNT(DISTINCT industry) as unique_industries,
    COUNT(CASE WHEN industry IS NULL OR industry = '' THEN 1 END) as companies_without_industry
FROM companies;

\echo ''
\echo '🔍 Potential Issues (if any):'
-- Check for industries that might need manual review
SELECT 
    industry,
    COUNT(*) as count
FROM companies 
WHERE industry IS NOT NULL 
  AND industry != ''
  AND industry NOT IN (
    'Aerospace & Defense',
    'Automotive',
    'Banking & Financial Services',
    'Biotechnology & Pharmaceuticals',
    'Chemicals',
    'Construction & Materials',
    'Consumer Goods',
    'E-commerce & Retail',
    'Education',
    'Energy & Utilities',
    'Engineering & Manufacturing',
    'Healthcare & Medical Technology',
    'Insurance',
    'Logistics & Transportation',
    'Media & Entertainment',
    'Mining & Metals',
    'Real Estate',
    'Software & Technology',
    'Telecommunications',
    'Tourism & Hospitality'
  )
GROUP BY industry
ORDER BY count DESC;

\echo ''
\echo '✅ Validation Complete'
