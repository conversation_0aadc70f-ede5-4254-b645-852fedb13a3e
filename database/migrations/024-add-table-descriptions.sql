-- Migration: Add descriptions to all database tables
-- This migration adds comprehensive descriptions to all tables that currently lack them

-- Core business tables
COMMENT ON TABLE public.benefits IS 'Master list of all available employee benefits that companies can offer';

COMMENT ON TABLE public.company_benefits IS 'Junction table linking companies to the benefits they offer, with verification status tracking';

COMMENT ON TABLE public.benefit_verifications IS 'User-submitted verifications confirming or disputing company benefits';

COMMENT ON TABLE public.benefit_removal_disputes IS 'User disputes against automatic benefit removals, requiring admin review';

-- User and authentication tables
COMMENT ON TABLE public.users IS 'Registered users of the BenefitLens platform with company associations and payment status';

COMMENT ON TABLE public.user_sessions IS 'Active user sessions for authentication and session management';

COMMENT ON TABLE public.magic_link_tokens IS 'Temporary tokens for passwordless authentication via magic links';

COMMENT ON TABLE public.magic_link_rate_limits IS 'Rate limiting for magic link requests to prevent abuse';

COMMENT ON TABLE public.company_verification_tokens IS 'Tokens for company email domain verification process';

-- Analytics and tracking tables
COMMENT ON TABLE public.search_queries IS 'User search queries for analytics and search optimization';

COMMENT ON TABLE public.company_page_views IS 'Individual page view events for company profile analytics';

COMMENT ON TABLE public.company_analytics_summary IS 'Daily aggregated analytics data per company for performance tracking';

COMMENT ON TABLE public.daily_analytics_summary IS 'Platform-wide daily analytics aggregations for admin dashboard';

COMMENT ON TABLE public.benefit_search_interactions IS 'User interactions with benefits in search results for engagement tracking';

-- User features
COMMENT ON TABLE public.saved_companies IS 'Companies saved by users for quick access and comparison';

COMMENT ON TABLE public.user_benefit_rankings IS 'User-defined benefit priority rankings for personalized recommendations';

COMMENT ON TABLE public.missing_company_reports IS 'User reports requesting addition of missing companies to the platform';

-- Location and geography
COMMENT ON TABLE public.company_locations IS 'Geographic locations where companies operate, with normalized city data';

-- System infrastructure
COMMENT ON TABLE public.cache_store IS 'Application-level caching system for improved performance';

COMMENT ON TABLE public.csrf_tokens IS 'CSRF protection tokens for secure form submissions';

COMMENT ON TABLE public.rate_limits IS 'General rate limiting system for API and feature access control';

COMMENT ON TABLE public.session_config IS 'Configuration settings for session management and security';

COMMENT ON TABLE public.migration_log IS 'Database migration tracking for deployment and rollback management';

-- Log this migration
INSERT INTO migration_log (id, migration_name, description) 
VALUES (24, '024-add-table-descriptions', 'Add comprehensive descriptions to all database tables for documentation and maintenance');
