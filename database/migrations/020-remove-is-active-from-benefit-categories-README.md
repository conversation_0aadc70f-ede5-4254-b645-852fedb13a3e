# Migration 020: Remove is_active Field from Benefit Categories

## Overview
This migration removes the `is_active` boolean field from the `benefit_categories` table. The field is no longer needed because we now determine the active status of benefit categories dynamically based on whether they have associated benefits.

## What This Migration Does
1. Drops the `is_active` column from the `benefit_categories` table
2. Updates table comments to document the change
3. Logs the migration in the `migration_log` table

## Production Deployment Commands

### 1. Backup Database (REQUIRED)
Before running any migration in production, always create a backup:

```bash
# Create a backup with timestamp
pg_dump -h your-production-host -U your-username -d benefitlens_production > backup_$(date +%Y%m%d_%H%M%S)_before_migration_020.sql

# Or using the backup script if available
./scripts/backup-database.sh
```

### 2. Run the Migration
Connect to your production database and run the migration:

```bash
# Connect to production database
psql -h your-production-host -U your-username -d benefitlens_production

# Run the migration
\i database/migrations/020-remove-is-active-from-benefit-categories.sql
```

### 3. Verify the Migration
After running the migration, verify it was successful:

```sql
-- Check that the is_active column is gone
\d benefit_categories

-- Verify migration was logged
SELECT * FROM migration_log WHERE migration_name = '020-remove-is-active-from-benefit-categories';

-- Check that benefit categories still work correctly
SELECT id, name, display_name, sort_order FROM benefit_categories ORDER BY sort_order;
```

## Rollback Instructions
If you need to rollback this migration, you can restore the `is_active` column:

```sql
-- Add the is_active column back with default value true
ALTER TABLE benefit_categories ADD COLUMN is_active BOOLEAN DEFAULT true;

-- Update all existing categories to be active
UPDATE benefit_categories SET is_active = true;

-- Remove the migration log entry
DELETE FROM migration_log WHERE migration_name = '020-remove-is-active-from-benefit-categories';
```

## Impact Assessment
- **Breaking Changes**: None - the application code already uses dynamic logic based on benefit count
- **Downtime**: Minimal - this is a simple column drop operation
- **Data Loss**: The `is_active` values will be lost, but they are no longer used by the application
- **Performance**: Slight improvement - smaller table size and no need to filter by `is_active`

## Testing
After deployment, verify that:
1. Benefit categories display correctly in the admin interface
2. The "Show Empty" filter works correctly (shows categories with 0 benefits)
3. Category management (create, edit, delete) still works
4. No application errors related to missing `is_active` field

## Related Files Updated
- `src/types/database.ts` - Removed `is_active` from `BenefitCategory` interface
- Application logic already uses dynamic benefit count instead of `is_active` field

## Notes
- This migration is safe to run as the application code no longer references the `is_active` field
- The migration includes `IF EXISTS` clause to prevent errors if the column was already dropped
- Migration logging ensures we can track when this change was applied
