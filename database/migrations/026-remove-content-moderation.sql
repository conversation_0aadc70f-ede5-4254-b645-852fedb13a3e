-- Migration: Remove content moderation system
-- This migration removes the unused content moderation tables and indexes

-- Drop indexes first
DROP INDEX IF EXISTS idx_content_moderation_queue_status;
DROP INDEX IF EXISTS idx_content_moderation_queue_priority;
DROP INDEX IF EXISTS idx_content_moderation_queue_created_at;
DROP INDEX IF EXISTS idx_content_moderation_queue_user_id;
DROP INDEX IF EXISTS idx_content_moderation_queue_content_type;
DROP INDEX IF EXISTS idx_moderation_actions_queue_item_id;
DROP INDEX IF EXISTS idx_moderation_actions_performed_by;
DROP INDEX IF EXISTS idx_moderation_actions_created_at;

-- Drop tables (order matters due to foreign key constraints)
DROP TABLE IF EXISTS moderation_actions;
DROP TABLE IF EXISTS content_moderation_queue;

-- Log this migration
INSERT INTO migration_log (id, migration_name, description) 
VALUES (26, '026-remove-content-moderation', 'Remove unused content moderation system tables and indexes');
