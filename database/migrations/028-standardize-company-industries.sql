-- Migration: Standardize Company Industries
-- Description: Clean up and standardize company industry values to reduce inconsistencies
-- Date: 2025-09-05

-- Log the migration start
INSERT INTO migration_log (migration_name, description)
VALUES ('028-standardize-company-industries', 'Clean up and standardize company industry values to reduce inconsistencies');

-- Create a temporary function to standardize industries
CREATE OR REPLACE FUNCTION standardize_industry(input_industry TEXT) 
RETURNS TEXT AS $$
BEGIN
    -- Handle NULL or empty values
    IF input_industry IS NULL OR TRIM(input_industry) = '' THEN
        RETURN input_industry;
    END IF;
    
    -- Clean up the input (remove trailing dots, backslashes, trim)
    input_industry := TRIM(REGEXP_REPLACE(input_industry, '[\\\.]+$', ''));
    
    -- Apply industry mappings
    RETURN CASE input_industry
        WHEN 'Aerospace & Defence' THEN 'Aerospace & Defense'
        WHEN 'Defence' THEN 'Aerospace & Defense'
        WHEN 'Airlines' THEN 'Logistics & Transportation'
        WHEN 'Airport operator' THEN 'Logistics & Transportation'
        WHEN 'Apparel' THEN 'Consumer Goods'
        WHEN 'Banking' THEN 'Banking & Financial Services'
        WHEN 'Biotechnology' THEN 'Biotechnology & Pharmaceuticals'
        WHEN 'Chemistry' THEN 'Chemicals'
        WHEN 'Clothing, Accessories' THEN 'Consumer Goods'
        WHEN 'Conglomerate' THEN 'Engineering & Manufacturing'
        WHEN 'Construction' THEN 'Construction & Materials'
        WHEN 'Construction Materials' THEN 'Construction & Materials'
        WHEN 'Consumer goods' THEN 'Consumer Goods'
        WHEN 'Consulting' THEN 'Software & Technology'
        WHEN 'Delivery service' THEN 'Logistics & Transportation'
        WHEN 'Distribution' THEN 'Logistics & Transportation'
        WHEN 'E-Commerce' THEN 'E-commerce & Retail'
        WHEN 'E-commerce' THEN 'E-commerce & Retail'
        WHEN 'e-Commerce' THEN 'E-commerce & Retail'
        WHEN 'Electrical engineering' THEN 'Engineering & Manufacturing'
        WHEN 'Energy technology' THEN 'Energy & Utilities'
        WHEN 'Engineering and Services' THEN 'Engineering & Manufacturing'
        WHEN 'Engineering, medical technology' THEN 'Healthcare & Medical Technology'
        WHEN 'Financial Services' THEN 'Banking & Financial Services'
        WHEN 'Handling equipment' THEN 'Engineering & Manufacturing'
        WHEN 'Healthcare' THEN 'Healthcare & Medical Technology'
        WHEN 'Human Resources' THEN 'Software & Technology'
        WHEN 'Industrials' THEN 'Engineering & Manufacturing'
        WHEN 'Internet Exchange / Network Interconnection' THEN 'Telecommunications'
        WHEN 'Intralogistics, Mechanical engineering' THEN 'Engineering & Manufacturing'
        WHEN 'Investment management' THEN 'Banking & Financial Services'
        WHEN 'IT' THEN 'Software & Technology'
        WHEN 'IT and consulting' THEN 'Software & Technology'
        WHEN 'IT services' THEN 'Software & Technology'
        WHEN 'Legal Services' THEN 'Banking & Financial Services'
        WHEN 'Leisure-events' THEN 'Tourism & Hospitality'
        WHEN 'Logistics' THEN 'Logistics & Transportation'
        WHEN 'Machinery' THEN 'Engineering & Manufacturing'
        WHEN 'Manufacturing' THEN 'Engineering & Manufacturing'
        WHEN 'Meal kit' THEN 'E-commerce & Retail'
        WHEN 'Medical Equipment' THEN 'Healthcare & Medical Technology'
        WHEN 'Medical equipment' THEN 'Healthcare & Medical Technology'
        WHEN 'Medical Technology' THEN 'Healthcare & Medical Technology'
        WHEN 'Medical technology' THEN 'Healthcare & Medical Technology'
        WHEN 'Metals' THEN 'Mining & Metals'
        WHEN 'Mining (fertilizer and salt)' THEN 'Mining & Metals'
        WHEN 'Online-market' THEN 'E-commerce & Retail'
        WHEN 'Optics' THEN 'Healthcare & Medical Technology'
        WHEN 'Packaging' THEN 'Engineering & Manufacturing'
        WHEN 'Payment Services' THEN 'Banking & Financial Services'
        WHEN 'Pharmaceuticals' THEN 'Biotechnology & Pharmaceuticals'
        WHEN 'Pharmacy' THEN 'Biotechnology & Pharmaceuticals'
        WHEN 'Printing' THEN 'Engineering & Manufacturing'
        WHEN 'Private Equity' THEN 'Banking & Financial Services'
        WHEN 'Real estate' THEN 'Real Estate'
        WHEN 'Renewable energy' THEN 'Energy & Utilities'
        WHEN 'Retail' THEN 'E-commerce & Retail'
        WHEN 'Retail and agribusiness' THEN 'E-commerce & Retail'
        WHEN 'Retail, Optics' THEN 'E-commerce & Retail'
        WHEN 'Semiconductor industry' THEN 'Software & Technology'
        WHEN 'Software' THEN 'Software & Technology'
        WHEN 'Sports' THEN 'Consumer Goods'
        WHEN 'Sports equipment' THEN 'Consumer Goods'
        WHEN 'Technology' THEN 'Software & Technology'
        WHEN 'Telecommunication' THEN 'Telecommunications'
        WHEN 'Tourism' THEN 'Tourism & Hospitality'
        WHEN 'Transport & Logistics' THEN 'Logistics & Transportation'
        WHEN 'Utilities' THEN 'Energy & Utilities'
        WHEN 'Waste management' THEN 'Energy & Utilities'
        WHEN 'Wind energy' THEN 'Energy & Utilities'
        ELSE input_industry -- Return original if no mapping found
    END;
END;
$$ LANGUAGE plpgsql;

-- Show current industry distribution before cleanup
DO $$
DECLARE
    rec RECORD;
BEGIN
    RAISE NOTICE 'Industry distribution BEFORE cleanup:';
    FOR rec IN
        SELECT industry, COUNT(*) as count
        FROM companies
        WHERE industry IS NOT NULL AND industry != ''
        GROUP BY industry
        ORDER BY count DESC, industry
    LOOP
        RAISE NOTICE '  %: % companies', rec.industry, rec.count;
    END LOOP;
END $$;

-- Update all company industries using the standardization function
UPDATE companies
SET industry = standardize_industry(industry),
    updated_at = NOW()
WHERE industry IS NOT NULL
  AND industry != ''
  AND standardize_industry(industry) != industry;

-- Show industry distribution after cleanup
DO $$
DECLARE
    rec RECORD;
    total_companies INTEGER;
    total_industries INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_companies FROM companies WHERE industry IS NOT NULL AND industry != '';
    SELECT COUNT(DISTINCT industry) INTO total_industries FROM companies WHERE industry IS NOT NULL AND industry != '';

    RAISE NOTICE 'Industry distribution AFTER cleanup:';
    RAISE NOTICE 'Total companies: %, Total unique industries: %', total_companies, total_industries;
    RAISE NOTICE '';

    FOR rec IN
        SELECT industry, COUNT(*) as count
        FROM companies
        WHERE industry IS NOT NULL AND industry != ''
        GROUP BY industry
        ORDER BY count DESC, industry
    LOOP
        RAISE NOTICE '  %: % companies', rec.industry, rec.count;
    END LOOP;
END $$;

-- Refresh the materialized view to reflect the changes
REFRESH MATERIALIZED VIEW companies_with_benefits_cache;

-- Drop the temporary function
DROP FUNCTION standardize_industry(TEXT);

-- Migration completed successfully (logged automatically by applied_at timestamp)

-- Add a comment to document the standardized industries
COMMENT ON COLUMN companies.industry IS 'Standardized industry categories: Aerospace & Defense, Automotive, Banking & Financial Services, Biotechnology & Pharmaceuticals, Chemicals, Construction & Materials, Consumer Goods, E-commerce & Retail, Education, Energy & Utilities, Engineering & Manufacturing, Healthcare & Medical Technology, Insurance, Logistics & Transportation, Media & Entertainment, Mining & Metals, Real Estate, Software & Technology, Telecommunications, Tourism & Hospitality';
