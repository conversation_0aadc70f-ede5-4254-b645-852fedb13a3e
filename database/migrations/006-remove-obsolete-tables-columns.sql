-- Migration: Remove obsolete tables and columns
-- This migration removes database objects that are no longer used in the application

-- Analysis Summary:
-- 1. company_users table - Not actively used in current codebase, replaced by email domain matching
-- 2. session_activity table - Created but never actively used for logging
-- 3. verification_status column - Referenced in system-status.js but doesn't exist in schema
-- 4. Some unused indexes and functions that are no longer needed

-- Step 1: Remove unused tables

-- Drop company_users table (replaced by email domain matching)
-- This table was intended for manual company-user associations but is not used
DROP TABLE IF EXISTS company_users CASCADE;

-- Drop session_activity table (created but never actively used)
-- The log_session_activity function exists but is not called in the application
DROP TABLE IF EXISTS session_activity CASCADE;

-- Step 2: Remove related functions that are no longer needed

-- Remove session activity logging function (not used in application)
DROP FUNCTION IF EXISTS log_session_activity(character varying, character varying, inet, text);

-- Step 3: Remove unused indexes (if any exist for dropped tables)
-- These will be automatically dropped with the tables, but explicit for clarity

DROP INDEX IF EXISTS idx_company_users_company_id;
DROP INDEX IF EXISTS idx_company_users_email;
DROP INDEX IF EXISTS idx_session_activity_token;
DROP INDEX IF EXISTS idx_session_activity_created_at;

-- Step 4: Clean up any references in other tables or views
-- Check if any views or materialized views reference the dropped tables
-- (None found in current schema)

-- Step 5: Update comments and documentation
COMMENT ON TABLE companies IS 'Companies table - user access controlled by email domain matching and explicit company_id in users table';

-- Step 6: Verify no foreign key constraints are broken
-- The dropped tables don't have foreign keys pointing to them from active tables

-- Add migration log entry
INSERT INTO migration_log (migration_name, description, applied_at) 
VALUES (
  '006-remove-obsolete-tables-columns', 
  'Removed obsolete tables: company_users, session_activity and related functions',
  NOW()
) ON CONFLICT (migration_name) DO NOTHING;

-- Display summary of changes
\echo '=== OBSOLETE TABLES REMOVAL SUMMARY ==='
\echo 'Removed tables:'
\echo '  - company_users (replaced by email domain matching)'
\echo '  - session_activity (created but never used)'
\echo ''
\echo 'Removed functions:'
\echo '  - log_session_activity (not called in application)'
\echo ''
\echo 'Impact: No functional impact - these were unused database objects'
\echo 'Benefits: Reduced database size and simplified schema'
