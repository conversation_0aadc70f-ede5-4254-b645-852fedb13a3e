-- Migration: Add missing comments to auth_logs table
-- This migration ensures auth_logs table has all the proper comments for documentation

-- Add comments for auth_logs table and columns
COMMENT ON TABLE auth_logs IS 'Detailed logs of authentication events for security monitoring and admin review';
COMMENT ON COLUMN auth_logs.event_type IS 'Type of authentication event (sign_in_request, sign_up_request, etc.)';
COMMENT ON COLUMN auth_logs.status IS 'Success or failure status of the authentication event';
COMMENT ON COLUMN auth_logs.error_type IS 'Categorized error type for failed events';
COMMENT ON COLUMN auth_logs.failure_reason IS 'Human-readable explanation of why the authentication failed';
COMMENT ON COLUMN auth_logs.token_used IS 'Partial magic link token (first 8 chars) for tracking';
COMMENT ON COLUMN auth_logs.additional_context IS 'Additional context data in JSON format';

-- Log this migration
INSERT INTO migration_log (migration_name, description) 
VALUES ('022-add-auth-logs-comments', 'Add missing documentation comments to auth_logs table and columns');
