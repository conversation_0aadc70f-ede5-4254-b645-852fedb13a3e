-- Migration: Add privacy features
-- This migration adds GDPR compliance features

-- Add deletion tracking columns to users table
ALTER TABLE users 
ADD COLUMN deletion_requested_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN deletion_reason TEXT;

-- Create data deletion requests table
CREATE TABLE data_deletion_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    reason TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID REFERENCES users(id),
    notes TEXT,
    CONSTRAINT data_deletion_requests_status_check CHECK (status IN ('pending', 'processing', 'completed', 'cancelled'))
);

-- Create data export logs table
CREATE TABLE data_export_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    export_type VARCHAR(50) NOT NULL DEFAULT 'full',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    file_size_bytes BIGINT,
    data_types JSONB
);



-- Create indexes for performance
CREATE INDEX idx_data_deletion_requests_user_id ON data_deletion_requests(user_id);
CREATE INDEX idx_data_deletion_requests_status ON data_deletion_requests(status);
CREATE INDEX idx_data_deletion_requests_created_at ON data_deletion_requests(created_at);
CREATE INDEX idx_users_deletion_requested_at ON users(deletion_requested_at);

CREATE INDEX idx_data_export_logs_user_id ON data_export_logs(user_id);
CREATE INDEX idx_data_export_logs_created_at ON data_export_logs(created_at);



-- Add table descriptions
COMMENT ON TABLE data_deletion_requests IS 'Tracks user requests for data deletion under GDPR Article 17 (Right to Erasure)';
COMMENT ON TABLE data_export_logs IS 'Logs user data export requests under GDPR Article 15 (Right of Access)';


-- Add column descriptions
COMMENT ON COLUMN users.deletion_requested_at IS 'Timestamp when user requested account deletion';
COMMENT ON COLUMN users.deletion_reason IS 'User-provided reason for account deletion request';

-- Log this migration
INSERT INTO migration_log (id, migration_name, description)
VALUES (25, '025-add-privacy-features', 'Add GDPR compliance features for legal compliance');
