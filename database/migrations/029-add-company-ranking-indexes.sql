-- Migration: Add indexes for company ranking performance
-- Date: 2025-09-05
-- Description: Add composite indexes to optimize the new company ranking system
--              that ranks companies by benefits quality (60%) and user engagement (40%)

-- Add composite index for company benefits with verification status
-- This optimizes the benefits quality score calculation
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_company_benefits_company_verified
ON company_benefits (company_id, is_verified);

-- Add composite index for analytics summary with company and date
-- This optimizes the user engagement score calculation
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_company_analytics_summary_company_date
ON company_analytics_summary (company_id, date);

-- Note: Removed profile completeness index as it's no longer used in ranking
-- Profile data is managed by admins only, not by companies

-- Log migration
INSERT INTO migration_log (id, migration_name, description, applied_at)
VALUES (29, '029-add-company-ranking-indexes', 'Add indexes for company ranking performance', NOW());
