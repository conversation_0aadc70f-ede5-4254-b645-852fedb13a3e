-- Migration: Remove is_active field from benefit_categories table
-- This migration removes the is_active boolean field from benefit_categories table
-- since we now determine active status based on whether the category has benefits

-- Drop the is_active column from benefit_categories table
-- This column is no longer needed since we determine active status dynamically
-- based on benefit count
ALTER TABLE benefit_categories DROP COLUMN IF EXISTS is_active;

-- Add comment to document the change
COMMENT ON TABLE benefit_categories IS 'Benefit categories table - active status is now determined dynamically based on benefit count';

-- Log this migration
INSERT INTO migration_log (migration_name, description) 
VALUES ('020-remove-is-active-from-benefit-categories', 'Remove is_active field from benefit_categories table - active status now determined by benefit count');

-- Create migration_log table if it doesn't exist (for safety)
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
