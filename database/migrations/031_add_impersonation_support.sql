-- Migration: Add impersonation support to user sessions
-- This allows admins to impersonate users for debugging and support purposes

-- Add impersonation columns to user_sessions table
ALTER TABLE user_sessions 
ADD COLUMN is_impersonation BOOLEAN DEFAULT FALSE NOT NULL,
ADD COLUMN original_admin_id UUID NULL,
ADD COLUMN impersonated_user_id UUID NULL;

-- Add foreign key constraints
ALTER TABLE user_sessions 
ADD CONSTRAINT user_sessions_original_admin_id_fkey 
FOREIGN KEY (original_admin_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE user_sessions 
ADD CONSTRAINT user_sessions_impersonated_user_id_fkey 
FOREIGN KEY (impersonated_user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Add indexes for performance
CREATE INDEX idx_user_sessions_is_impersonation ON user_sessions(is_impersonation);
CREATE INDEX idx_user_sessions_original_admin_id ON user_sessions(original_admin_id);
CREATE INDEX idx_user_sessions_impersonated_user_id ON user_sessions(impersonated_user_id);

-- Add check constraint to ensure impersonation data consistency
ALTER TABLE user_sessions 
ADD CONSTRAINT user_sessions_impersonation_check 
CHECK (
  (is_impersonation = FALSE AND original_admin_id IS NULL AND impersonated_user_id IS NULL) OR
  (is_impersonation = TRUE AND original_admin_id IS NOT NULL AND impersonated_user_id IS NOT NULL)
);

-- Update session monitoring view to include impersonation info
DROP VIEW IF EXISTS session_monitoring;
CREATE VIEW session_monitoring AS
SELECT 
    u.email,
    u.first_name,
    u.last_name,
    us.session_token,
    us.created_at,
    us.expires_at,
    us.is_impersonation,
    admin_u.email as original_admin_email,
    impersonated_u.email as impersonated_user_email,
    CASE 
        WHEN us.expires_at < NOW() THEN 'expired'
        ELSE 'active'
    END as status,
    EXTRACT(EPOCH FROM (us.expires_at - NOW())) / 3600 as hours_until_expiry
FROM user_sessions us
JOIN users u ON us.user_id = u.id
LEFT JOIN users admin_u ON us.original_admin_id = admin_u.id
LEFT JOIN users impersonated_u ON us.impersonated_user_id = impersonated_u.id
ORDER BY us.created_at DESC;

-- Add comment explaining the impersonation feature
COMMENT ON COLUMN user_sessions.is_impersonation IS 'Whether this session is an admin impersonating another user';
COMMENT ON COLUMN user_sessions.original_admin_id IS 'ID of the admin user who initiated the impersonation (only set when is_impersonation=true)';
COMMENT ON COLUMN user_sessions.impersonated_user_id IS 'ID of the user being impersonated (only set when is_impersonation=true, should match user_id)';

-- Add new event types for impersonation to activity_log constraint
ALTER TABLE activity_log DROP CONSTRAINT IF EXISTS activity_log_event_type_check;
ALTER TABLE activity_log ADD CONSTRAINT activity_log_event_type_check CHECK (
  (event_type)::text = ANY (ARRAY[
    ('benefit_automatically_removed'::character varying)::text,
    ('benefit_disputed'::character varying)::text,
    ('benefit_removal_dispute_approved'::character varying)::text,
    ('benefit_removal_dispute_cancelled'::character varying)::text,
    ('benefit_removal_dispute_rejected'::character varying)::text,
    ('benefit_removal_dispute_submitted'::character varying)::text,
    ('benefit_verified'::character varying)::text,
    ('cache_refresh'::character varying)::text,
    ('company_added'::character varying)::text,
    ('company_deleted'::character varying)::text,
    ('session_cleanup'::character varying)::text,
    ('user_deleted'::character varying)::text,
    ('user_registered'::character varying)::text,
    ('user_impersonation_started'::character varying)::text,
    ('user_impersonation_ended'::character varying)::text
  ])
);

-- Update the comment to include new event types
COMMENT ON COLUMN activity_log.event_type IS 'Type of event: benefit_automatically_removed, benefit_disputed, benefit_removal_dispute_approved, benefit_removal_dispute_cancelled, benefit_removal_dispute_rejected, benefit_removal_dispute_submitted, benefit_verified, cache_refresh, company_added, company_deleted, session_cleanup, user_deleted, user_registered, user_impersonation_started, user_impersonation_ended';

-- Log this migration
INSERT INTO migration_log (id, migration_name, description, applied_at)
VALUES (31, '031_add_impersonation_support', 'Add impersonation support to user sessions', NOW());
