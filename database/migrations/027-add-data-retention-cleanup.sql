-- Migration: Add data retention and IP address anonymization
-- This migration implements GDPR-compliant data retention policies

-- Create function to anonymize IP addresses
CREATE OR REPLACE FUNCTION anonymize_ip_address(ip_addr INET) 
RETURNS INET AS $$
BEGIN
    -- Handle IPv4 addresses (zero out last octet)
    IF family(ip_addr) = 4 THEN
        RETURN (host(ip_addr)::text || '.0')::INET;
    -- Handle IPv6 addresses (zero out last 64 bits)
    ELSIF family(ip_addr) = 6 THEN
        -- Get first 64 bits and append zeros
        RETURN (substring(host(ip_addr) from 1 for position(':' in reverse(substring(host(ip_addr) from 1 for length(host(ip_addr))/2)))) || '::')::INET;
    ELSE
        RETURN ip_addr;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        -- If anonymization fails, return a generic anonymized IP
        IF family(ip_addr) = 4 THEN
            RETURN '0.0.0.0'::INET;
        ELSE
            RETURN '::'::INET;
        END IF;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON> function to clean up old analytics data with IP addresses
CREATE OR REPLACE FUNCTION cleanup_old_analytics_data() 
RETURNS TABLE(
    search_queries_anonymized INTEGER,
    page_views_anonymized INTEGER,
    old_exports_deleted INTEGER
) AS $$
DECLARE
    search_count INTEGER := 0;
    views_count INTEGER := 0;
    exports_count INTEGER := 0;
BEGIN
    -- Anonymize IP addresses in search_queries older than 12 months
    UPDATE search_queries 
    SET ip_address = anonymize_ip_address(ip_address)
    WHERE created_at < NOW() - INTERVAL '12 months'
      AND ip_address IS NOT NULL
      AND host(ip_address) != '0.0.0.0'
      AND host(ip_address) != '::';
    
    GET DIAGNOSTICS search_count = ROW_COUNT;

    -- Anonymize IP addresses in company_page_views older than 12 months
    UPDATE company_page_views 
    SET ip_address = anonymize_ip_address(ip_address)
    WHERE created_at < NOW() - INTERVAL '12 months'
      AND ip_address IS NOT NULL
      AND host(ip_address) != '0.0.0.0'
      AND host(ip_address) != '::';
    
    GET DIAGNOSTICS views_count = ROW_COUNT;

    -- Delete old data export logs older than 3 years (compliance retention)
    DELETE FROM data_export_logs 
    WHERE created_at < NOW() - INTERVAL '3 years';
    
    GET DIAGNOSTICS exports_count = ROW_COUNT;

    -- Return counts
    RETURN QUERY SELECT search_count, views_count, exports_count;
END;
$$ LANGUAGE plpgsql;

-- Create function for comprehensive data retention cleanup
CREATE OR REPLACE FUNCTION comprehensive_data_retention_cleanup()
RETURNS TABLE(
    operation TEXT,
    records_affected INTEGER,
    completed_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    cleanup_result RECORD;
    rate_limit_count INTEGER := 0;
    session_count INTEGER := 0;
BEGIN
    -- Run analytics cleanup
    SELECT * INTO cleanup_result FROM cleanup_old_analytics_data();
    
    RETURN QUERY SELECT 
        'search_queries_anonymized'::TEXT, 
        cleanup_result.search_queries_anonymized, 
        NOW();
    
    RETURN QUERY SELECT 
        'page_views_anonymized'::TEXT, 
        cleanup_result.page_views_anonymized, 
        NOW();
    
    RETURN QUERY SELECT 
        'old_exports_deleted'::TEXT, 
        cleanup_result.old_exports_deleted, 
        NOW();

    -- Clean up expired rate limits (older than 7 days)
    DELETE FROM rate_limits 
    WHERE expires_at < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS rate_limit_count = ROW_COUNT;
    
    RETURN QUERY SELECT 
        'expired_rate_limits_deleted'::TEXT, 
        rate_limit_count, 
        NOW();

    -- Clean up expired sessions (older than 30 days)
    DELETE FROM user_sessions 
    WHERE expires_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS session_count = ROW_COUNT;
    
    RETURN QUERY SELECT 
        'expired_sessions_deleted'::TEXT, 
        session_count, 
        NOW();

    -- Log the cleanup operation
    INSERT INTO activity_log (event_type, description, metadata)
    VALUES (
        'data_retention_cleanup',
        'Automated data retention cleanup completed',
        jsonb_build_object(
            'search_queries_anonymized', cleanup_result.search_queries_anonymized,
            'page_views_anonymized', cleanup_result.page_views_anonymized,
            'exports_deleted', cleanup_result.old_exports_deleted,
            'rate_limits_deleted', rate_limit_count,
            'sessions_deleted', session_count,
            'cleanup_date', NOW()
        )
    );
END;
$$ LANGUAGE plpgsql;

-- Create table to track cleanup schedules and results
CREATE TABLE data_retention_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cleanup_type VARCHAR(100) NOT NULL,
    records_affected INTEGER NOT NULL DEFAULT 0,
    operation_details JSONB,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) NOT NULL DEFAULT 'completed',
    error_message TEXT,
    CONSTRAINT data_retention_log_status_check CHECK (status IN ('running', 'completed', 'failed'))
);

-- Create index for performance
CREATE INDEX idx_data_retention_log_started_at ON data_retention_log(started_at);
CREATE INDEX idx_data_retention_log_cleanup_type ON data_retention_log(cleanup_type);

-- Add table description
COMMENT ON TABLE data_retention_log IS 'Tracks automated data retention cleanup operations for GDPR compliance';
COMMENT ON FUNCTION anonymize_ip_address(INET) IS 'Anonymizes IP addresses by zeroing out identifying portions';
COMMENT ON FUNCTION cleanup_old_analytics_data() IS 'Anonymizes IP addresses in analytics data older than 12 months';
COMMENT ON FUNCTION comprehensive_data_retention_cleanup() IS 'Performs comprehensive data retention cleanup according to privacy policy';

-- Log this migration
INSERT INTO migration_log (id, migration_name, description)
VALUES (27, '027-add-data-retention-cleanup', 'Add GDPR-compliant data retention and IP anonymization functions');
