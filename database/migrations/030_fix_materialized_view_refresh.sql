-- Fix materialized view refresh function to handle unpopulated views
-- This fixes the "CONCURRENTLY cannot be used when the materialized view is not populated" error

-- Update the refresh function to handle unpopulated views
CREATE OR REPLACE FUNCTION refresh_cache_views()
RETURNS VOID AS $$
DECLARE
    companies_populated BOOLEAN;
    benefits_populated BOOLEAN;
BEGIN
    -- Check if materialized views are populated
    SELECT ispopulated INTO companies_populated 
    FROM pg_matviews 
    WHERE matviewname = 'companies_with_benefits_cache';
    
    SELECT ispopulated INTO benefits_populated 
    FROM pg_matviews 
    WHERE matviewname = 'benefits_with_categories_cache';
    
    -- Refresh companies_with_benefits_cache
    IF companies_populated THEN
        REFRESH MATERIALIZED VIEW CONCURRENTLY companies_with_benefits_cache;
    ELSE
        REFRESH MATERIALIZED VIEW companies_with_benefits_cache;
    END IF;
    
    -- Refresh benefits_with_categories_cache
    IF benefits_populated THEN
        REFRESH MATERIALIZED VIEW CONCURRENTLY benefits_with_categories_cache;
    ELSE
        REFRESH MATERIALIZED VIEW benefits_with_categories_cache;
    END IF;
    
    -- Log the refresh
    INSERT INTO activity_log (
        event_type,
        event_description,
        created_at
    ) VALUES (
        'cache_refresh',
        'Materialized views refreshed (companies_populated: ' || COALESCE(companies_populated::text, 'null') || 
        ', benefits_populated: ' || COALESCE(benefits_populated::text, 'null') || ')',
        NOW()
    );
END;
$$ LANGUAGE plpgsql;

-- Update migration log
INSERT INTO migration_log (id, migration_name, description) VALUES
(30, '030_fix_materialized_view_refresh', 'Fixed materialized view refresh function to handle unpopulated views')
ON CONFLICT (migration_name) DO NOTHING;
