-- Migration: Add benefit activity events to activity log
-- This migration adds new event types for tracking benefit additions and removals from companies

-- Update the event_type check constraint to include new event types
ALTER TABLE activity_log DROP CONSTRAINT IF EXISTS activity_log_event_type_check;

ALTER TABLE activity_log ADD CONSTRAINT activity_log_event_type_check 
CHECK (event_type IN (
  'company_added',
  'company_deleted', 
  'user_registered',
  'user_deleted',
  'benefit_added_to_company',
  'benefit_removed_from_company',
  'benefit_verified',
  'benefit_disputed',
  'benefit_removal_dispute_submitted',
  'benefit_removal_dispute_approved',
  'benefit_removal_dispute_rejected',
  'benefit_removal_dispute_cancelled',
  'benefit_automatically_removed',
  'cache_refresh',
  'session_cleanup'
));

-- Update the comment to reflect the new event types
COMMENT ON COLUMN activity_log.event_type IS 'Type of event: benefit_added_to_company, benefit_automatically_removed, benefit_disputed, benefit_removal_dispute_approved, benefit_removal_dispute_cancelled, benefit_removal_dispute_rejected, benefit_removal_dispute_submitted, benefit_removed_from_company, benefit_verified, cache_refresh, company_added, company_deleted, session_cleanup, user_deleted, user_registered';

-- Log this migration
INSERT INTO migration_log (id, migration_name, description) 
VALUES (15, '015-add-benefit-activity-events', 'Add benefit_added_to_company and benefit_removed_from_company event types to activity log');
