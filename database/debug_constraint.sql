-- Debug script to test constraint validation
-- This script helps identify why the constraint validation is failing

-- First, let's see all the data
SELECT 'Current activity_log data:' as info;
SELECT event_type, COUNT(*) FROM activity_log GROUP BY event_type ORDER BY event_type;

-- Check for any problematic rows
SELECT 'Checking for rows that would violate the constraint:' as info;
SELECT * FROM activity_log 
WHERE event_type NOT IN (
  'benefit_added_to_company',
  'benefit_automatically_removed',
  'benefit_disputed',
  'benefit_removal_dispute_approved',
  'benefit_removal_dispute_cancelled',
  'benefit_removal_dispute_rejected',
  'benefit_removal_dispute_submitted',
  'benefit_removed_from_company',
  'benefit_verified',
  'cache_refresh',
  'company_added',
  'session_cleanup',
  'user_deleted',
  'user_registered'
);

-- Check for encoding issues
SELECT 'Checking for encoding/whitespace issues:' as info;
SELECT 
  event_type,
  length(event_type) as len,
  octet_length(event_type) as byte_len,
  encode(event_type::bytea, 'hex') as hex_encoding
FROM activity_log 
GROUP BY event_type, length(event_type), octet_length(event_type), encode(event_type::bytea, 'hex')
ORDER BY event_type;

-- Test the constraint manually
SELECT 'Testing constraint logic manually:' as info;
SELECT 
  event_type,
  CASE 
    WHEN event_type IN (
      'benefit_added_to_company',
      'benefit_automatically_removed',
      'benefit_disputed',
      'benefit_removal_dispute_approved',
      'benefit_removal_dispute_cancelled',
      'benefit_removal_dispute_rejected',
      'benefit_removal_dispute_submitted',
      'benefit_removed_from_company',
      'benefit_verified',
      'cache_refresh',
      'company_added',
      'session_cleanup',
      'user_deleted',
      'user_registered'
    ) THEN 'PASS'
    ELSE 'FAIL'
  END as constraint_check
FROM activity_log
GROUP BY event_type;
