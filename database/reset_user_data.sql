-- =====================================================
-- BenefitLens Production Database User Data Reset Script
-- =====================================================
-- 
-- WARNING: This script will DELETE ALL USER-RELATED DATA from the database!
-- 
-- What this script PRESERVES:
-- - companies table and company_locations
-- - benefits table  
-- - benefit_categories table
-- - company_benefits table (company-benefit associations)
--
-- What this script DELETES:
-- - All user accounts and sessions
-- - All user interactions and analytics
-- - All authentication tokens and logs
-- - All user-generated content (saved companies, rankings, etc.)
-- - All cache and temporary data
--
-- IMPORTANT: 
-- 1. Make sure you have a database backup before running this!
-- 2. This script should only be run on production with explicit approval
-- 3. All users will need to re-register after this reset
-- 4. All analytics and user interaction history will be lost
--
-- =====================================================

-- Start transaction for safety
BEGIN;

-- Display current data counts before deletion
\echo '=== BEFORE DELETION - Current Data Counts ==='
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'companies', COUNT(*) FROM companies  
UNION ALL
SELECT 'benefits', COUNT(*) FROM benefits
UNION ALL
SELECT 'benefit_categories', COUNT(*) FROM benefit_categories
UNION ALL
SELECT 'company_benefits', COUNT(*) FROM company_benefits
UNION ALL
SELECT 'user_sessions', COUNT(*) FROM user_sessions
UNION ALL
SELECT 'saved_companies', COUNT(*) FROM saved_companies
UNION ALL
SELECT 'user_benefit_rankings', COUNT(*) FROM user_benefit_rankings
ORDER BY table_name;

\echo '=== STARTING USER DATA DELETION ==='

-- Step 1: Delete authentication and session related data
\echo 'Deleting authentication and session data...'
DELETE FROM magic_link_tokens;
DELETE FROM magic_link_rate_limits;
DELETE FROM auth_logs;
DELETE FROM csrf_tokens;
DELETE FROM user_sessions; -- This will cascade from users deletion anyway, but explicit for clarity
DELETE FROM session_activity;

-- Step 2: Delete user interaction and analytics data
\echo 'Deleting user interaction and analytics data...'
DELETE FROM benefit_search_interactions;
DELETE FROM company_page_views;
DELETE FROM search_queries;
DELETE FROM company_analytics_summary;
DELETE FROM daily_analytics_summary;

-- Step 3: Delete user-generated content and preferences
\echo 'Deleting user-generated content...'
DELETE FROM saved_companies;
DELETE FROM user_benefit_rankings;
DELETE FROM benefit_verifications;
DELETE FROM benefit_removal_disputes;
DELETE FROM missing_company_reports;

-- Step 4: Delete company user associations and verification tokens
\echo 'Deleting company user associations...'
DELETE FROM company_users;
DELETE FROM company_verification_tokens;

-- Step 5: Clear activity logs (contains user references)
\echo 'Clearing activity logs...'
DELETE FROM activity_log;

-- Step 6: Delete users table (this will cascade to remaining dependent tables)
\echo 'Deleting users table...'
DELETE FROM users;

-- Step 7: Clear cache and rate limiting data
\echo 'Clearing cache and rate limiting data...'
DELETE FROM cache_store;
DELETE FROM rate_limits;

-- Step 8: Reset sequences (optional - keeps IDs starting from 1)
\echo 'Resetting sequences...'
-- Note: UUIDs don't use sequences, but if there are any integer sequences:
ALTER SEQUENCE IF EXISTS migration_log_id_seq RESTART WITH 1;
ALTER SEQUENCE IF EXISTS session_config_id_seq RESTART WITH 1;

\echo '=== USER DATA DELETION COMPLETED ==='

-- Display data counts after deletion
\echo '=== AFTER DELETION - Remaining Data Counts ==='
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'companies', COUNT(*) FROM companies  
UNION ALL
SELECT 'benefits', COUNT(*) FROM benefits
UNION ALL
SELECT 'benefit_categories', COUNT(*) FROM benefit_categories
UNION ALL
SELECT 'company_benefits', COUNT(*) FROM company_benefits
UNION ALL
SELECT 'company_locations', COUNT(*) FROM company_locations
UNION ALL
SELECT 'user_sessions', COUNT(*) FROM user_sessions
UNION ALL
SELECT 'saved_companies', COUNT(*) FROM saved_companies
UNION ALL
SELECT 'user_benefit_rankings', COUNT(*) FROM user_benefit_rankings
UNION ALL
SELECT 'auth_logs', COUNT(*) FROM auth_logs
UNION ALL
SELECT 'activity_log', COUNT(*) FROM activity_log
ORDER BY table_name;

-- Verification queries
\echo '=== VERIFICATION ==='
\echo 'Checking that core business data is preserved:'

SELECT 
    'Companies preserved: ' || COUNT(*) as status 
FROM companies;

SELECT 
    'Benefits preserved: ' || COUNT(*) as status 
FROM benefits;

SELECT 
    'Benefit categories preserved: ' || COUNT(*) as status 
FROM benefit_categories;

SELECT 
    'Company-benefit associations preserved: ' || COUNT(*) as status 
FROM company_benefits;

\echo '=== RESET COMPLETE ==='
\echo 'Summary:'
\echo '- All user accounts and related data have been deleted'
\echo '- Companies, benefits, and categories have been preserved'
\echo '- Company-benefit associations have been preserved'
\echo '- All caches and temporary data have been cleared'
\echo '- The database is ready for fresh user registrations'

-- Commit the transaction
COMMIT;

\echo '=== TRANSACTION COMMITTED ==='
\echo 'Database reset completed successfully!'
