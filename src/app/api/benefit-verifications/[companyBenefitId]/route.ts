import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ companyBenefitId: string }> }
) => {
    const { companyBenefitId } = await params

    // Get all verifications for this company benefit
    const result = await query(
      'SELECT status FROM benefit_verifications WHERE company_benefit_id = $1',
      [companyBenefitId]
    )

    const verifications = result.rows

    if (!verifications || verifications.length === 0) {
      return NextResponse.json({
        confirmed: 0,
        disputed: 0,
        total: 0
      })
    }

    const confirmedCount = verifications.filter(v => v.status === 'confirmed').length
    const disputedCount = verifications.filter(v => v.status === 'disputed').length
    const totalCount = verifications.length

    return NextResponse.json({
      confirmed: confirmedCount,
      disputed: disputedCount,
      total: totalCount
    })
})
