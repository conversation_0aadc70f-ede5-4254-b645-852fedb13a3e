import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { withErrorHandling } from '@/lib/api-error-handler'

/**
 * GET /api/benefit-verifications/user/[companyBenefitId]
 * Check if the current user has verified a specific company benefit
 */
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ companyBenefitId: string }> }
) => {
  const { companyBenefitId } = await params

  // Check if benefit exists and get verification status
  const benefitResult = await query(
    'SELECT is_verified FROM company_benefits WHERE id = $1',
    [companyBenefitId]
  )

  if (benefitResult.rows.length === 0) {
    return NextResponse.json({
      hasVerified: false,
      isAdminVerified: false,
      verification: null,
      message: 'Company benefit not found'
    }, { status: 404 })
  }

  const isVerified = benefitResult.rows[0].is_verified

  // Determine if this is admin-verified by checking if it's verified but doesn't meet user criteria
  let isAdminVerified = false
  if (isVerified) {
    const verificationCounts = await query(
      'SELECT status FROM benefit_verifications WHERE company_benefit_id = $1',
      [companyBenefitId]
    )

    const confirmedCount = verificationCounts.rows.filter(v => v.status === 'confirmed').length
    const disputedCount = verificationCounts.rows.filter(v => v.status === 'disputed').length
    const meetsUserCriteria = confirmedCount >= 2 && confirmedCount > disputedCount

    isAdminVerified = isVerified && !meetsUserCriteria
  }

  // Check if user is authenticated
  const user = await getCurrentUser()
  if (!user) {
    return NextResponse.json({
      hasVerified: false,
      isAdminVerified,
      requiresAuth: true,
      message: 'Authentication required'
    })
  }

  // Check if user has verified this benefit
  const userVerificationResult = await query(
    'SELECT id, status, created_at FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2',
    [companyBenefitId, user.id]
  )

  const hasUserVerified = userVerificationResult.rows.length > 0
  const verification = hasUserVerified ? userVerificationResult.rows[0] : null

  return NextResponse.json({
    hasVerified: hasUserVerified || isAdminVerified,
    isAdminVerified,
    verification: verification ? {
      id: verification.id,
      status: verification.status,
      createdAt: verification.created_at
    } : null,
    message: isAdminVerified
      ? 'Benefit is admin-verified'
      : hasUserVerified
        ? 'User has verified this benefit'
        : 'User has not verified this benefit'
  })
})
