import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { withErrorHandling } from '@/lib/api-error-handler'

export const POST = withErrorHandling(async (request: NextRequest) => {
    // Prevent direct API scraping of verification data
    await requireWebUIOrAdmin(request)

    const body = await request.json()
    const { companyBenefitIds } = body

    if (!companyBenefitIds || !Array.isArray(companyBenefitIds) || companyBenefitIds.length === 0) {
      return NextResponse.json(
        { error: 'Missing or invalid companyBenefitIds array' },
        { status: 400 }
      )
    }

    // Limit to reasonable batch size to prevent abuse
    if (companyBenefitIds.length > 50) {
      return NextResponse.json(
        { error: 'Batch size too large. Maximum 50 company benefit IDs allowed.' },
        { status: 400 }
      )
    }

    try {
      // Get all verifications for the requested company benefits in one query
      const placeholders = companyBenefitIds.map((_, index) => `$${index + 1}`).join(',')
      const result = await query(
        `SELECT company_benefit_id, status FROM benefit_verifications WHERE company_benefit_id IN (${placeholders})`,
        companyBenefitIds
      )

      const verifications = result.rows

      // Group verifications by company_benefit_id and calculate counts
      const verificationCounts: Record<string, { confirmed: number; disputed: number; total: number }> = {}

      // Initialize all requested IDs with zero counts
      companyBenefitIds.forEach(id => {
        verificationCounts[id] = { confirmed: 0, disputed: 0, total: 0 }
      })

      // Count verifications for each company benefit
      verifications.forEach(verification => {
        const id = verification.company_benefit_id
        if (verificationCounts[id]) {
          verificationCounts[id].total++
          if (verification.status === 'confirmed') {
            verificationCounts[id].confirmed++
          } else if (verification.status === 'disputed') {
            verificationCounts[id].disputed++
          }
        }
      })

      return NextResponse.json(verificationCounts)

    } catch (error) {
      console.error('Error fetching batch verification counts:', error)
      return NextResponse.json(
        { error: 'Failed to fetch verification counts' },
        { status: 500 }
      )
    }
})
