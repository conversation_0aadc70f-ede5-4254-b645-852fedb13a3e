import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser, requireAuth } from '@/lib/auth'
import { sendEmail } from '@/lib/email'
import { query } from '@/lib/local-db'
import { withErrorHandling } from '@/lib/api-error-handler'
import { createMissingCompanyReportEmail } from '@/lib/email-generators'

interface AdminNotificationData {
  reportId: string
  userEmail: string
  emailDomain: string
  firstName?: string
  lastName?: string
}

function createAdminNotificationEmail(data: AdminNotificationData) {
  const { reportId, userEmail, emailDomain, firstName, lastName } = data
  return createMissingCompanyReportEmail(reportId, userEmail, emailDomain, firstName, lastName)
}

export const POST = withErrorHandling(async (request: NextRequest) => {
  await requireAuth()
    const user = await getCurrentUser()
    
    if (!user?.email) {
      return NextResponse.json(
        { error: 'User email not found' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { userEmail, emailDomain, firstName, lastName } = body

    if (!userEmail || !emailDomain) {
      return NextResponse.json(
        { error: 'User email and email domain are required' },
        { status: 400 }
      )
    }

    // Verify the user email matches the authenticated user
    if (userEmail.toLowerCase() !== user.email.toLowerCase()) {
      return NextResponse.json(
        { error: 'Email mismatch' },
        { status: 400 }
      )
    }

    // Check if there's already a recent report from this user for the same email domain
    const recentReportResult = await query(
      'SELECT id FROM missing_company_reports WHERE user_email = $1 AND email_domain = $2 AND created_at > NOW() - INTERVAL \'24 hours\'',
      [userEmail.toLowerCase(), emailDomain.toLowerCase()]
    )

    if (recentReportResult.rows.length > 0) {
      return NextResponse.json(
        { error: 'You have already submitted a report for this company domain in the last 24 hours. Please wait before submitting another report.' },
        { status: 429 }
      )
    }

    // Store the missing company report
    const reportResult = await query(
      `INSERT INTO missing_company_reports (user_email, email_domain, first_name, last_name, status, created_at)
       VALUES ($1, $2, $3, $4, 'pending', NOW()) RETURNING id`,
      [userEmail.toLowerCase(), emailDomain.toLowerCase(), firstName || null, lastName || null]
    )

    const reportId = reportResult.rows[0].id

    // Send notification email to admin
    const adminEmailOptions = createAdminNotificationEmail({
      reportId,
      userEmail,
      emailDomain,
      firstName,
      lastName
    })

    await sendEmail(adminEmailOptions)

    console.log('✅ Missing company report submitted:', { reportId, userEmail, emailDomain })

    return NextResponse.json({
      success: true,
      message: 'Thank you! We have received your report and will review it shortly. You will be notified via email once your company is added.',
      reportId
    })
})
