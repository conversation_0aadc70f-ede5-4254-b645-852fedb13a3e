import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { getCurrentUser } from '@/lib/auth'
import { withErrorHandling } from '@/lib/api-error-handler'

// GET /api/user/benefit-rankings/[benefitId] - Get user's ranking for a specific benefit
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ benefitId: string }> }
) => {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { benefitId } = await params

    const result = await query(
      `SELECT 
        ubr.id,
        ubr.user_id,
        ubr.benefit_id,
        ubr.ranking,
        ubr.created_at,
        ubr.updated_at,
        b.name as benefit_name,
        b.category,
        b.icon,
        b.description
      FROM user_benefit_rankings ubr
      LEFT JOIN benefits b ON ubr.benefit_id = b.id
      WHERE ubr.user_id = $1 AND ubr.benefit_id = $2`,
      [user.id, benefitId]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Ranking not found' },
        { status: 404 }
      )
    }

    const row = result.rows[0]
    const ranking = {
      id: row.id,
      user_id: row.user_id,
      benefit_id: row.benefit_id,
      ranking: row.ranking,
      created_at: row.created_at,
      updated_at: row.updated_at,
      benefit: {
        id: row.benefit_id,
        name: row.benefit_name,
        category: row.category,
        icon: row.icon,
        description: row.description
      }
    }

    return NextResponse.json(ranking)
})

// PUT /api/user/benefit-rankings/[benefitId] - Update user's ranking for a specific benefit
export const PUT = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ benefitId: string }> }
) => {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { benefitId } = await params
    const body = await request.json()
    const { ranking } = body

    if (typeof ranking !== 'number' || ranking < 1 || ranking > 10) {
      return NextResponse.json(
        { error: 'Ranking must be a number between 1 and 10' },
        { status: 400 }
      )
    }

    // Verify benefit exists
    const benefitCheck = await query(
      'SELECT id FROM benefits WHERE id = $1',
      [benefitId]
    )

    if (benefitCheck.rows.length === 0) {
      return NextResponse.json(
        { error: 'Benefit not found' },
        { status: 404 }
      )
    }

    // Check if another benefit already has this ranking
    const existingRanking = await query(
      'SELECT benefit_id FROM user_benefit_rankings WHERE user_id = $1 AND ranking = $2 AND benefit_id != $3',
      [user.id, ranking, benefitId]
    )

    if (existingRanking.rows.length > 0) {
      return NextResponse.json(
        { error: `Ranking ${ranking} is already assigned to another benefit` },
        { status: 400 }
      )
    }

    // Upsert the ranking
    const result = await query(
      `INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking)
       VALUES ($1, $2, $3)
       ON CONFLICT (user_id, benefit_id)
       DO UPDATE SET ranking = $3, updated_at = NOW()
       RETURNING *`,
      [user.id, benefitId, ranking]
    )

    return NextResponse.json({
      success: true,
      ranking: result.rows[0]
    })
})

// POST /api/user/benefit-rankings/[benefitId] - Create/update user's ranking for a specific benefit (alias for PUT)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ benefitId: string }> }
) {
  return PUT(request, { params })
}

// DELETE /api/user/benefit-rankings/[benefitId] - Delete user's ranking for a specific benefit
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ benefitId: string }> }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { benefitId } = await params

    const result = await query(
      'DELETE FROM user_benefit_rankings WHERE user_id = $1 AND benefit_id = $2',
      [user.id, benefitId]
    )

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: 'Ranking not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Benefit ranking deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting benefit ranking:', error)
    return NextResponse.json(
      { error: 'Failed to delete benefit ranking' },
      { status: 500 }
    )
  }
}
