import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { withErrorHandling } from '@/lib/api-error-handler'

// Helper function to get client IP address
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')

  return forwarded?.split(',')[0]?.trim() ||
         realIp ||
         cfConnectingIp ||
         'unknown'
}

export const GET = withErrorHandling(async (request: NextRequest) => {
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Collect all user data from various tables
    const userData: {
      user: {
        id: string
        email: string
        firstName: string | null
        lastName: string | null
        emailVerified: boolean
        role: string
        paymentStatus: 'free' | 'paying'
        companyId: string | null | undefined
        createdAt: string
        updatedAt: string
      }
      exportInfo: {
        exportDate: string
        exportVersion: string
        dataTypes: string[]
      }
      savedCompanies?: any[]
      benefitVerifications?: any[]
      benefitDisputes?: any[]
      analyticsData?: any
      benefitRankings?: any[]
      authLogs?: any
      dataExportHistory?: any
      privacyNotice?: any
    } = {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        emailVerified: user.emailVerified,
        role: user.role,
        paymentStatus: user.paymentStatus,
        companyId: user.companyId,
        createdAt: user.createdAt,
        updatedAt: user.createdAt // LocalUser doesn't have updatedAt, using createdAt
      },
      exportInfo: {
        exportDate: new Date().toISOString(),
        exportVersion: '1.0',
        dataTypes: []
      }
    }

    // Get saved companies
    try {
      const savedCompaniesResult = await query(`
        SELECT sc.*, c.name as company_name, c.domain, c.industry
        FROM saved_companies sc
        JOIN companies c ON sc.company_id = c.id
        WHERE sc.user_id = $1
        ORDER BY sc.created_at DESC
      `, [user.id])
      
      userData.exportInfo.dataTypes.push('saved_companies')
      userData.savedCompanies = savedCompaniesResult.rows
    } catch (error) {
      console.error('Error fetching saved companies:', error)
    }

    // Get benefit verifications
    try {
      const verificationsResult = await query(`
        SELECT bv.*, cb.company_id, c.name as company_name, b.name as benefit_name
        FROM benefit_verifications bv
        JOIN company_benefits cb ON bv.company_benefit_id = cb.id
        JOIN companies c ON cb.company_id = c.id
        JOIN benefits b ON cb.benefit_id = b.id
        WHERE bv.user_id = $1
        ORDER BY bv.created_at DESC
      `, [user.id])
      
      userData.exportInfo.dataTypes.push('benefit_verifications')
      userData.benefitVerifications = verificationsResult.rows
    } catch (error) {
      console.error('Error fetching benefit verifications:', error)
    }

    // Get benefit disputes
    try {
      const disputesResult = await query(`
        SELECT brd.*, cb.company_id, c.name as company_name, b.name as benefit_name
        FROM benefit_removal_disputes brd
        JOIN company_benefits cb ON brd.company_benefit_id = cb.id
        JOIN companies c ON cb.company_id = c.id
        JOIN benefits b ON cb.benefit_id = b.id
        WHERE brd.user_id = $1
        ORDER BY brd.created_at DESC
      `, [user.id])
      
      userData.exportInfo.dataTypes.push('benefit_disputes')
      userData.benefitDisputes = disputesResult.rows
    } catch (error) {
      console.error('Error fetching benefit disputes:', error)
    }

    // Get analytics data with IP addresses (full data for user export)
    try {
      const analyticsResult = await query(`
        SELECT
          'company_view' as event_type,
          company_id,
          ip_address,
          user_agent,
          referrer,
          created_at
        FROM company_page_views
        WHERE user_id = $1
        UNION ALL
        SELECT
          'search' as event_type,
          NULL as company_id,
          ip_address,
          user_agent,
          NULL as referrer,
          created_at
        FROM search_queries
        WHERE user_id = $1
        UNION ALL
        SELECT
          'benefit_interaction' as event_type,
          company_id,
          NULL as ip_address,
          NULL as user_agent,
          interaction_type as referrer,
          created_at
        FROM benefit_search_interactions
        WHERE user_id = $1
        ORDER BY created_at DESC
        LIMIT 1000
      `, [user.id])

      userData.exportInfo.dataTypes.push('analytics_with_ip_data')
      userData.analyticsData = {
        totalEvents: analyticsResult.rows.length,
        events: analyticsResult.rows,
        privacyNote: "This includes IP addresses and user agent data collected for analytics purposes. IP addresses are anonymized after 12 months."
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error)
    }

    // Get user rankings if they exist
    try {
      const rankingsResult = await query(`
        SELECT ur.*, b.name as benefit_name
        FROM user_benefit_rankings ur
        JOIN benefits b ON ur.benefit_id = b.id
        WHERE ur.user_id = $1
        ORDER BY ur.ranking ASC
      `, [user.id])
      
      if (rankingsResult.rows.length > 0) {
        userData.exportInfo.dataTypes.push('benefit_rankings')
        userData.benefitRankings = rankingsResult.rows
      }
    } catch (error) {
      console.error('Error fetching user rankings:', error)
    }

    // Get auth logs with full IP data (last 100 entries)
    try {
      const authLogsResult = await query(`
        SELECT
          event_type,
          status,
          created_at,
          ip_address,
          user_agent,
          error_type,
          error_message,
          failure_reason
        FROM auth_logs
        WHERE email = $1
        ORDER BY created_at DESC
        LIMIT 100
      `, [user.email])

      userData.exportInfo.dataTypes.push('auth_logs_with_ip')
      userData.authLogs = {
        entries: authLogsResult.rows,
        privacyNote: "Authentication logs include IP addresses for security purposes. These are retained indefinitely for fraud prevention.",
        totalEntries: authLogsResult.rows.length
      }
    } catch (error) {
      console.error('Error fetching auth logs:', error)
    }

    // Get data export logs (user's own export history)
    try {
      const exportLogsResult = await query(`
        SELECT
          export_type,
          ip_address,
          user_agent,
          created_at,
          file_size_bytes,
          data_types
        FROM data_export_logs
        WHERE user_id = $1
        ORDER BY created_at DESC
        LIMIT 50
      `, [user.id])

      if (exportLogsResult.rows.length > 0) {
        userData.exportInfo.dataTypes.push('data_export_logs')
        userData.dataExportHistory = {
          exports: exportLogsResult.rows,
          privacyNote: "Export logs track when you've downloaded your data, including IP addresses for compliance purposes.",
          totalExports: exportLogsResult.rows.length
        }
      }
    } catch (error) {
      console.error('Error fetching data export logs:', error)
    }

    // Add privacy notice with IP address information
    userData.privacyNotice = {
      message: "This export contains all personal data we have stored about you in accordance with GDPR Article 15 (Right of Access), including IP addresses.",
      ipAddressNotice: {
        collection: "We collect IP addresses for security, analytics, and compliance purposes.",
        retention: {
          security: "Authentication logs with IP addresses are retained indefinitely for fraud prevention.",
          analytics: "Analytics data with IP addresses is anonymized after 12 months.",
          compliance: "Data export logs with IP addresses are retained for 3 years for compliance."
        },
        anonymization: "For analytics purposes, we anonymize IP addresses by removing the last octet (IPv4) or last 64 bits (IPv6).",
        legalBasis: {
          security: "Legitimate interest (Art. 6(1)(f) GDPR)",
          analytics: "Consent (Art. 6(1)(a) GDPR) - can be withdrawn",
          compliance: "Legal obligation (Art. 6(1)(c) GDPR)"
        }
      },
      dataRetention: "Most data is retained for the duration of your account. Analytics data with IP addresses is anonymized after 12 months.",
      contact: "For questions about your data or IP address deletion requests, contact <EMAIL>",
      rights: [
        "Right to rectification (Article 16)",
        "Right to erasure (Article 17) - limited for security logs",
        "Right to restrict processing (Article 18)",
        "Right to data portability (Article 20)",
        "Right to object (Article 21) - especially for analytics"
      ]
    }

    // Log the data export for compliance
    const clientIP = getClientIP(request)
    const userAgent = request.headers.get('user-agent') || 'unknown'
    const exportDataSize = JSON.stringify(userData).length

    try {
      await query(`
        INSERT INTO data_export_logs (
          user_id, email, export_type, ip_address, user_agent,
          file_size_bytes, data_types
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        user.id,
        user.email,
        'full',
        clientIP,
        userAgent,
        exportDataSize,
        JSON.stringify(userData.exportInfo.dataTypes)
      ])
    } catch (error) {
      console.error('Error logging data export:', error)
      // Don't fail the export if logging fails
    }

    // Set headers for file download
    const filename = `benefitlens-data-export-${user.id}-${new Date().toISOString().split('T')[0]}.json`

    return new NextResponse(JSON.stringify(userData, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })
})
