import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { checkCacheHealth } from '@/lib/postgresql-cache'
import { withErrorHandling } from '@/lib/api-error-handler'

// Simple health check for load balancers and basic monitoring
// Returns 200 OK if basic services are working, 503 if not
export const GET = withErrorHandling(async (_request: NextRequest) => {
    // Quick database check
    await query('SELECT 1')
    
    // Quick cache check
    const cacheHealthy = await checkCacheHealth()

    if (!cacheHealthy) {
      return NextResponse.json(
        { status: 'unhealthy', error: 'Cache unavailable' },
        { status: 503 }
      )
    }
    
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString()
    })
})
