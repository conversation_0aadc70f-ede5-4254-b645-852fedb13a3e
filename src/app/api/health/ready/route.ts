import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { checkCacheHealth } from '@/lib/postgresql-cache'
import { withErrorHandling } from '@/lib/api-error-handler'

// Readiness check - indicates if the application is ready to serve traffic
// This is more thorough than the liveness check and includes dependency checks
export const GET = withErrorHandling(async (_request: NextRequest) => {
  const checks = []
  let allReady = true
    // Database readiness check
    const dbStart = Date.now()
    try {
      // Test database with a more comprehensive query
      const dbResult = await query(`
        SELECT 
          COUNT(*) as table_count
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `)
      
      const dbTime = Date.now() - dbStart
      const tableCount = parseInt(dbResult.rows[0]?.table_count || '0')
      
      if (tableCount < 10) { // We expect at least 10 tables
        checks.push({
          name: 'database',
          status: 'not_ready',
          error: `Insufficient tables: ${tableCount}`,
          responseTime: dbTime
        })
        allReady = false
      } else {
        checks.push({
          name: 'database',
          status: 'ready',
          responseTime: dbTime,
          details: { tableCount }
        })
      }
    } catch (error) {
      checks.push({
        name: 'database',
        status: 'not_ready',
        error: error instanceof Error ? error.message : 'Database error'
      })
      allReady = false
    }
    
    // Cache readiness check
    const cacheStart = Date.now()
    try {
      const cacheHealthy = await checkCacheHealth()
      const cacheTime = Date.now() - cacheStart

      if (!cacheHealthy) {
        checks.push({
          name: 'cache',
          status: 'not_ready',
          error: 'Cache health check failed',
          responseTime: cacheTime
        })
        allReady = false
      } else {
        checks.push({
          name: 'cache',
          status: 'ready',
          responseTime: cacheTime
        })
      }
    } catch (error) {
      checks.push({
        name: 'cache',
        status: 'not_ready',
        error: error instanceof Error ? error.message : 'Cache error'
      })
      allReady = false
    }
    
    // Application readiness check
    const appStart = Date.now()
    try {
      // Check if essential environment variables are set
      const requiredEnvVars = [
        'DATABASE_URL'
      ]
      
      const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar])
      const appTime = Date.now() - appStart
      
      if (missingEnvVars.length > 0) {
        checks.push({
          name: 'application',
          status: 'not_ready',
          error: `Missing environment variables: ${missingEnvVars.join(', ')}`,
          responseTime: appTime
        })
        allReady = false
      } else {
        checks.push({
          name: 'application',
          status: 'ready',
          responseTime: appTime,
          details: {
            nodeVersion: process.version,
            uptime: process.uptime()
          }
        })
      }
    } catch (error) {
      checks.push({
        name: 'application',
        status: 'not_ready',
        error: error instanceof Error ? error.message : 'Application error'
      })
      allReady = false
    }
    
    const response = {
      status: allReady ? 'ready' : 'not_ready',
      timestamp: new Date().toISOString(),
      checks
    }
    
    return NextResponse.json(response, {
      status: allReady ? 200 : 503
    })
})
