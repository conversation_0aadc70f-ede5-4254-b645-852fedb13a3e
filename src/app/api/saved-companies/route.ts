import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { withErrorHandling } from '@/lib/api-error-handler'
import { query } from '@/lib/local-db'

// GET /api/saved-companies - Get user's saved companies
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Prevent direct API scraping of company data through saved companies
  await requireWebUIOrAdmin(request)
  const userId = await requireAuth()

  const result = await query(`
    SELECT
      sc.id as saved_id,
      sc.created_at as saved_at,
      c.id,
      c.name,
      c.industry,
      c.size,
      c.description,
      c.career_url,
      c.domain,
      COUNT(cb.id) as benefit_count,
      -- Get primary location info as a simple string for backward compatibility
      (SELECT cl.city || CASE WHEN cl.country IS NOT NULL THEN ', ' || cl.country ELSE '' END
       FROM company_locations cl
       WHERE cl.company_id = c.id AND cl.is_primary = true
       LIMIT 1) as primary_location
    FROM saved_companies sc
    JOIN companies c ON sc.company_id = c.id
    LEFT JOIN company_benefits cb ON c.id = cb.company_id
    WHERE sc.user_id = $1
    GROUP BY sc.id, sc.created_at, c.id, c.name, c.industry, c.size, c.description, c.career_url, c.domain
    ORDER BY sc.created_at DESC
  `, [userId])

  // Transform the data to include location in the expected format
  const transformedRows = result.rows.map(row => ({
    ...row,
    // Add locations array for compatibility with CompanyCard component
    locations: row.primary_location ? [{
      city: row.primary_location.split(', ')[0],
      country: row.primary_location.split(', ')[1] || null,
      location_normalized: row.primary_location,
      location_raw: row.primary_location,
      is_primary: true
    }] : []
  }))

  return NextResponse.json(transformedRows)
})

// POST /api/saved-companies - Save a company
export const POST = withErrorHandling(async (request: NextRequest) => {
  const userId = await requireAuth()
  const { companyId } = await request.json()

  if (!companyId) {
    return NextResponse.json(
      { error: 'Company ID is required' },
      { status: 400 }
    )
  }

  // Check if company exists
  const companyResult = await query(
    'SELECT id FROM companies WHERE id = $1',
    [companyId]
  )

  if (companyResult.rows.length === 0) {
    return NextResponse.json(
      { error: 'Company not found' },
      { status: 404 }
    )
  }

  // Check if already saved
  const existingResult = await query(
    'SELECT id FROM saved_companies WHERE user_id = $1 AND company_id = $2',
    [userId, companyId]
  )

  if (existingResult.rows.length > 0) {
    return NextResponse.json(
      { error: 'Company already saved' },
      { status: 400 }
    )
  }

  // Save the company
  const result = await query(
    'INSERT INTO saved_companies (user_id, company_id) VALUES ($1, $2) RETURNING *',
    [userId, companyId]
  )

  return NextResponse.json(result.rows[0], { status: 201 })
})

// DELETE /api/saved-companies - Remove a saved company
export const DELETE = withErrorHandling(async (request: NextRequest) => {
  const userId = await requireAuth()
  const { searchParams } = new URL(request.url)
  const companyId = searchParams.get('companyId')

  if (!companyId) {
    return NextResponse.json(
      { error: 'Company ID is required' },
      { status: 400 }
    )
  }

  const result = await query(
    'DELETE FROM saved_companies WHERE user_id = $1 AND company_id = $2 RETURNING *',
    [userId, companyId]
  )

  if (result.rows.length === 0) {
    return NextResponse.json(
      { error: 'Saved company not found' },
      { status: 404 }
    )
  }

  return NextResponse.json({ message: 'Company removed from saved list' })
})
