import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { withErrorHandling } from '@/lib/api-error-handler'

// GET /api/saved-companies/[companyId] - Check if company is saved by current user
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) => {
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json({ saved: false })
    }

    const { companyId } = await params
    const result = await query(
      'SELECT id FROM saved_companies WHERE user_id = $1 AND company_id = $2',
      [user.id, companyId]
    )

    return NextResponse.json({ saved: result.rows.length > 0 })
})
