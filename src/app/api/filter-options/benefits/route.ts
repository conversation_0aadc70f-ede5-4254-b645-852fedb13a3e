import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (request: NextRequest) => {
  // Allow web UI access for all users, block direct API access except for admins
  await requireWebUIOrAdmin(request)

  // Get all benefits with count of companies offering each benefit
  const sql = `
    SELECT
      b.name,
      bc.name as category,
      b.icon,
      COUNT(DISTINCT cb.company_id) as company_count
    FROM benefits b
    LEFT JOIN benefit_categories bc ON b.category_id = bc.id
    LEFT JOIN company_benefits cb ON b.id = cb.benefit_id
    GROUP BY b.id, b.name, bc.name, b.icon
    ORDER BY company_count DESC, b.name ASC
  `

  const result = await query(sql)

  const benefits = result.rows.map(row => ({
    value: row.name,
    label: row.name,
    count: parseInt(row.company_count),
    category: row.category,
    icon: row.icon
  }))

  return NextResponse.json(benefits)
})
