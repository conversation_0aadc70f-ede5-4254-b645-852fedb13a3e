import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (request: NextRequest) => {
  // Allow web UI access for all users, block direct API access except for admins
  await requireWebUIOrAdmin(request)

  // Get all unique industries with count of companies in each industry
  const sql = `
    SELECT
      industry,
      COUNT(*) as company_count
    FROM companies
    WHERE industry IS NOT NULL AND industry != ''
    GROUP BY industry
    ORDER BY company_count DESC, industry ASC
  `

  const result = await query(sql)

  const industries = result.rows.map(row => ({
    value: row.industry,
    label: row.industry,
    count: parseInt(row.company_count)
  }))

  return NextResponse.json(industries)
})
