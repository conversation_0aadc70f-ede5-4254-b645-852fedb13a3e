import { NextRequest, NextResponse } from 'next/server'
import { getSession } from '@/lib/session-storage'
import { withErrorHandling } from '@/lib/api-error-handler'

export const POST = withErrorHandling(async (request: NextRequest) => {
  const { sessionToken } = await request.json()

  if (!sessionToken) {
    return NextResponse.json(
      { error: 'Session token required' },
      { status: 400 }
    )
  }

  // Validate session using unified session storage
  const session = await getSession(sessionToken)

  if (!session) {
    return NextResponse.json(
      { error: 'Invalid or expired session' },
      { status: 401 }
    )
  }

  return NextResponse.json({
    session: {
      userId: session.userId
    }
  })
})
