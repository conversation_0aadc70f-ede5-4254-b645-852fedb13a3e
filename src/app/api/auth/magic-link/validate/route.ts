import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { getRequestContext } from '@/lib/logger'
import { withErrorHandling } from '@/lib/api-error-handler'

export const POST = withErrorHandling(async (request: NextRequest) => {
  const requestContext = getRequestContext(request)
    const body = await request.json()
    const { token } = body

    console.log('🔍 Magic link validation attempt:', { token: token?.substring(0, 8) + '...' })

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      )
    }

    // Check if token exists and is not expired, but don't mark it as used
    const tokenResult = await query(
      'SELECT * FROM magic_link_tokens WHERE token = $1 AND used_at IS NULL',
      [token]
    )

    if (tokenResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Invalid or expired magic link' },
        { status: 401 }
      )
    }

    const tokenData = tokenResult.rows[0]

    // Check if token is expired
    if (new Date(tokenData.expires_at) < new Date()) {
      return NextResponse.json(
        { error: 'Magic link has expired' },
        { status: 401 }
      )
    }

    console.log('✅ Magic link validation successful (not consumed)')

    return NextResponse.json({
      success: true,
      message: 'Token is valid'
    })
})
