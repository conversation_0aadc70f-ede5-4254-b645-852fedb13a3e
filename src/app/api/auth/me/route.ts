import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/local-auth'
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (_request: NextRequest) => {
  const user = await getCurrentUser()

  if (!user) {
    return NextResponse.json(
      { error: 'Not authenticated' },
      { status: 401 }
    )
  }

  // Extract company domain from email
  const emailDomain = user.email.split('@')[1]

  return NextResponse.json({
    user: {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      paymentStatus: user.paymentStatus,
      company_domain: emailDomain,
      is_premium: user.paymentStatus === 'paying',
      // Include impersonation information
      isImpersonation: user.isImpersonation || false,
      originalAdminId: user.originalAdminId || null,
      originalAdminEmail: user.originalAdminEmail || null
    }
  })
})
