import { NextRequest, NextResponse } from 'next/server'
import { bulkAddCompanyBenefits, bulkRemoveCompanyBenefits } from '@/lib/database'
import { requireAuth, canUserManageCompany } from '@/lib/auth'
import { withErrorHandling } from '@/lib/api-error-handler'

export const POST = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
    const userId = await requireAuth()
    const { id: companyId } = await params
    
    const canManage = await canUserManageCompany(companyId)
    if (!canManage) {
      return NextResponse.json(
        { error: 'You can only manage benefits for companies that match your email domain' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { benefitIds, action } = body

    if (!benefitIds || !Array.isArray(benefitIds) || benefitIds.length === 0) {
      return NextResponse.json(
        { error: 'Benefit IDs array is required' },
        { status: 400 }
      )
    }

    if (!action || !['add', 'remove'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be either "add" or "remove"' },
        { status: 400 }
      )
    }

    let result
    if (action === 'add') {
      result = await bulkAddCompanyBenefits(companyId, benefitIds, userId)
    } else {
      await bulkRemoveCompanyBenefits(companyId, benefitIds)
      result = { success: true, removed: benefitIds.length }
    }

    return NextResponse.json(result)
})
