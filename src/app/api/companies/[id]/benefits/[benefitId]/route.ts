import { NextRequest, NextResponse } from 'next/server'
import { removeCompanyBenefit } from '@/lib/database'
import { getCurrentUser, canUserManageCompany } from '@/lib/auth'
import { withErrorHandling } from '@/lib/api-error-handler'

/**
 * DELETE /api/companies/[id]/benefits/[benefitId]
 * Remove a specific benefit from a company
 */
export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string; benefitId: string }> }
) => {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const { id: companyId, benefitId } = await params

    // Check if user can manage this company based on email domain
    const canManage = await canUserManageCompany(companyId)
    if (!canManage) {
      return NextResponse.json(
        { error: 'You can only manage benefits for companies that match your email domain' },
        { status: 403 }
      )
    }

    await removeCompanyBenefit(companyId, benefitId)
    return NextResponse.json({ success: true })
})
