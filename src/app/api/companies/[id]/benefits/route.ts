import { NextRequest, NextResponse } from 'next/server'
import { addCompanyBenefit, removeCompanyBenefit, getCompanyBenefits } from '@/lib/database'
import { getCurrentUser, canUserManageCompany, requireAuth } from '@/lib/auth'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  // Allow web UI access for all users, block direct API access except for admins
  await requireWebUIOrAdmin(request)

  const { id: companyId } = await params
  const benefits = await getCompanyBenefits(companyId)
  return NextResponse.json(benefits)
})

export const POST = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const user = await getCurrentUser()
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const { id: companyId } = await params

  // Check if user can manage this company based on email domain
  const canManage = await canUserManageCompany(companyId)
  if (!canManage) {
    return NextResponse.json(
      { error: 'You can only manage benefits for companies that match your email domain' },
      { status: 403 }
    )
  }

  const body = await request.json()
  const { benefitId, benefitIds } = body

  // Support both single benefitId and multiple benefitIds
  const idsToAdd = benefitIds || (benefitId ? [benefitId] : [])

  if (!idsToAdd || idsToAdd.length === 0) {
    return NextResponse.json(
      { error: 'Benefit ID(s) required' },
      { status: 400 }
    )
  }

  // Add multiple benefits
  const added = []
  for (const id of idsToAdd) {
    try {
      const benefit = await addCompanyBenefit(companyId, id, user.id)
      added.push(benefit)
    } catch (error) {
      console.error(`Error adding benefit ${id}:`, error)
      // Continue with other benefits even if one fails
    }
  }

  return NextResponse.json({
    success: true,
    added,
    count: added.length
  }, { status: 200 })
})

export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const user = await getCurrentUser()
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const { id: companyId } = await params

  // Check if user can manage this company based on email domain
  const canManage = await canUserManageCompany(companyId)
  if (!canManage) {
    return NextResponse.json(
      { error: 'You can only manage benefits for companies that match your email domain' },
      { status: 403 }
    )
  }

  const { searchParams } = new URL(request.url)
  const benefitId = searchParams.get('benefitId')

  if (!benefitId) {
    return NextResponse.json(
      { error: 'Benefit ID is required' },
      { status: 400 }
    )
  }

  await removeCompanyBenefit(companyId, benefitId)
  return NextResponse.json({ success: true })
})
