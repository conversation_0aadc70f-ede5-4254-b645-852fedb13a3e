import { NextRequest, NextResponse } from 'next/server'
import { getCompanyLocations, addCompanyLocation, updateCompanyLocation, removeCompanyLocation } from '@/lib/database'
import { requireAuth, canUserManageCompany } from '@/lib/auth'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { withErrorHandling } from '@/lib/api-error-handler'
import type { LocationType } from '@/types/database'

export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  // Allow web UI access for all users, block direct API access except for admins
  await requireWebUIOrAdmin(request)

  const { id: companyId } = await params
  const locations = await getCompanyLocations(companyId)
  return NextResponse.json(locations)
})

export const POST = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const _userId = await requireAuth()
  const { id: companyId } = await params

  // Check if user can manage this company
  const canManage = await canUserManageCompany(companyId)
  if (!canManage) {
    return NextResponse.json(
      { error: 'Not authorized to manage this company' },
      { status: 403 }
    )
  }

  const body = await request.json()
  const { location_raw, location_type = 'office', is_primary = false, is_headquarters = false } = body

  if (!location_raw) {
    return NextResponse.json(
      { error: 'Location is required' },
      { status: 400 }
    )
  }

  const location = await addCompanyLocation(
    companyId,
    location_raw,
    location_type as LocationType,
    is_primary,
    is_headquarters
  )

  return NextResponse.json(location, { status: 201 })
})

export const PATCH = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const _userId = await requireAuth()
  const { id: companyId } = await params

  // Check if user can manage this company
  const canManage = await canUserManageCompany(companyId)
  if (!canManage) {
    return NextResponse.json(
      { error: 'Not authorized to manage this company' },
      { status: 403 }
    )
  }

  const body = await request.json()
  const { location_id, ...updates } = body

  if (!location_id) {
    return NextResponse.json(
      { error: 'Location ID is required' },
      { status: 400 }
    )
  }

  const location = await updateCompanyLocation(location_id, updates)
  return NextResponse.json(location)
})

export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const _userId = await requireAuth()
  const { id: companyId } = await params

  // Check if user can manage this company
  const canManage = await canUserManageCompany(companyId)
  if (!canManage) {
    return NextResponse.json(
      { error: 'Not authorized to manage this company' },
      { status: 403 }
    )
  }

  const { searchParams } = new URL(request.url)
  const locationId = searchParams.get('location_id')

  if (!locationId) {
    return NextResponse.json(
      { error: 'Location ID is required' },
      { status: 400 }
    )
  }

  await removeCompanyLocation(locationId)
  return NextResponse.json({ success: true })
})
