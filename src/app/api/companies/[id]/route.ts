import { NextRequest, NextResponse } from 'next/server'
import { getCompanyById } from '@/lib/database'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  // Allow web UI access for all users, block direct API access except for admins
  await requireWebUIOrAdmin(request)

  const { id } = await params
  const { searchParams } = new URL(request.url)
  const bypassCache = searchParams.get('fresh') === 'true'

  // Validate UUID format - treat invalid UUIDs as not found
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
  if (!uuidRegex.test(id)) {
    return NextResponse.json(
      { error: 'Company not found' },
      { status: 404 }
    )
  }

  const company = await getCompanyById(id, bypassCache)

  if (!company) {
    return NextResponse.json(
      { error: 'Company not found' },
      { status: 404 }
    )
  }

  const response = NextResponse.json(company)

  // Disable caching to ensure fresh data
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
  response.headers.set('Pragma', 'no-cache')
  response.headers.set('Expires', '0')

  return response
})

export async function PATCH(
  _request: NextRequest,
  { params: _params }: { params: Promise<{ id: string }> }
) {
  // Company editing by users is no longer allowed
  // Only admins can edit companies through the admin dashboard
  return NextResponse.json(
    { error: 'Company editing is restricted to administrators' },
    { status: 403 }
  )
}
