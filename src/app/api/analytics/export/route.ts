import { NextRequest, NextResponse } from 'next/server'
// import { requireAuth } from '@/lib/auth'
import { requireFullAnalyticsAccess } from '@/lib/analytics-access-control'
import { withErrorHandling } from '@/lib/api-error-handler'

export const POST = withErrorHandling(async (request: NextRequest) => {
  // Require full analytics access for export functionality
  await requireFullAnalyticsAccess()

    const body = await request.json()
    const { type, format, data, filters } = body

    if (!type || !format || !data) {
      return NextResponse.json(
        { error: 'Type, format, and data are required' },
        { status: 400 }
      )
    }

    const timestamp = new Date().toISOString().split('T')[0]
    let filename = `workwell_${type}_${timestamp}`
    let content = ''
    let contentType = ''

    if (format === 'csv') {
      contentType = 'text/csv'
      filename += '.csv'

      if (type === 'search_trends') {
        const headers = 'Rank,Search Term,Category,Search Count,Trend Score,Change %\n'
        const rows = data.trends.map((trend: { rank: number; search_term: string; category: string; search_count: number; trend_score: number; change: number }) =>
          `${trend.rank},"${trend.search_term}","${trend.category}",${trend.search_count},${trend.trend_score},${trend.change}`
        ).join('\n')
        content = headers + rows
      } else if (type === 'top_companies') {
        const headers = 'Rank,Company Name,Location,Industry,View Count,Benefit Count,Verified Benefits,Engagement Rate\n'
        const rows = data.companies.map((company: { rank: number; name: string; location: string; industry: string; view_count: number; benefit_count: number; verified_benefit_count: number; engagement_rate: number }) =>
          `${company.rank},"${company.name}","${company.location}","${company.industry}",${company.view_count},${company.benefit_count},${company.verified_benefit_count},${company.engagement_rate}%`
        ).join('\n')
        content = headers + rows
      } else if (type === 'company_analytics') {
        const headers = 'Benefit Name,Category,Verified,Views,Interactions,Performance Score\n'
        const rows = data.benefit_performance.map((benefit: { name: string; category: string; is_verified: boolean; views: number; interactions: number; performance_score: number }) =>
          `"${benefit.name}","${benefit.category}",${benefit.is_verified},${benefit.views},${benefit.interactions},${benefit.performance_score}`
        ).join('\n')
        content = headers + rows
      }
    } else if (format === 'json') {
      contentType = 'application/json'
      filename += '.json'
      
      const exportData = {
        export_info: {
          type,
          generated_at: new Date().toISOString(),
          filters: filters || {},
          total_records: Array.isArray(data.trends) ? data.trends.length : 
                        Array.isArray(data.companies) ? data.companies.length :
                        Array.isArray(data.benefit_performance) ? data.benefit_performance.length : 0
        },
        data
      }
      
      content = JSON.stringify(exportData, null, 2)
    } else if (format === 'pdf') {
      // For PDF, we'll return a JSON response with instructions
      // In a real implementation, you'd use a PDF library like puppeteer or jsPDF
      return NextResponse.json({
        message: 'PDF export is not implemented in this demo',
        suggestion: 'Use CSV or JSON format for now',
        pdf_implementation_note: 'In production, integrate with puppeteer or similar PDF generation library'
      })
    }

    return new NextResponse(content, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`
      }
    })
})
