import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { withErrorHandling } from '@/lib/api-error-handler'

/**
 * GET /api/analytics/benefit-trends
 * Get benefit trends and popularity data
 */
export const GET = withErrorHandling(async (_request: NextRequest) => {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get benefit trends based on company associations
    const trendsResult = await query(`
      SELECT 
        b.id,
        b.name,
        b.category_id,
        b.icon,
        COUNT(cb.id) as company_count,
        COUNT(DISTINCT cb.company_id) as unique_companies,
        AVG(CASE WHEN cb.is_verified THEN 1 ELSE 0 END) as verification_rate,
        COUNT(CASE WHEN cb.created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as recent_additions
      FROM benefits b
      LEFT JOIN company_benefits cb ON b.id = cb.benefit_id
      GROUP BY b.id, b.name, b.category_id, b.icon
      ORDER BY company_count DESC, recent_additions DESC
      LIMIT 50
    `)

    // Get trending benefits (most added in last 30 days)
    const trendingResult = await query(`
      SELECT 
        b.id,
        b.name,
        b.icon,
        COUNT(cb.id) as recent_count
      FROM benefits b
      JOIN company_benefits cb ON b.id = cb.benefit_id
      WHERE cb.created_at >= NOW() - INTERVAL '30 days'
      GROUP BY b.id, b.name, b.icon
      ORDER BY recent_count DESC
      LIMIT 10
    `)

    // Get benefit categories with counts
    const categoriesResult = await query(`
      SELECT 
        bc.id,
        bc.name,
        COUNT(DISTINCT b.id) as benefit_count,
        COUNT(DISTINCT cb.company_id) as company_count
      FROM benefit_categories bc
      LEFT JOIN benefits b ON bc.id = b.category_id
      LEFT JOIN company_benefits cb ON b.id = cb.benefit_id
      GROUP BY bc.id, bc.name
      ORDER BY company_count DESC
    `)

    return NextResponse.json({
      trends: trendsResult.rows,
      trending: trendingResult.rows,
      categories: categoriesResult.rows,
      generated_at: new Date().toISOString()
    })
})
