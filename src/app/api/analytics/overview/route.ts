import { NextRequest, NextResponse } from 'next/server'
import { getAnalyticsAccessInfo } from '@/lib/analytics-access-control'
import { getAnalyticsOverview } from '@/lib/analytics-tracker'
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (request: NextRequest) => {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '7d'

    // Check analytics access level
    const accessInfo = await getAnalyticsAccessInfo()

    // If user is not authenticated or on demo mode, return demo data
    if (accessInfo.level === 'none' || accessInfo.isDemoMode) {
      return NextResponse.json({
        period,
        overview: {
          total_searches: 12450,
          company_views: 89320,
          active_companies: 156,
          avg_engagement: 78.5
        },
        is_demo_data: true,
        demo_notice: accessInfo.level === 'none'
          ? 'This is preview data. Sign up to access more analytics features.'
          : 'This is preview data. Upgrade to Premium to see real analytics.',
        generated_at: new Date().toISOString()
      })
    }

    // Get real analytics data for paying customers
    const analyticsData = await getAnalyticsOverview(period)
    return NextResponse.json(analyticsData)
})
