import { NextRequest, NextResponse } from 'next/server'
import { getAnalyticsAccessInfo } from '@/lib/analytics-access-control'
import { generateDemoTopCompanies } from '@/lib/demo-analytics-generator'
import { getTopCompanies } from '@/lib/analytics-tracker'
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (request: NextRequest) => {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '7d'
    const limit = parseInt(searchParams.get('limit') || '10')
    const _benefitFilter = searchParams.get('benefit')

    // Check analytics access level
    const accessInfo = await getAnalyticsAccessInfo()

    // If user is not authenticated or on demo mode, return demo data
    if (accessInfo.level === 'none' || accessInfo.isDemoMode) {
      const demoData = generateDemoTopCompanies(period, limit)
      return NextResponse.json(demoData)
    }

    // Get real top companies data
    const topCompaniesData = await getTopCompanies(period, limit)
    return NextResponse.json(topCompaniesData)
})
