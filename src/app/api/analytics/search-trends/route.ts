import { NextRequest, NextResponse } from 'next/server'
import { getAnalyticsAccessInfo } from '@/lib/analytics-access-control'
import { generateDemoSearchTrends } from '@/lib/demo-analytics-generator'
import { getSearchTrends } from '@/lib/analytics-tracker'
import { withErrorHandling } from '@/lib/api-error-handler'

// For demo purposes, we'll simulate search trends
// In production, you'd track actual search queries in a separate table

export const GET = withErrorHandling(async (request: NextRequest) => {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '7d' // 7d, 30d, 90d
    const limit = parseInt(searchParams.get('limit') || '10')

    // Check analytics access level
    const accessInfo = await getAnalyticsAccessInfo()

    // If user is not authenticated or on demo mode, return demo data
    if (accessInfo.level === 'none' || accessInfo.isDemoMode) {
      const demoData = generateDemoSearchTrends(period, limit)
      return NextResponse.json(demoData)
    }

    // Get real search trends data
    const searchTrendsData = await getSearchTrends(period, limit)
    return NextResponse.json(searchTrendsData)
})
