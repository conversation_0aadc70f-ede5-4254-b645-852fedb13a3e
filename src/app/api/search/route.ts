import { NextRequest, NextResponse } from 'next/server'
import { searchCompanies } from '@/lib/database'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (request: NextRequest) => {
  // Allow web UI access for all users, block direct API access except for admins
  await requireWebUIOrAdmin(request)

  const { searchParams } = new URL(request.url)
  const query = searchParams.get('q')
  const sort = searchParams.get('sort')

  if (!query || query.trim().length < 2) {
    return NextResponse.json(
      { error: 'Search query must be at least 2 characters' },
      { status: 400 }
    )
  }

  const sortOption = (sort && ['smart', 'alphabetical', 'newest'].includes(sort))
    ? sort as 'smart' | 'alphabetical' | 'newest'
    : 'smart'

  const companies = await searchCompanies(query.trim(), sortOption)

  return NextResponse.json(companies)
})
