import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { withErrorHandling } from '@/lib/api-error-handler'

// GET /api/benefit-categories - Get all benefit categories that have benefits (web UI or admin)
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Allow web UI access for all users, block direct API access except for admins
  await requireWebUIOrAdmin(request)

  const result = await query(`
    SELECT
      bc.id,
      bc.name,
      bc.display_name,
      bc.description,
      bc.icon,
      bc.sort_order,
      COUNT(b.id) as benefit_count
    FROM benefit_categories bc
    LEFT JOIN benefits b ON bc.id = b.category_id
    GROUP BY bc.id
    HAVING COUNT(b.id) > 0
    ORDER BY bc.sort_order ASC, bc.display_name ASC
  `)

  return NextResponse.json(result.rows)
})
