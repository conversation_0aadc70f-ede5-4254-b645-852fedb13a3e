import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { withErrorHandling } from '@/lib/api-error-handler'
import { query } from '@/lib/local-db'

export const POST = withErrorHandling(async (request: NextRequest) => {
  // Prevent direct API scraping of dispute data
  await requireWebUIOrAdmin(request)

  try {
    const user = await getCurrentUser()
    const body = await request.json()
    const { companyBenefitIds } = body

    if (!Array.isArray(companyBenefitIds) || companyBenefitIds.length === 0) {
      return NextResponse.json(
        { error: 'Company benefit IDs array is required' },
        { status: 400 }
      )
    }

    const userId = user?.id
    const results: Record<string, any> = {}

    // Initialize results for all requested IDs
    for (const companyBenefitId of companyBenefitIds) {
      results[companyBenefitId] = {
        canDispute: false,
        dispute: null,
        stats: {
          pending: 0,
          approved: 0,
          rejected: 0,
          total: 0
        }
      }
    }

    // Get all dispute stats for the requested benefits
    const statsQuery = `
      SELECT
        company_benefit_id,
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
      FROM benefit_removal_disputes
      WHERE company_benefit_id = ANY($1)
      GROUP BY company_benefit_id
    `

    const statsResult = await query(statsQuery, [companyBenefitIds])

    // Update stats
    for (const row of statsResult.rows) {
      if (results[row.company_benefit_id]) {
        results[row.company_benefit_id].stats = {
          pending: parseInt(row.pending || '0'),
          approved: parseInt(row.approved || '0'),
          rejected: parseInt(row.rejected || '0'),
          total: parseInt(row.total || '0')
        }
      }
    }

    // Get user's disputes if authenticated
    if (userId) {
      const userDisputesQuery = `
        SELECT
          company_benefit_id,
          id,
          reason,
          status,
          created_at,
          admin_comment
        FROM benefit_removal_disputes
        WHERE company_benefit_id = ANY($1) AND user_id = $2
      `
      const userDisputesResult = await query(userDisputesQuery, [companyBenefitIds, userId])

      // Update user disputes
      for (const dispute of userDisputesResult.rows) {
        if (results[dispute.company_benefit_id]) {
          results[dispute.company_benefit_id].dispute = dispute
        }
      }

      // Check if user can dispute for benefits without existing disputes
      for (const companyBenefitId of companyBenefitIds) {
        if (!results[companyBenefitId].dispute) {
          const benefitCheck = await query(
            'SELECT is_verified FROM company_benefits WHERE id = $1',
            [companyBenefitId]
          )

          if (benefitCheck.rows.length > 0) {
            const benefit = benefitCheck.rows[0]
            results[companyBenefitId].canDispute = benefit.is_verified

            // Determine if this is admin-verified vs community-verified
            if (benefit.is_verified) {
              // Get verification counts to determine if admin-verified
              const verificationCounts = await query(
                'SELECT status FROM benefit_verifications WHERE company_benefit_id = $1',
                [companyBenefitId]
              )

              const confirmedCount = verificationCounts.rows.filter(v => v.status === 'confirmed').length
              const disputedCount = verificationCounts.rows.filter(v => v.status === 'disputed').length
              const meetsUserCriteria = confirmedCount >= 2 && confirmedCount > disputedCount

              results[companyBenefitId].isAdminVerified = !meetsUserCriteria
            }
          }
        }
      }
    }

    return NextResponse.json(results)
  } catch (error) {
    console.error('Error fetching batch dispute status:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dispute status', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
})
