import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { withErrorHandling } from '@/lib/api-error-handler'

async function updateCompanyBenefitStatus(companyBenefitId: string) {
  try {
    // Get all verifications for this company benefit
    const verifications = await query(
      'SELECT status FROM benefit_verifications WHERE company_benefit_id = $1',
      [companyBenefitId]
    )

    const totalVerifications = verifications.rows.length
    const confirmedVerifications = verifications.rows.filter(v => v.status === 'confirmed').length
    const disputedVerifications = verifications.rows.filter(v => v.status === 'disputed').length

    // Update company benefit verification status
    // Benefits need 2+ confirmations and more confirmations than disputes to be verified
    let isVerified = false
    if (confirmedVerifications >= 2 && confirmedVerifications > disputedVerifications) {
      isVerified = true
    }

    await query(
      'UPDATE company_benefits SET is_verified = $1 WHERE id = $2',
      [isVerified, companyBenefitId]
    )

    return {
      companyBenefitId,
      totalVerifications,
      confirmedVerifications,
      disputedVerifications,
      isVerified,
      changed: true
    }
  } catch (error) {
    console.error('Error updating company benefit status:', error)
    return {
      companyBenefitId,
      error: error instanceof Error ? error.message : 'Unknown error',
      changed: false
    }
  }
}

/**
 * POST /api/admin/recalculate-verifications
 * Recalculate verification status for all company benefits based on current verification logic
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()

  try {
    // Get all company benefits that have verifications
    const result = await query(`
      SELECT DISTINCT cb.id as company_benefit_id, c.name as company_name, b.name as benefit_name
      FROM company_benefits cb
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE EXISTS (
        SELECT 1 FROM benefit_verifications bv 
        WHERE bv.company_benefit_id = cb.id
      )
      ORDER BY c.name, b.name
    `)

    const companyBenefits = result.rows
    const results = []

    console.log(`Found ${companyBenefits.length} company benefits with verifications to recalculate`)

    // Process each company benefit
    for (const cb of companyBenefits) {
      const updateResult = await updateCompanyBenefitStatus(cb.company_benefit_id)
      results.push({
        ...updateResult,
        companyName: cb.company_name,
        benefitName: cb.benefit_name
      })
    }

    // Count changes
    const changedCount = results.filter(r => r.changed && !r.error).length
    const errorCount = results.filter(r => r.error).length
    const verifiedCount = results.filter(r => r.isVerified && r.changed && !r.error).length

    return NextResponse.json({
      success: true,
      message: `Recalculated verification status for ${companyBenefits.length} company benefits`,
      summary: {
        total: companyBenefits.length,
        processed: changedCount,
        errors: errorCount,
        newlyVerified: verifiedCount
      },
      results: results
    })

  } catch (error) {
    console.error('Error recalculating verifications:', error)
    return NextResponse.json(
      { 
        error: 'Failed to recalculate verifications',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
})
