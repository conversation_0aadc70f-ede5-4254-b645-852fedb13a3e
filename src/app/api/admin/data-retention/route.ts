import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { runDataRetentionCleanup, runIPAnonymization, getCleanupHistory } from '@/lib/data-retention-cleanup'
import { withErrorHandling } from '@/lib/api-error-handler'

// GET /api/admin/data-retention - Get cleanup history
export const GET = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    
    const history = await getCleanupHistory(limit)
    
    return NextResponse.json({
      success: true,
      history,
      total: history.length
    })
})

// POST /api/admin/data-retention - Trigger manual cleanup
export const POST = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()
    
    const body = await request.json()
    const { type = 'comprehensive' } = body
    
    let result
    
    if (type === 'ip_only') {
      result = await runIPAnonymization()
    } else {
      result = await runDataRetentionCleanup()
    }
    
    return NextResponse.json({
      success: result.success,
      result,
      message: result.success
        ? `Cleanup completed successfully. ${result.totalRecordsAffected} records affected.`
        : `Cleanup failed: ${result.error}`
    })
})
