import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireAdmin } from '@/lib/auth'
import { withErrorHandling } from '@/lib/api-error-handler'

// GET /api/admin/benefit-categories - Get all benefit categories
export const GET = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()

    const { searchParams } = new URL(request.url)
    const includeEmpty = searchParams.get('includeEmpty') === 'true'

    let havingClause = ''
    if (!includeEmpty) {
      havingClause = 'HAVING COUNT(b.id) > 0'
    }

    const result = await query(`
      SELECT
        bc.*,
        COUNT(b.id) as benefit_count,
        CASE WHEN COUNT(b.id) > 0 THEN true ELSE false END as has_benefits
      FROM benefit_categories bc
      LEFT JOIN benefits b ON bc.id = b.category_id
      GROUP BY bc.id
      ${havingClause}
      ORDER BY bc.sort_order ASC, bc.display_name ASC
    `)

    return NextResponse.json(result.rows)
})

// POST /api/admin/benefit-categories - Create a new benefit category
export const POST = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()
    
    const body = await request.json()
    const { name, display_name, description, icon, sort_order } = body
    
    // Validation
    if (!name || !display_name) {
      return NextResponse.json(
        { error: 'Name and display name are required' },
        { status: 400 }
      )
    }
    
    // Validate name format (lowercase, alphanumeric, underscores only)
    if (!/^[a-z0-9_]+$/.test(name)) {
      return NextResponse.json(
        { error: 'Name must contain only lowercase letters, numbers, and underscores' },
        { status: 400 }
      )
    }
    
    // Check if name already exists
    const existingCategory = await query(
      'SELECT id FROM benefit_categories WHERE name = $1',
      [name]
    )
    
    if (existingCategory.rows.length > 0) {
      return NextResponse.json(
        { error: 'A category with this name already exists' },
        { status: 409 }
      )
    }
    
    // Get next sort order if not provided
    let finalSortOrder = sort_order
    if (finalSortOrder === undefined || finalSortOrder === null) {
      const maxSortResult = await query(
        'SELECT COALESCE(MAX(sort_order), 0) + 1 as next_sort_order FROM benefit_categories'
      )
      finalSortOrder = maxSortResult.rows[0].next_sort_order
    }
    
    const result = await query(
      `INSERT INTO benefit_categories (name, display_name, description, icon, sort_order)
       VALUES ($1, $2, $3, $4, $5) RETURNING *`,
      [name, display_name, description, icon, finalSortOrder]
    )
    
    return NextResponse.json(result.rows[0], { status: 201 })
})
