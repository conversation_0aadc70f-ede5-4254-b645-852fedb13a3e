import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { withErrorHandling } from '@/lib/api-error-handler'

/**
 * PUT /api/admin/missing-companies/[reportId]
 * Update missing company report status (approve/reject)
 */
export const PUT = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ reportId: string }> }
) => {
  await requireAdmin()
    const { reportId } = await params
    const body = await request.json()
    const { status, adminNotes, companyId } = body

    // Normalize status (accept 'approved' as alias for 'added')
    const normalizedStatus = status === 'approved' ? 'added' : status

    if (!normalizedStatus || !['reviewed', 'added', 'rejected'].includes(normalizedStatus)) {
      return NextResponse.json(
        { error: 'Status must be one of: reviewed, added, rejected (or approved)' },
        { status: 400 }
      )
    }

    // Check if report exists
    const existingResult = await query(
      'SELECT id FROM missing_company_reports WHERE id = $1',
      [reportId]
    )

    if (existingResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Missing company report not found' },
        { status: 404 }
      )
    }

    // Update report status
    const result = await query(
      `UPDATE missing_company_reports
       SET status = $1, admin_notes = $2, company_id = $3, updated_at = NOW()
       WHERE id = $4
       RETURNING *`,
      [normalizedStatus, adminNotes || null, companyId || null, reportId]
    )

    return NextResponse.json({
      success: true,
      report: result.rows[0]
    })
})

/**
 * GET /api/admin/missing-companies/[reportId]
 * Get specific missing company report details
 */
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ reportId: string }> }
) => {
  await requireAdmin()
    const { reportId } = await params

    const result = await query(
      `SELECT 
        mcr.*,
        c.name as company_name,
        c.domain as company_domain
       FROM missing_company_reports mcr
       LEFT JOIN companies c ON mcr.company_id = c.id
       WHERE mcr.id = $1`,
      [reportId]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Missing company report not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(result.rows[0])
})
