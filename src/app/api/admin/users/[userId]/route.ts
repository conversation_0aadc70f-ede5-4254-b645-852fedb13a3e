import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { withErrorHandling } from '@/lib/api-error-handler'

// GET /api/admin/users/[userId] - Get detailed user information
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) => {
  await requireAdmin()

    const { userId } = await params

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Get detailed user information with company and session data (using automatic email domain matching)
    const userResult = await query(`
      SELECT
        u.id,
        u.email,
        u.first_name,
        u.last_name,
        u.role,
        u.payment_status,
        u.email_verified,
        u.created_at,
        u.updated_at,
        -- Company assignment via users.company_id (automatic email domain matching)
        c.id as company_id,
        c.name as company_name,
        c.domain as company_domain,
        'domain' as company_assignment_type,

        COUNT(DISTINCT s.id) as total_sessions,
        COUNT(DISTINCT CASE WHEN s.expires_at > NOW() THEN s.id END) as active_sessions,
        MAX(s.created_at) as last_login
      FROM users u
      -- Company assignment via users.company_id
      LEFT JOIN companies c ON u.company_id = c.id
      LEFT JOIN user_sessions s ON u.id = s.user_id
      WHERE u.id = $1
      GROUP BY u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, u.email_verified, u.created_at, u.updated_at,
               c.id, c.name, c.domain
    `, [userId])

    if (userResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    const user = userResult.rows[0]

    // Get recent sessions
    const sessionsResult = await query(`
      SELECT
        id,
        created_at,
        expires_at,
        CASE WHEN expires_at > NOW() THEN true ELSE false END as is_active
      FROM user_sessions
      WHERE user_id = $1
      ORDER BY created_at DESC
      LIMIT 10
    `, [userId])

    const detailedUser = {
      ...user,
      sessions: sessionsResult.rows
    }

    return NextResponse.json(detailedUser)
})
