import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { sendEmail } from '@/lib/email'
import { createCompanyDiscoveryEmail } from '@/lib/user-discovery'
import { withErrorHandling } from '@/lib/api-error-handler'

export const POST = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()

    const body = await request.json()
    const { companyId, domain } = body

    if (!companyId && !domain) {
      return NextResponse.json(
        { error: 'Either company ID or domain is required' },
        { status: 400 }
      )
    }

    let company
    let searchDomain = domain

    // If companyId is provided, get company details
    if (companyId) {
      const companyResult = await query(
        'SELECT id, name, domain FROM companies WHERE id = $1',
        [companyId]
      )

      if (companyResult.rows.length === 0) {
        return NextResponse.json(
          { error: 'Company not found' },
          { status: 404 }
        )
      }

      company = companyResult.rows[0]
      searchDomain = company.domain
    }

    if (!searchDomain) {
      return NextResponse.json(
        { error: 'No domain available for notification lookup' },
        { status: 400 }
      )
    }

    // Find pending reports for this domain
    // Note: This API is for manual notification, so we don't exclude discovery users here
    // as the admin might want to manually notify specific users
    const reportsResult = await query(
      'SELECT id, user_email, first_name, last_name FROM missing_company_reports WHERE email_domain = $1 AND status = $2',
      [searchDomain.toLowerCase(), 'pending']
    )

    if (reportsResult.rows.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No pending reports found for this domain',
        notifiedUsers: 0
      })
    }

    // If we don't have company details yet, get them
    if (!company) {
      const companyResult = await query(
        'SELECT id, name, domain FROM companies WHERE domain = $1',
        [searchDomain.toLowerCase()]
      )

      if (companyResult.rows.length === 0) {
        return NextResponse.json(
          { error: 'No company found with this domain' },
          { status: 404 }
        )
      }

      company = companyResult.rows[0]
    }

    const notificationResult = {
      notifiedUsers: 0,
      errors: [] as string[],
      reportIds: [] as string[]
    }

    // Send notifications to all users who reported this company
    for (const report of reportsResult.rows) {
      try {
        // Send notification email
        const emailOptions = createCompanyDiscoveryEmail(
          report.user_email,
          report.first_name || 'User',
          company.name,
          company.domain || 'your-company.com',
          company.id
        )
        await sendEmail(emailOptions)

        // Update report status to 'added' and link to company
        await query(
          'UPDATE missing_company_reports SET status = $1, company_id = $2, updated_at = NOW() WHERE id = $3',
          ['added', company.id, report.id]
        )

        notificationResult.notifiedUsers++
        notificationResult.reportIds.push(report.id)
        console.log(`✅ Notified user ${report.user_email} about company addition: ${company.name}`)
      } catch (error) {
        console.error(`Error notifying user ${report.user_email}:`, error)
        notificationResult.errors.push(`Failed to notify ${report.user_email}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully notified ${notificationResult.notifiedUsers} users about ${company.name} being added`,
      companyName: company.name,
      companyId: company.id,
      domain: searchDomain,
      notifiedUsers: notificationResult.notifiedUsers,
      reportIds: notificationResult.reportIds,
      errors: notificationResult.errors
    })
})
