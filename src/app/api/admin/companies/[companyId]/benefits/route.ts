import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { withErrorHandling } from '@/lib/api-error-handler'
import { query } from '@/lib/local-db'

// GET /api/admin/companies/[companyId]/benefits - Get all benefits for a company
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) => {
  await requireAdmin()
    const { companyId } = await params
    
    // Get company benefits with verification counts
    const result = await query(`
      SELECT
        cb.id as company_benefit_id,
        cb.is_verified,
        cb.created_at as added_at,
        b.id as benefit_id,
        b.name as benefit_name,
        bc.name as category,
        b.icon,
        -- Get verification counts
        (SELECT COUNT(*) FROM benefit_verifications bv
         WHERE bv.company_benefit_id = cb.id AND bv.status = 'confirmed') as confirmed_count,
        (SELECT COUNT(*) FROM benefit_verifications bv
         WHERE bv.company_benefit_id = cb.id AND bv.status = 'disputed') as disputed_count,
        (SELECT COUNT(*) FROM benefit_verifications bv
         WHERE bv.company_benefit_id = cb.id) as total_verifications
      FROM company_benefits cb
      JOIN benefits b ON cb.benefit_id = b.id
      LEFT JOIN benefit_categories bc ON b.category_id = bc.id
      WHERE cb.company_id = $1
      ORDER BY bc.name, b.name
    `, [companyId])

    return NextResponse.json({
      benefits: result.rows
    })
})

// POST /api/admin/companies/[companyId]/benefits - Add benefit to company
export const POST = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) => {
  await requireAdmin()
    const { companyId } = await params
    const body = await request.json()
    const { benefitId, isVerified = false } = body

    if (!benefitId) {
      return NextResponse.json(
        { error: 'Benefit ID is required' },
        { status: 400 }
      )
    }

    // Check if company exists
    const companyResult = await query(
      'SELECT id, name FROM companies WHERE id = $1',
      [companyId]
    )

    if (companyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Check if benefit exists
    const benefitResult = await query(
      'SELECT id, name FROM benefits WHERE id = $1',
      [benefitId]
    )

    if (benefitResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Benefit not found' },
        { status: 404 }
      )
    }

    // Check if benefit is already added to company
    const existingResult = await query(
      'SELECT id FROM company_benefits WHERE company_id = $1 AND benefit_id = $2',
      [companyId, benefitId]
    )

    if (existingResult.rows.length > 0) {
      return NextResponse.json(
        { error: 'Benefit already added to this company' },
        { status: 400 }
      )
    }

    // Add benefit to company
    const insertResult = await query(
      'INSERT INTO company_benefits (company_id, benefit_id, is_verified) VALUES ($1, $2, $3) RETURNING *',
      [companyId, benefitId, isVerified]
    )

    const company = companyResult.rows[0]
    const benefit = benefitResult.rows[0]

    return NextResponse.json({
      success: true,
      message: `Added "${benefit.name}" to ${company.name}`,
      companyBenefit: insertResult.rows[0]
    }, { status: 201 })
})

// DELETE /api/admin/companies/[companyId]/benefits - Remove benefit from company
export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) => {
  await requireAdmin()
    const { companyId } = await params
    const { searchParams } = new URL(request.url)
    const benefitId = searchParams.get('benefitId')

    if (!benefitId) {
      return NextResponse.json(
        { error: 'Benefit ID is required' },
        { status: 400 }
      )
    }

    // Get company and benefit info for response
    const infoResult = await query(`
      SELECT 
        c.name as company_name,
        b.name as benefit_name,
        cb.id as company_benefit_id
      FROM company_benefits cb
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE cb.company_id = $1 AND cb.benefit_id = $2
    `, [companyId, benefitId])

    if (infoResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company benefit association not found' },
        { status: 404 }
      )
    }

    const info = infoResult.rows[0]

    // Delete the company benefit (this will cascade delete verifications)
    await query(
      'DELETE FROM company_benefits WHERE company_id = $1 AND benefit_id = $2',
      [companyId, benefitId]
    )

    return NextResponse.json({
      success: true,
      message: `Removed "${info.benefit_name}" from ${info.company_name}`
    })
})
