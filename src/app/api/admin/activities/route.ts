import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { getRecentActivities } from '@/lib/activity-logger'
import { withErrorHandling } from '@/lib/api-error-handler'

// GET /api/admin/activities - Get recent activities for admin dashboard
export const GET = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    
    // Validate limit
    if (limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Limit must be between 1 and 100' },
        { status: 400 }
      )
    }
    
    const activities = await getRecentActivities(limit)
    
    return NextResponse.json({
      activities,
      total: activities.length
    })
})
