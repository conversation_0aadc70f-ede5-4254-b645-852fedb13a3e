import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin, getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { v4 as uuidv4 } from 'uuid'
import { cookies } from 'next/headers'
import { withErrorHandling } from '@/lib/api-error-handler'

// POST /api/admin/impersonate - Start impersonating a user
export const POST = withErrorHandling(async (request: NextRequest) => {
  // Ensure the current user is an admin
  const adminUser = await requireAdmin()

  const body = await request.json()
  const { userId } = body

  // Debug logging
  console.log('🔍 Impersonation request received:', {
    body,
    userId,
    userIdType: typeof userId,
    userIdLength: userId?.length
  })

  if (!userId) {
    return NextResponse.json(
      { error: 'User ID is required' },
      { status: 400 }
    )
  }

  // Verify the target user exists and is not an admin
  const targetUserResult = await query(
    'SELECT id, email, role FROM users WHERE id = $1',
    [userId]
  )

  if (targetUserResult.rows.length === 0) {
    return NextResponse.json(
      { error: 'User not found' },
      { status: 404 }
    )
  }

  const targetUser = targetUserResult.rows[0]

  // Prevent impersonating other admins for security
  if (targetUser.role === 'admin') {
    return NextResponse.json(
      { error: 'Cannot impersonate other admin users' },
      { status: 403 }
    )
  }

  // Prevent self-impersonation (doesn't make sense)
  if (targetUser.id === adminUser.id) {
    return NextResponse.json(
      { error: 'Cannot impersonate yourself' },
      { status: 400 }
    )
  }

  try {
    // Create a new impersonation session
    const sessionToken = uuidv4()
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

    // Insert the impersonation session
    await query(
      `INSERT INTO user_sessions (
        user_id, 
        session_token, 
        expires_at, 
        is_impersonation, 
        original_admin_id, 
        impersonated_user_id
      ) VALUES ($1, $2, $3, $4, $5, $6)`,
      [
        targetUser.id,      // user_id points to the impersonated user
        sessionToken,
        expiresAt,
        true,               // is_impersonation
        adminUser.id,       // original_admin_id
        targetUser.id       // impersonated_user_id
      ]
    )

    // Set the new session cookie
    const cookieStore = await cookies()
    cookieStore.set('session_token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: expiresAt,
    })

    // Log the impersonation activity
    await query(
      `INSERT INTO activity_log (
        event_type, 
        event_description, 
        user_id, 
        metadata
      ) VALUES ($1, $2, $3, $4)`,
      [
        'user_impersonation_started',
        `Admin ${adminUser.email} started impersonating user ${targetUser.email}`,
        adminUser.id,
        JSON.stringify({
          admin_id: adminUser.id,
          admin_email: adminUser.email,
          impersonated_user_id: targetUser.id,
          impersonated_user_email: targetUser.email,
          session_token: sessionToken.substring(0, 8) + '...'
        })
      ]
    )

    return NextResponse.json({
      success: true,
      message: `Now impersonating ${targetUser.email}`,
      impersonatedUser: {
        id: targetUser.id,
        email: targetUser.email
      }
    })

  } catch (error) {
    console.error('Error starting impersonation:', error)
    return NextResponse.json(
      { error: 'Failed to start impersonation' },
      { status: 500 }
    )
  }
})
