import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { v4 as uuidv4 } from 'uuid'
import { cookies } from 'next/headers'
import { withErrorHandling } from '@/lib/api-error-handler'

// POST /api/admin/exit-impersonation - Exit impersonation and return to admin session
export const POST = withErrorHandling(async (request: NextRequest) => {
  const currentUser = await getCurrentUser()
  
  if (!currentUser) {
    return NextResponse.json(
      { error: 'Not authenticated' },
      { status: 401 }
    )
  }

  // Get the current session token
  const cookieStore = await cookies()
  const currentSessionToken = cookieStore.get('session_token')?.value

  if (!currentSessionToken) {
    return NextResponse.json(
      { error: 'No active session found' },
      { status: 400 }
    )
  }

  // Check if the current session is an impersonation session
  const sessionResult = await query(
    `SELECT 
      us.is_impersonation,
      us.original_admin_id,
      us.impersonated_user_id,
      admin_u.email as admin_email,
      impersonated_u.email as impersonated_email
    FROM user_sessions us
    LEFT JOIN users admin_u ON us.original_admin_id = admin_u.id
    LEFT JOIN users impersonated_u ON us.impersonated_user_id = impersonated_u.id
    WHERE us.session_token = $1`,
    [currentSessionToken]
  )

  if (sessionResult.rows.length === 0) {
    return NextResponse.json(
      { error: 'Session not found' },
      { status: 404 }
    )
  }

  const session = sessionResult.rows[0]

  if (!session.is_impersonation) {
    return NextResponse.json(
      { error: 'Not currently impersonating a user' },
      { status: 400 }
    )
  }

  if (!session.original_admin_id) {
    return NextResponse.json(
      { error: 'Invalid impersonation session - no original admin ID' },
      { status: 400 }
    )
  }

  try {
    // Create a new regular admin session
    const newSessionToken = uuidv4()
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

    // Insert the new admin session
    await query(
      `INSERT INTO user_sessions (
        user_id, 
        session_token, 
        expires_at, 
        is_impersonation, 
        original_admin_id, 
        impersonated_user_id
      ) VALUES ($1, $2, $3, $4, $5, $6)`,
      [
        session.original_admin_id,  // Back to the original admin
        newSessionToken,
        expiresAt,
        false,                      // Not an impersonation
        null,                       // No original admin
        null                        // No impersonated user
      ]
    )

    // Delete the impersonation session
    await query(
      'DELETE FROM user_sessions WHERE session_token = $1',
      [currentSessionToken]
    )

    // Set the new session cookie
    cookieStore.set('session_token', newSessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: expiresAt,
    })

    // Log the end of impersonation
    await query(
      `INSERT INTO activity_log (
        event_type, 
        event_description, 
        user_id, 
        metadata
      ) VALUES ($1, $2, $3, $4)`,
      [
        'user_impersonation_ended',
        `Admin ${session.admin_email} ended impersonation of user ${session.impersonated_email}`,
        session.original_admin_id,
        JSON.stringify({
          admin_id: session.original_admin_id,
          admin_email: session.admin_email,
          impersonated_user_id: session.impersonated_user_id,
          impersonated_user_email: session.impersonated_email,
          old_session_token: currentSessionToken.substring(0, 8) + '...',
          new_session_token: newSessionToken.substring(0, 8) + '...'
        })
      ]
    )

    return NextResponse.json({
      success: true,
      message: `Exited impersonation of ${session.impersonated_email}`,
      returnedToAdmin: session.admin_email
    })

  } catch (error) {
    console.error('Error exiting impersonation:', error)
    return NextResponse.json(
      { error: 'Failed to exit impersonation' },
      { status: 500 }
    )
  }
})
