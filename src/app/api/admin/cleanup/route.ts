import { NextRequest, NextResponse } from 'next/server'
import { withErrorHandling } from '@/lib/api-error-handler'
import { requireAdmin } from '@/lib/auth'
import { scheduledCacheMaintenance } from '@/lib/postgresql-cache'
import { runDataRetentionCleanup } from '@/lib/data-retention-cleanup'

// POST /api/admin/cleanup - Trigger manual cleanup
export const POST = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()
  
  const body = await request.json()
  const { type = 'cache' } = body
  
  let result: any = {}
  
  try {
    if (type === 'cache') {
      await scheduledCacheMaintenance()
      result = { type: 'cache', message: 'Cache maintenance completed successfully' }
    } else if (type === 'data-retention') {
      const cleanupResult = await runDataRetentionCleanup()
      result = { 
        type: 'data-retention', 
        message: `Data retention cleanup completed. ${cleanupResult.totalRecordsAffected} records affected.`,
        details: cleanupResult
      }
    } else if (type === 'all') {
      await scheduledCacheMaintenance()
      const cleanupResult = await runDataRetentionCleanup()
      result = { 
        type: 'all', 
        message: `All cleanup completed. ${cleanupResult.totalRecordsAffected} records affected.`,
        details: cleanupResult
      }
    } else {
      return NextResponse.json(
        { error: 'Invalid cleanup type. Use: cache, data-retention, or all' },
        { status: 400 }
      )
    }
    
    return NextResponse.json({
      success: true,
      result
    })
  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Cleanup failed', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    )
  }
})
