import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { getAuthFailureStats } from '@/lib/auth-logger'
import { withErrorHandling } from '@/lib/api-error-handler'

// GET /api/admin/auth-logs - Get authentication failure statistics and logs
export const GET = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()

  const { searchParams } = new URL(request.url)
  const days = parseInt(searchParams.get('days') || '7')

  // Validate days parameter
  if (days < 1 || days > 365) {
    return NextResponse.json(
      { error: 'Days parameter must be between 1 and 365' },
      { status: 400 }
    )
  }

  const stats = await getAuthFailureStats(days)

  return NextResponse.json(stats)
})
