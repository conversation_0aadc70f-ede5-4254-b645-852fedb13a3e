import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { logBenefitRemovalDisputeProcessed, logBenefitAutomaticallyRemoved } from '@/lib/activity-logger'
import { withErrorHandling } from '@/lib/api-error-handler'

// GET /api/admin/benefit-removal-disputes - Get all pending disputes for admin review
export const GET = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'pending'
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get disputes with company and benefit information
    const result = await query(`
      SELECT
        brd.id as dispute_id,
        brd.company_benefit_id,
        brd.user_id,
        brd.reason,
        brd.status,
        brd.admin_user_id,
        brd.admin_comment,
        brd.created_at,
        brd.updated_at,
        cb.is_verified as benefit_is_verified,
        c.id as company_id,
        c.name as company_name,
        c.domain as company_domain,
        b.id as benefit_id,
        b.name as benefit_name,
        bc.name as benefit_category,
        b.icon as benefit_icon,
        u.email as user_email,
        u.first_name as user_first_name,
        u.last_name as user_last_name,
        admin_u.email as admin_email,
        admin_u.first_name as admin_first_name,
        admin_u.last_name as admin_last_name,
        -- Get dispute counts for this benefit
        (SELECT COUNT(*) FROM benefit_removal_disputes brd2
         WHERE brd2.company_benefit_id = cb.id AND brd2.status = 'approved') as approved_disputes_count,
        (SELECT COUNT(*) FROM benefit_removal_disputes brd2
         WHERE brd2.company_benefit_id = cb.id AND brd2.status = 'pending') as pending_disputes_count,
        (SELECT COUNT(*) FROM benefit_removal_disputes brd2
         WHERE brd2.company_benefit_id = cb.id) as total_disputes_count
      FROM benefit_removal_disputes brd
      JOIN company_benefits cb ON brd.company_benefit_id = cb.id
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      LEFT JOIN benefit_categories bc ON b.category_id = bc.id
      LEFT JOIN users u ON brd.user_id = u.id
      LEFT JOIN users admin_u ON brd.admin_user_id = admin_u.id
      WHERE brd.status = $1
      ORDER BY brd.created_at DESC
      LIMIT $2 OFFSET $3
    `, [status, limit, offset])

    // Get total count for pagination
    const countResult = await query(
      'SELECT COUNT(*) as total FROM benefit_removal_disputes WHERE status = $1',
      [status]
    )

    return NextResponse.json({
      disputes: result.rows,
      total: parseInt(countResult.rows[0].total),
      limit,
      offset,
      status
    })
})

// POST /api/admin/benefit-removal-disputes - Approve or reject a dispute
export async function POST(request: NextRequest) {
  try {
    const adminUser = await requireAdmin()
    const adminUserId = adminUser.id

    const body = await request.json()
    const { disputeId, action, adminComment } = body
    
    if (!disputeId || !action) {
      return NextResponse.json(
        { error: 'Dispute ID and action are required' },
        { status: 400 }
      )
    }
    
    if (!['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be approve or reject' },
        { status: 400 }
      )
    }

    // Get the dispute details
    const disputeResult = await query(`
      SELECT 
        brd.*,
        cb.is_verified,
        c.name as company_name,
        b.name as benefit_name
      FROM benefit_removal_disputes brd
      JOIN company_benefits cb ON brd.company_benefit_id = cb.id
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE brd.id = $1
    `, [disputeId])
    
    if (disputeResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Dispute not found' },
        { status: 404 }
      )
    }
    
    const dispute = disputeResult.rows[0]
    
    if (dispute.status !== 'pending') {
      return NextResponse.json(
        { error: 'Dispute has already been processed' },
        { status: 400 }
      )
    }

    // Update the dispute status
    const newStatus = action === 'approve' ? 'approved' : 'rejected'
    await query(`
      UPDATE benefit_removal_disputes
      SET status = $1, admin_user_id = $2, admin_comment = $3, updated_at = NOW()
      WHERE id = $4
    `, [newStatus, adminUserId, adminComment, disputeId])

    // Log the dispute processing activity
    await logBenefitRemovalDisputeProcessed(
      dispute.benefit_id,
      dispute.benefit_name,
      dispute.company_id,
      dispute.company_name,
      adminUserId,
      adminUser?.email || 'unknown',
      newStatus,
      dispute.user_id,
      dispute.user_email || 'unknown',
      adminUser?.firstName && adminUser?.lastName ? `${adminUser.firstName} ${adminUser.lastName}` : undefined,
      dispute.user_first_name && dispute.user_last_name ? `${dispute.user_first_name} ${dispute.user_last_name}` : undefined,
      adminComment
    )

    // If approved, check if we now have 2 approved disputes for this benefit from different users
    if (action === 'approve') {
      const approvedDisputesResult = await query(`
        SELECT COUNT(DISTINCT user_id) as unique_users, COUNT(*) as total_count
        FROM benefit_removal_disputes
        WHERE company_benefit_id = $1 AND status = 'approved'
      `, [dispute.company_benefit_id])

      const { unique_users, total_count } = approvedDisputesResult.rows[0]
      const uniqueUserCount = parseInt(unique_users)
      const totalApprovedCount = parseInt(total_count)

      // If we have 2 or more approved disputes from different users, automatically remove the benefit
      if (uniqueUserCount >= 2 && totalApprovedCount >= 2) {
        await query(
          'DELETE FROM company_benefits WHERE id = $1',
          [dispute.company_benefit_id]
        )

        // Log the automatic removal
        await logBenefitAutomaticallyRemoved(
          dispute.benefit_id,
          dispute.benefit_name,
          dispute.company_id,
          dispute.company_name,
          totalApprovedCount,
          uniqueUserCount,
          adminUserId,
          adminUser?.email || 'unknown',
          adminUser?.firstName && adminUser?.lastName ? `${adminUser.firstName} ${adminUser.lastName}` : undefined
        )

        return NextResponse.json({
          message: `Dispute ${action}ed successfully. Benefit automatically removed due to ${totalApprovedCount} approved disputes from ${uniqueUserCount} different users.`,
          dispute: dispute,
          action: action,
          benefitRemoved: true,
          approvedDisputesCount: totalApprovedCount,
          uniqueUserCount: uniqueUserCount
        })
      }
    }

    return NextResponse.json({
      message: `Dispute ${action}ed successfully`,
      dispute: dispute,
      action: action,
      benefitRemoved: false
    })
    
  } catch (error) {
    console.error('Error processing benefit removal dispute:', error)
    return NextResponse.json(
      { error: 'Failed to process dispute' },
      { status: 500 }
    )
  }
}
