'use client'

import { useState } from 'react'
import { 
  createSignInMagicLinkEmail, 
  createSignUpMagicLinkEmail, 
  createCompanyDiscoveryEmail,
  createMissingCompanyReportEmail 
} from '@/lib/email-generators'

export default function EmailPreviewClient() {
  const [selectedTemplate, setSelectedTemplate] = useState('signin')
  const [previewData, setPreviewData] = useState({
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    companyName: 'Example Corp',
    companyDomain: 'example.com',
    companyId: 'company-123',
    token: 'sample-token-123',
    reportId: 'report-123'
  })

  const generateEmailHTML = () => {
    try {
      switch (selectedTemplate) {
        case 'signin':
          return createSignInMagicLinkEmail(previewData.email, previewData.token).html
        case 'signup':
          return createSignUpMagicLinkEmail(previewData.email, previewData.firstName, previewData.token).html
        case 'discovery':
          return createCompanyDiscoveryEmail(
            previewData.email,
            previewData.firstName,
            previewData.companyName,
            previewData.companyDomain,
            previewData.companyId
          ).html
        case 'missing-company':
          return createMissingCompanyReportEmail(
            previewData.reportId,
            previewData.email,
            previewData.companyDomain,
            previewData.firstName,
            previewData.lastName
          ).html
        default:
          return '<p>Select a template to preview</p>'
      }
    } catch (error) {
      return `<p style="color: red;">Error generating email: ${error instanceof Error ? error.message : 'Unknown error'}</p>`
    }
  }

  const emailTemplates = [
    { id: 'signin', name: 'Sign In Magic Link' },
    { id: 'signup', name: 'Sign Up Magic Link' },
    { id: 'discovery', name: 'Company Discovery' },
    { id: 'missing-company', name: 'Missing Company Report (Admin)' }
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Header */}
          <div className="bg-blue-600 text-white p-6">
            <h1 className="text-2xl font-bold">📧 Email Template Preview</h1>
            <p className="text-blue-100 mt-2">Preview and test email templates before deployment</p>
            <p className="text-blue-200 text-sm mt-1">⚠️ Admin Only - Production Access</p>
          </div>

          <div className="flex">
            {/* Sidebar */}
            <div className="w-1/3 bg-gray-50 p-6 border-r">
              <h2 className="text-lg font-semibold mb-4">Template Selection</h2>
              
              <div className="space-y-2 mb-6">
                {emailTemplates.map((template) => (
                  <button
                    key={template.id}
                    onClick={() => setSelectedTemplate(template.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      selectedTemplate === template.id
                        ? 'bg-blue-100 text-blue-800 border border-blue-300'
                        : 'bg-white hover:bg-gray-100 border border-gray-200'
                    }`}
                  >
                    {template.name}
                  </button>
                ))}
              </div>

              <h3 className="text-md font-semibold mb-3">Preview Data</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    value={previewData.email}
                    onChange={(e) => setPreviewData({...previewData, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                  <input
                    type="text"
                    value={previewData.firstName}
                    onChange={(e) => setPreviewData({...previewData, firstName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                  <input
                    type="text"
                    value={previewData.companyName}
                    onChange={(e) => setPreviewData({...previewData, companyName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Company Domain</label>
                  <input
                    type="text"
                    value={previewData.companyDomain}
                    onChange={(e) => setPreviewData({...previewData, companyDomain: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Preview */}
            <div className="flex-1 p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Email Preview</h2>
                <button
                  onClick={() => {
                    const emailHTML = generateEmailHTML()
                    const newWindow = window.open('', '_blank')
                    if (newWindow) {
                      newWindow.document.body.innerHTML = emailHTML
                    }
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Open in New Window
                </button>
              </div>
              
              <div className="border border-gray-300 rounded-lg overflow-hidden">
                <iframe
                  srcDoc={generateEmailHTML()}
                  className="w-full h-[600px]"
                  title="Email Preview"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
