import { requireAdmin } from '@/lib/auth'
import { redirect } from 'next/navigation'
import EmailPreviewClient from './email-preview-client'

export default async function EmailPreviewPage() {
  // Require admin access
  try {
    await requireAdmin()
  } catch (error) {
    redirect('/auth/signin?message=Admin access required')
  }

  // Only allow in development or for admins
  if (process.env.NODE_ENV === 'production') {
    // In production, this page is only accessible to admins (already checked above)
    // You could add additional restrictions here if needed
  }

  return <EmailPreviewClient />
}
