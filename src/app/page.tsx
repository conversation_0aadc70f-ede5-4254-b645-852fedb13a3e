import { Suspense } from 'react'
import { Metadata } from 'next'
import { CompanySearch } from '@/components/company-search'

export const metadata: Metadata = {
  title: "BenefitLens - Find Companies Based on Their Benefits",
  description: "Discover and compare companies by their employee benefits. Search for companies offering Wellpass, sabbatical leave, remote work, flexible hours, and more benefits in Germany.",
  keywords: [
    "employee benefits search",
    "company benefits comparison",
    "Wellpass companies",
    "sabbatical leave employers",
    "remote work companies",
    "flexible working hours",
    "German companies benefits",
    "employer benefits finder"
  ],
  openGraph: {
    title: "BenefitLens - Find Companies Based on Their Benefits",
    description: "Discover and compare companies by their employee benefits. Search for companies offering Wellpass, sabbatical leave, remote work, and more.",
    type: 'website',
  },
  alternates: {
    canonical: '/',
  },
}

function CompanySearchFallback() {
  return (
    <div className="flex justify-center items-center py-12">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>
  )
}

export default function Home() {
  return (
    <main className="container mx-auto px-4 py-6 sm:py-8">
      <div className="text-center mb-8 sm:mb-12">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
          Find Companies Based on Their Benefits
        </h1>
        <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto px-2">
          Find and compare companies by their employee benefits. Search for specific benefits like Wellpass, sabbatical leave, and more.
        </p>
      </div>



      <Suspense fallback={<CompanySearchFallback />}>
        <CompanySearch />
      </Suspense>
    </main>
  );
}
