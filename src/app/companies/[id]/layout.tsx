import { Metadata } from 'next'
import { generateCompanyMetadata } from '@/lib/company-metadata'

interface CompanyLayoutProps {
  children: React.ReactNode
  params: Promise<{ id: string }>
}

export async function generateMetadata(
  { params }: { params: Promise<{ id: string }> }
): Promise<Metadata> {
  const resolvedParams = await params
  return generateCompanyMetadata(resolvedParams)
}

export default function CompanyLayout({ children }: CompanyLayoutProps) {
  return <>{children}</>
}
