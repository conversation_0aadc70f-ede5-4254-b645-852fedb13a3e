'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useConsent } from '@/lib/consent-manager'
import { Shield, BarChart3, Zap, Download, Trash2, RefreshCw, <PERSON>ert<PERSON>riangle, FileText } from 'lucide-react'
import Link from 'next/link'

export default function PrivacyPreferencesPage() {
  const { 
    preferences, 
    hasConsent, 
    updatePreferences, 
    acceptAll, 
    acceptNecessaryOnly, 
    revokeConsent 
  } = useConsent()
  
  const [showConfirmRevoke, setShowConfirmRevoke] = useState(false)
  const [showConfirmDeletion, setShowConfirmDeletion] = useState(false)
  const [saved, setSaved] = useState(false)
  const [deletionStatus, setDeletionStatus] = useState<any>(null)
  const [isExporting, setIsExporting] = useState(false)
  const [isDeletingData, setIsDeletingData] = useState(false)

  const togglePreference = (key: keyof typeof preferences) => {
    if (key === 'necessary') {return} // Cannot disable necessary cookies
    
    updatePreferences({
      [key]: !preferences[key]
    })
    setSaved(true)
    setTimeout(() => setSaved(false), 2000)
  }

  const handleRevokeConsent = () => {
    revokeConsent()
    setShowConfirmRevoke(false)
  }

  // Load deletion status on component mount
  useEffect(() => {
    fetchDeletionStatus()
  }, [])

  const fetchDeletionStatus = async () => {
    try {
      const response = await fetch('/api/user/data-deletion')
      if (response.ok) {
        const data = await response.json()
        setDeletionStatus(data)
      }
    } catch (error) {
      console.error('Error fetching deletion status:', error)
    }
  }

  const handleExportData = async () => {
    setIsExporting(true)
    try {
      const response = await fetch('/api/user/data-export')
      if (response.ok) {
        const blob = await response.blob()
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `benefitlens-data-export-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      } else {
        alert('Error exporting data. Please try again later.')
      }
    } catch (error) {
      console.error('Error exporting data:', error)
      alert('Error exporting data. Please try again later.')
    } finally {
      setIsExporting(false)
    }
  }

  const handleRequestDeletion = async (reason: string) => {
    setIsDeletingData(true)
    try {
      const response = await fetch('/api/user/data-deletion', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          confirmDeletion: true,
          reason
        })
      })

      if (response.ok) {
        await fetchDeletionStatus()
        setShowConfirmDeletion(false)
        alert('Deletion request was successfully submitted.')
      } else {
        alert('Error submitting deletion request. Please try again later.')
      }
    } catch (error) {
      console.error('Error requesting deletion:', error)
      alert('Error submitting deletion request. Please try again later.')
    } finally {
      setIsDeletingData(false)
    }
  }

  const handleCancelDeletion = async () => {
    try {
      const response = await fetch('/api/user/data-deletion', {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchDeletionStatus()
        alert('Deletion request was successfully cancelled.')
      } else {
        alert('Error cancelling deletion request.')
      }
    } catch (error) {
      console.error('Error cancelling deletion:', error)
      alert('Error cancelling deletion request.')
    }
  }

  return (
    <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm border p-6 lg:p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">Privacy Settings</h1>
              <p className="text-gray-600">
                Manage your cookie and privacy preferences. You have full
                control over your data and can change these settings at any time.
              </p>
            </div>

            {saved && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 font-medium">✓ Settings saved</p>
              </div>
            )}

            {/* Current Status */}
            <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h2 className="text-lg font-semibold text-blue-900 mb-2">Current Status</h2>
              <p className="text-blue-800">
                {hasConsent
                  ? 'You have set your privacy preferences.'
                  : 'You have not yet set your privacy preferences.'
                }
              </p>
            </div>

            {/* Cookie Categories */}
            <div className="space-y-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900">Cookie Categories</h2>

              {/* Necessary Cookies */}
              <div className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Shield className="w-5 h-5 text-green-600" />
                    <h3 className="text-lg font-medium text-gray-900">Necessary Cookies</h3>
                  </div>
                  <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                    Always active
                  </div>
                </div>
                <p className="text-gray-600 mb-4">
                  These cookies are required for the basic functions of the website and cannot
                  be disabled. They include:
                </p>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• Session management and login</li>
                  <li>• Security features and CSRF protection</li>
                  <li>• Basic website functionality</li>
                  <li>• Storage of your cookie preferences</li>
                </ul>
              </div>

              {/* Analytics Cookies */}
              <div className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <BarChart3 className="w-5 h-5 text-blue-600" />
                    <h3 className="text-lg font-medium text-gray-900">Analytics Cookies</h3>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.analytics}
                      onChange={() => togglePreference('analytics')}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                <p className="text-gray-600 mb-4">
                  Help us understand how visitors interact with the website.
                  All data is anonymized and used to improve the user experience.
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <p className="text-sm text-blue-800 font-medium mb-2">
                    🛡️ IP Address Privacy Notice
                  </p>
                  <p className="text-sm text-blue-700 mb-2">
                    When analytics cookies are enabled, we collect anonymized IP addresses
                    (last digits removed) to understand geographic usage patterns and improve our service.
                  </p>
                  <ul className="text-xs text-blue-600 space-y-1 ml-4">
                    <li>• IP addresses are anonymized immediately for analytics</li>
                    <li>• Full IP addresses are only stored for security purposes</li>
                    <li>• Analytics data with IP addresses is auto-deleted after 12 months</li>
                    <li>• You can request deletion of your IP data anytime</li>
                  </ul>
                </div>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• Page views and navigation behavior</li>
                  <li>• Search history and filter usage (with anonymized IP)</li>
                  <li>• Anonymous usage statistics</li>
                  <li>• Performance monitoring</li>
                  <li>• Geographic analytics (region-level only)</li>
                </ul>
              </div>

              {/* Functional Cookies */}
              <div className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Zap className="w-5 h-5 text-purple-600" />
                    <h3 className="text-lg font-medium text-gray-900">Functional Cookies</h3>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={preferences.functional}
                      onChange={() => togglePreference('functional')}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                <p className="text-gray-600 mb-4">
                  Enable advanced features and a personalized user experience.
                </p>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• Saved search preferences</li>
                  <li>• Personalized content</li>
                  <li>• Advanced UI features</li>
                  <li>• Improved user experience</li>
                </ul>
              </div>


            </div>

            {/* Data Management Section */}
            <div className="border-t pt-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Data Management</h2>

              {/* Data Export */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <div className="flex items-start gap-4">
                  <FileText className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-blue-900 mb-2">Export Data</h3>
                    <p className="text-blue-800 mb-4">
                      Download all your personal data that we have stored about you.
                      This includes your profile, saved companies, reviews, and anonymized usage data.
                    </p>
                    <Button
                      onClick={handleExportData}
                      disabled={isExporting}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {isExporting ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Exporting...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4 mr-2" />
                          Export Data
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Data Deletion */}
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                <div className="flex items-start gap-4">
                  <AlertTriangle className="w-6 h-6 text-red-600 flex-shrink-0 mt-1" />
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-red-900 mb-2">Delete Account and Data</h3>

                    {deletionStatus?.hasDeletionRequest ? (
                      <div className="space-y-4">
                        <div className="bg-yellow-100 border border-yellow-300 rounded p-3">
                          <p className="text-yellow-800 font-medium">
                            ⚠️ Deletion request submitted on {new Date(deletionStatus.deletionRequestedAt).toLocaleDateString('en-US')}
                          </p>
                          <p className="text-yellow-700 text-sm mt-1">
                            Your account will be deleted within 30 days. You have {' '}
                            {deletionStatus.gracePeriodInfo?.gracePeriodDays || 7} days left to cancel the request.
                          </p>
                        </div>

                        {deletionStatus.gracePeriodInfo?.canCancel && (
                          <Button
                            onClick={handleCancelDeletion}
                            variant="outline"
                            className="border-yellow-400 text-yellow-700 hover:bg-yellow-50"
                          >
                            Cancel Deletion Request
                          </Button>
                        )}
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <p className="text-red-800">
                          Permanently delete your account and all associated data.
                          This action cannot be undone.
                        </p>
                        <ul className="text-sm text-red-700 space-y-1 ml-4">
                          <li>• All personal data will be deleted</li>
                          <li>• Saved companies and reviews will be removed</li>
                          <li>• Usage data will be anonymized</li>
                          <li>• 7-day grace period before final deletion</li>
                        </ul>
                        <Button
                          onClick={() => setShowConfirmDeletion(true)}
                          variant="outline"
                          className="border-red-400 text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Request Account Deletion
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="border-t pt-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Button
                  onClick={acceptAll}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                >
                  <RefreshCw className="w-4 h-4" />
                  Accept All
                </Button>

                <Button
                  onClick={acceptNecessaryOnly}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Shield className="w-4 h-4" />
                  Necessary Only
                </Button>

                <Button
                  onClick={handleExportData}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Export Data
                </Button>

                <Button
                  onClick={() => setShowConfirmRevoke(true)}
                  variant="outline"
                  className="flex items-center gap-2 text-red-600 border-red-300 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                  Revoke Consent
                </Button>
              </div>
            </div>

            {/* Revoke Confirmation Modal */}
            {showConfirmRevoke && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <div className="bg-white rounded-lg p-6 max-w-md w-full">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Revoke consent?
                  </h3>
                  <p className="text-gray-600 mb-6">
                    This will reset all your privacy preferences and you will
                    be asked for your consent again.
                  </p>
                  <div className="flex gap-3">
                    <Button
                      onClick={() => setShowConfirmRevoke(false)}
                      variant="outline"
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleRevokeConsent}
                      className="flex-1 bg-red-600 hover:bg-red-700"
                    >
                      Revoke
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Data Deletion Confirmation Modal */}
            {showConfirmDeletion && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <div className="bg-white rounded-lg p-6 max-w-md w-full">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Delete Account and Data?
                  </h3>
                  <div className="space-y-4 mb-6">
                    <div className="bg-red-50 border border-red-200 rounded p-3">
                      <p className="text-red-800 font-medium text-sm">
                        ⚠️ This action cannot be undone!
                      </p>
                    </div>
                    <p className="text-gray-600 text-sm">
                      All your personal data will be permanently deleted:
                    </p>
                    <ul className="text-sm text-gray-600 space-y-1 ml-4">
                      <li>• Profile data and login information</li>
                      <li>• Saved companies and reviews</li>
                      <li>• Usage history and preferences</li>
                      <li>• All data linked to your account</li>
                    </ul>
                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Reason for deletion (optional):
                      </label>
                      <textarea
                        id="deletionReason"
                        className="w-full p-2 border border-gray-300 rounded text-sm"
                        rows={3}
                        placeholder="Why do you want to delete your account?"
                      />
                    </div>
                  </div>
                  <div className="flex gap-3">
                    <Button
                      onClick={() => setShowConfirmDeletion(false)}
                      variant="outline"
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={() => {
                        const reason = (document.getElementById('deletionReason') as HTMLTextAreaElement)?.value || ''
                        handleRequestDeletion(reason)
                      }}
                      disabled={isDeletingData}
                      className="flex-1 bg-red-600 hover:bg-red-700"
                    >
                      {isDeletingData ? 'Processing...' : 'Request Deletion'}
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Legal Links */}
            <div className="border-t pt-8 mt-8">
              <p className="text-sm text-gray-600 text-center">
                For more information, see our{' '}
                <Link href="/datenschutz" className="text-blue-600 hover:text-blue-700 underline">
                  Privacy Policy
                </Link>{' '}
                and{' '}
                <Link href="/terms" className="text-blue-600 hover:text-blue-700 underline">
                  Terms of Service
                </Link>.
              </p>
            </div>
          </div>
        </div>
      </main>
  )
}
