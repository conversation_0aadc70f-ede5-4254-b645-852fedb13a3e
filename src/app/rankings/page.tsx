import { redirect } from 'next/navigation'
import { Metadata } from 'next'
import { BenefitRanking } from '@/components/benefit-ranking'
import { getCurrentUser } from '@/lib/auth'

export const metadata: Metadata = {
  title: "Benefit Rankings | BenefitLens",
  description: "Rank and prioritize employee benefits based on your preferences. Create your personalized benefit ranking to find companies that match your priorities.",
}

export default async function RankingsPage() {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect('/sign-in')
  }

  return (
    <main className="container mx-auto px-4 py-6 sm:py-8">
      <div className="text-center mb-8 sm:mb-12">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
          Rank Your Benefits
        </h1>
        <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto px-2">
          Drag and drop benefits to rank them by importance. Your rankings help us provide better company recommendations.
        </p>
      </div>
      <BenefitRanking />
    </main>
  )
}
