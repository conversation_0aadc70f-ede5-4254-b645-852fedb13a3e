import { redirect } from 'next/navigation'
import { CompanyDashboard } from '@/components/company-dashboard'
import { getCurrentUser, getUserCompany } from '@/lib/auth'

export default async function DashboardPage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect('/sign-in')
  }

  const company = await getUserCompany()

  return (
    <main className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Company Dashboard
        </h1>
        <p className="text-gray-600">
          Manage your company profile and benefits information
        </p>
      </div>

      <CompanyDashboard
        user={{
          id: user.id,
          email: user.email,
          firstName: user.firstName || undefined,
          lastName: user.lastName || undefined
        }}
        company={company}
      />
    </main>
  )
}
