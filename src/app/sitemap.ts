import { MetadataRoute } from 'next'
import { getCompanies, getBenefits } from '@/lib/database'
import type { Benefit } from '@/types/database'

// Static sitemap for build phase when database is not available
function getStaticSitemap(baseUrl: string): MetadataRoute.Sitemap {
  return [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${baseUrl}/companies`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/benefits`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/impressum`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.3,
    },
    {
      url: `${baseUrl}/datenschutz`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.3,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.3,
    },
  ]
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://benefitlens.de'

  // Skip database operations during build phase
  if (process.env.BUILD_PHASE === 'true') {
    console.log('Skipping database operations during build phase')
    return getStaticSitemap(baseUrl)
  }

  try {
    // Add timeout wrapper for database operations
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Database timeout')), 10000) // 10 second timeout
    })

    // Get all companies (limit to reasonable number for sitemap) with timeout
    const companiesPromise = getCompanies({}, { limit: 1000 })
    const companies = await Promise.race([companiesPromise, timeoutPromise]) as any[]

    // Get all benefits with timeout
    const benefitsPromise = getBenefits()
    const benefits = await Promise.race([benefitsPromise, timeoutPromise]) as any[]
    
    // Static pages
    const staticPages: MetadataRoute.Sitemap = [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1,
      },
      {
        url: `${baseUrl}/companies`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${baseUrl}/benefits`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
      {
        url: `${baseUrl}/about`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.6,
      },
      {
        url: `${baseUrl}/impressum`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.3,
      },
      {
        url: `${baseUrl}/datenschutz`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.3,
      },
      {
        url: `${baseUrl}/terms`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.3,
      },
    ]

    // Company pages
    const companyPages: MetadataRoute.Sitemap = companies.map((company) => ({
      url: `${baseUrl}/companies/${company.id}`,
      lastModified: new Date(company.updated_at || company.created_at),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }))

    // Benefit filter pages (for major benefits)
    const benefitPages: MetadataRoute.Sitemap = benefits
      .slice(0, 50) // Limit to top 50 benefits to avoid too many URLs
      .map((benefit: Benefit) => ({
        url: `${baseUrl}/companies?benefits=${encodeURIComponent(benefit.name)}`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.6,
      }))

    // Industry filter pages (get unique industries from companies)
    const industries = [...new Set(companies.map(c => c.industry).filter(Boolean))]
    const industryPages: MetadataRoute.Sitemap = industries
      .slice(0, 30) // Limit to top 30 industries
      .map((industry) => ({
        url: `${baseUrl}/companies?industry=${encodeURIComponent(industry)}`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.5,
      }))

    // Location filter pages (get unique cities from company locations)
    const cities = [...new Set(
      companies
        .flatMap(c => c.locations || [])
        .map(l => l.city)
        .filter(Boolean)
    )]
    const locationPages: MetadataRoute.Sitemap = cities
      .slice(0, 50) // Limit to top 50 cities
      .map((city) => ({
        url: `${baseUrl}/companies?location=${encodeURIComponent(city)}`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.5,
      }))

    return [
      ...staticPages,
      ...companyPages,
      ...benefitPages,
      ...industryPages,
      ...locationPages,
    ]
  } catch (error) {
    console.error('Error generating sitemap:', error)
    
    // Return minimal sitemap if there's an error
    return [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1,
      },
      {
        url: `${baseUrl}/companies`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${baseUrl}/benefits`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
    ]
  }
}
