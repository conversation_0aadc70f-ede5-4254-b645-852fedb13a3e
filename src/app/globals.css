@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #2563eb;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --muted: #f1f5f9;
  --muted-foreground: #475569;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #2563eb;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: #f9fafb; /* Force light gray background */
  color: var(--foreground);
  font-family: var(--font-sans);
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Mobile scrolling improvements */
.webkit-overflow-scrolling-touch {
  -webkit-overflow-scrolling: touch;
}

/* Ensure smooth scrolling on mobile */
@media (max-width: 640px) {
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Improve touch scrolling for dropdowns */
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Ensure minimum touch target sizes for mobile accessibility */
  button,
  input[type="submit"],
  input[type="button"],
  a[role="button"],
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* Exception for small icon buttons that are part of larger touch targets */
  .icon-only-small {
    min-height: auto;
    min-width: auto;
  }
}

/* Fix for dropdown scrolling issues */
.dropdown-scroll-container {
  /* Ensure smooth scrolling */
  scroll-behavior: smooth;

  /* Prevent overscroll bounce on mobile */
  overscroll-behavior: contain;

  /* Ensure proper scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;

  /* Prevent content from scrolling past boundaries */
  scroll-snap-type: y proximity;

  /* Ensure last item is fully visible when scrolled to bottom */
  padding-bottom: 4px;
  margin-bottom: 0;
}

/* WebKit scrollbar styling for better appearance */
.dropdown-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.dropdown-scroll-container::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 4px;
}

.dropdown-scroll-container::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.dropdown-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Prevent scroll jumping by ensuring consistent height calculation */
.dropdown-scroll-container {
  position: relative;
  height: auto;
  max-height: 256px; /* 320px total - 60px for search input area - 4px padding */
  overflow-y: auto;
  overflow-x: hidden;

  /* Ensure content doesn't overflow past the visible area */
  box-sizing: border-box;
}
