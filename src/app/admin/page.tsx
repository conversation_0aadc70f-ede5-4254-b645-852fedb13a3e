'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { AdminDashboard } from '@/components/admin-dashboard'

interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: string
  paymentStatus: string
  company_domain?: string
  is_premium?: boolean
}

export default function AdminPage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [authVerified, setAuthVerified] = useState(false)
  const router = useRouter()

  useEffect(() => {
    async function checkAuth() {
      try {
        console.log('AdminPage: Checking authentication...')
        const response = await fetch('/api/auth/me')
        console.log('AdminPage: Auth response status:', response.status)

        if (!response.ok) {
          console.log('AdminPage: Not authenticated, redirecting to sign-in')
          router.push('/sign-in')
          return
        }

        const data = await response.json()
        const userData = data.user
        console.log('AdminPage: User data:', userData)

        if (!userData) {
          console.log('AdminPage: No user data, redirecting to sign-in')
          router.push('/sign-in')
          return
        }

        // Check if user has admin role
        if (userData.role !== 'admin') {
          console.log('AdminPage: User is not admin, redirecting to home')
          router.push('/')
          return
        }

        console.log('AdminPage: Admin user verified - setting auth verified flag')
        setUser(userData)
        setAuthVerified(true)
      } catch (err) {
        console.error('AdminPage: Authentication check failed:', err)
        router.push('/sign-in')
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [router])

  // Note: Removed router override that was blocking legitimate navigation

  if (loading) {
    return (
      <main className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-800">Verifying admin access...</p>
          </div>
        </div>
      </main>
    )
  }

  if (!user) {
    return (
      <main className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <p className="text-gray-800">Redirecting...</p>
          </div>
        </div>
      </main>
    )
  }

  return (
    <main className="container mx-auto px-2 sm:px-4 py-8 max-w-full overflow-hidden">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Platform Administration
        </h1>
        <p className="text-gray-800">
          Manage companies, users, benefits, and platform-wide settings
        </p>
      </div>

      {/* Add error boundary around AdminDashboard */}
      <ErrorBoundary>
        <AdminDashboard />
      </ErrorBoundary>
    </main>
  )
}

// Simple error boundary component
function ErrorBoundary({ children }: { children: React.ReactNode }) {
  const [hasError, setHasError] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error('AdminPage: Caught error:', event.error)
      setHasError(true)
      setError(event.error)
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('AdminPage: Caught unhandled rejection:', event.reason)
      setHasError(true)
      setError(new Error(event.reason))
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  if (hasError) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Admin Dashboard Error</h3>
          <p className="text-gray-800 mb-4">
            The admin dashboard encountered an error. Please try refreshing the page.
          </p>
          <p className="text-sm text-gray-700 mb-4">
            Error: {error?.message || 'Unknown error'}
          </p>
          <button
            onClick={() => {
              setHasError(false)
              setError(null)
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
