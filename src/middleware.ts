import { NextRequest, NextResponse } from 'next/server'

const protectedRoutes = [
  '/dashboard',
  '/admin',
  '/api/admin',
]





// Simple rate limiting for Edge Runtime
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

function getClientIdentifier(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')

  return forwarded?.split(',')[0]?.trim() ||
         realIp ||
         cfConnectingIp ||
         'unknown'
}

function checkRateLimit(key: string, limit: number = 100, windowMs: number = 60000): boolean {
  const now = Date.now()
  const record = rateLimitStore.get(key)

  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (record.count >= limit) {
    return false
  }

  record.count++
  return true
}

function requiresCSRFProtection(method: string, pathname: string): boolean {
  if (pathname.startsWith('/api/') && !pathname.startsWith('/api/auth/')) {
    return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)
  }
  return false
}

function extractCSRFToken(request: NextRequest): string | null {
  return request.headers.get('X-CSRF-Token')
}

// Simple CSRF validation for Edge Runtime
const csrfStore = new Map<string, string>()

function validateCSRFToken(sessionId: string, providedToken: string): boolean {
  if (!providedToken || !sessionId) {
    return false
  }

  const storedToken = csrfStore.get(`csrf:${sessionId}`)
  return storedToken === providedToken
}

export async function middleware(request: NextRequest) {
  const response = NextResponse.next()

  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin')
  response.headers.set('X-XSS-Protection', '1; mode=block')

  // Add CSP header - permissive for development
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self'",
    "frame-ancestors 'none'"
  ].join('; ')
  response.headers.set('Content-Security-Policy', csp)

  // Rate limiting for API routes (can be disabled for testing)
  const rateLimitingDisabled = process.env.DISABLE_RATE_LIMITING === 'true'

  if (request.nextUrl.pathname.startsWith('/api/') && !rateLimitingDisabled) {
    const clientId = getClientIdentifier(request)
    const isAllowed = checkRateLimit(clientId, 100, 60000) // 100 requests per minute

    if (!isAllowed) {
      return NextResponse.json(
        { error: 'Too many requests' },
        { status: 429, headers: response.headers }
      )
    }
  }

  // Skip middleware for auth routes
  if (request.nextUrl.pathname.startsWith('/api/auth/')) {
    return response
  }

  // Check if route is protected
  const isProtectedRoute = protectedRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  )

  if (!isProtectedRoute) {
    return response
  }

  // Check session cookie for authentication
  const sessionToken = request.cookies.get('session_token')?.value

  if (!sessionToken) {
    // For API routes, return 401 instead of redirecting
    if (request.nextUrl.pathname.startsWith('/api/')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401, headers: response.headers }
      )
    }
    // For page routes, redirect to sign-in
    return NextResponse.redirect(new URL('/sign-in', request.url))
  }

  // For protected routes, just check if session token exists
  // The actual validation will happen in the page/API route using getCurrentUser()

  // CSRF protection for state-changing API requests
  if (requiresCSRFProtection(request.method, request.nextUrl.pathname)) {
    const csrfToken = extractCSRFToken(request)
    const isValidCSRF = validateCSRFToken(sessionToken, csrfToken || '')

    if (!isValidCSRF) {
      return NextResponse.json(
        { error: 'Invalid CSRF token' },
        { status: 403, headers: response.headers }
      )
    }
  }

  return response
}

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}
