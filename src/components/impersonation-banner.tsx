'use client'

import { useState } from 'react'
import { AlertTriangle, X } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface ImpersonationBannerProps {
  impersonatedUserEmail: string
  originalAdminEmail: string
  onExitImpersonation: () => void
}

export function ImpersonationBanner({ 
  impersonatedUserEmail, 
  originalAdminEmail, 
  onExitImpersonation 
}: ImpersonationBannerProps) {
  const [isExiting, setIsExiting] = useState(false)

  const handleExitImpersonation = async () => {
    setIsExiting(true)
    try {
      const response = await fetch('/api/admin/exit-impersonation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        // Redirect to admin dashboard
        window.location.href = '/admin'
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to exit impersonation')
      }
    } catch (error) {
      console.error('Error exiting impersonation:', error)
      alert('Failed to exit impersonation')
    } finally {
      setIsExiting(false)
    }
  }

  return (
    <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-3">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <AlertTriangle className="w-5 h-5 text-yellow-600" />
          <div className="text-sm">
            <span className="font-medium text-yellow-800">
              You are impersonating {impersonatedUserEmail}
            </span>
            <span className="text-yellow-700 ml-2">
              (Admin: {originalAdminEmail})
            </span>
          </div>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleExitImpersonation}
          disabled={isExiting}
          className="bg-white border-yellow-300 text-yellow-800 hover:bg-yellow-50"
        >
          {isExiting ? (
            <>
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-yellow-600 mr-2"></div>
              Exiting...
            </>
          ) : (
            <>
              <X className="w-3 h-3 mr-1" />
              Exit Impersonation
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
