'use client'

import { useState, useEffect } from 'react'
import { Shield, Trash2, Download, Refresh<PERSON><PERSON>, <PERSON>ert<PERSON>riangle, <PERSON><PERSON>ircle, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { SafeDate } from '@/components/ui/safe-date'

interface CleanupResult {
  operation: string
  recordsAffected: number
  completedAt: string
}

interface CleanupHistory {
  cleanup_type: string
  records_affected: number
  operation_details: any
  started_at: string
  completed_at: string
  status: 'completed' | 'failed' | 'running'
  error_message?: string
}

interface IPDeletionRequest {
  id: string
  user_id: string
  email: string
  reason: string
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
  created_at: string
  processed_at?: string
  processed_by?: string
  notes?: string
}

export function AdminIPDataManagement() {
  const [cleanupHistory, setCleanupHistory] = useState<CleanupHistory[]>([])
  const [deletionRequests, setDeletionRequests] = useState<IPDeletionRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [runningCleanup, setRunningCleanup] = useState(false)
  const [activeTab, setActiveTab] = useState<'cleanup' | 'requests'>('cleanup')

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    setLoading(true)
    try {
      // Fetch cleanup history
      const cleanupResponse = await fetch('/api/admin/data-retention?limit=20')
      if (cleanupResponse.ok) {
        const cleanupData = await cleanupResponse.json()
        setCleanupHistory(cleanupData.history || [])
      }

      // Fetch deletion requests
      const requestsResponse = await fetch('/api/admin/ip-deletion-requests')
      if (requestsResponse.ok) {
        const requestsData = await requestsResponse.json()
        setDeletionRequests(requestsData.requests || [])
      }
    } catch (error) {
      console.error('Error fetching IP data management data:', error)
    } finally {
      setLoading(false)
    }
  }

  const runCleanup = async (type: 'comprehensive' | 'ip_only') => {
    setRunningCleanup(true)
    try {
      const response = await fetch('/api/admin/data-retention', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type })
      })

      const result = await response.json()
      
      if (result.success) {
        alert(`Cleanup completed successfully! ${result.result.totalRecordsAffected} records affected.`)
        fetchData() // Refresh data
      } else {
        alert(`Cleanup failed: ${result.result.error}`)
      }
    } catch (error) {
      console.error('Error running cleanup:', error)
      alert('Failed to run cleanup')
    } finally {
      setRunningCleanup(false)
    }
  }

  const processIPDeletionRequest = async (requestId: string, action: 'approve' | 'reject', notes?: string) => {
    try {
      const response = await fetch(`/api/admin/ip-deletion-requests/${requestId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, notes })
      })

      if (response.ok) {
        alert(`Request ${action}d successfully`)
        fetchData() // Refresh data
      } else {
        const error = await response.json()
        alert(`Failed to ${action} request: ${error.error}`)
      }
    } catch (error) {
      console.error(`Error ${action}ing request:`, error)
      alert(`Failed to ${action} request`)
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading IP data management...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">IP Address Data Management</h3>
        <p className="text-sm text-gray-600">
          Manage IP address data retention, anonymization, and deletion requests for GDPR compliance.
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('cleanup')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'cleanup'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Shield className="w-4 h-4 inline mr-2" />
            Data Retention & Cleanup
          </button>
          <button
            onClick={() => setActiveTab('requests')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'requests'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Trash2 className="w-4 h-4 inline mr-2" />
            Deletion Requests
            {deletionRequests.filter(r => r.status === 'pending').length > 0 && (
              <span className="ml-2 bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
                {deletionRequests.filter(r => r.status === 'pending').length}
              </span>
            )}
          </button>
        </nav>
      </div>

      {/* Data Retention & Cleanup Tab */}
      {activeTab === 'cleanup' && (
        <div className="space-y-6">
          {/* Manual Cleanup Actions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-3">Manual Cleanup Actions</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={() => runCleanup('ip_only')}
                disabled={runningCleanup}
                variant="outline"
                className="flex items-center justify-center"
              >
                <Shield className="w-4 h-4 mr-2" />
                {runningCleanup ? 'Running...' : 'Anonymize Old IPs'}
              </Button>
              <Button
                onClick={() => runCleanup('comprehensive')}
                disabled={runningCleanup}
                className="flex items-center justify-center bg-blue-600 hover:bg-blue-700"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                {runningCleanup ? 'Running...' : 'Full Cleanup'}
              </Button>
            </div>
            <p className="text-xs text-blue-700 mt-2">
              IP anonymization removes identifying information from analytics data older than 12 months.
              Full cleanup includes session cleanup and expired data removal.
            </p>
          </div>

          {/* Cleanup History */}
          <div>
            <h4 className="font-medium text-gray-900 mb-4">Recent Cleanup History</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Records Affected
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {cleanupHistory.map((cleanup, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {cleanup.cleanup_type.replace('_', ' ').toUpperCase()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {cleanup.records_affected.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          cleanup.status === 'completed' 
                            ? 'bg-green-100 text-green-800'
                            : cleanup.status === 'failed'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {cleanup.status === 'completed' && <CheckCircle className="w-3 h-3 mr-1" />}
                          {cleanup.status === 'failed' && <AlertTriangle className="w-3 h-3 mr-1" />}
                          {cleanup.status === 'running' && <Clock className="w-3 h-3 mr-1" />}
                          {cleanup.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <SafeDate date={cleanup.completed_at || cleanup.started_at} />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Deletion Requests Tab */}
      {activeTab === 'requests' && (
        <div className="space-y-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
              <div>
                <h4 className="font-medium text-yellow-900">IP Data Deletion Requests</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Handle user requests for IP address data deletion. Note that security logs may be retained 
                  for legitimate business interests even after user requests.
                </p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reason
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Requested
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {deletionRequests.map((request) => (
                  <tr key={request.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{request.email}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 max-w-xs truncate" title={request.reason}>
                        {request.reason}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        request.status === 'completed' 
                          ? 'bg-green-100 text-green-800'
                          : request.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : request.status === 'processing'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {request.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <SafeDate date={request.created_at} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {request.status === 'pending' && (
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            onClick={() => processIPDeletionRequest(request.id, 'approve')}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const notes = prompt('Reason for rejection (optional):')
                              processIPDeletionRequest(request.id, 'reject', notes || undefined)
                            }}
                          >
                            Reject
                          </Button>
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {deletionRequests.length === 0 && (
            <div className="text-center py-8">
              <Trash2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No IP data deletion requests found.</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
