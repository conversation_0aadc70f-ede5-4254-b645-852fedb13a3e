import Script from 'next/script'
import type { Company, CompanyBenefit, CompanyLocation } from '@/types/database'

interface CompanyWithBenefits extends Company {
  company_benefits?: (CompanyBenefit & {
    benefit: {
      id: string
      name: string
      category: string
      icon: string | null
      description: string | null
    }
  })[]
  locations?: CompanyLocation[]
}

interface CompanyStructuredDataProps {
  company: CompanyWithBenefits
}

export function CompanyStructuredData({ company }: CompanyStructuredDataProps) {
  const primaryLocation = company.locations?.find(l => l.is_primary) || company.locations?.[0]
  
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": company.name,
    "description": company.description || `${company.name} - Employee benefits and company information`,
    "url": company.domain ? `https://${company.domain}` : undefined,
    "sameAs": company.career_url ? [company.career_url] : undefined,
    "industry": company.industry,
    "numberOfEmployees": {
      "@type": "QuantitativeValue",
      "name": company.size
    },
    "address": primaryLocation ? {
      "@type": "PostalAddress",
      "addressLocality": primaryLocation.city,
      "addressCountry": primaryLocation.country
    } : undefined,
    "location": company.locations?.map(location => ({
      "@type": "Place",
      "name": location.location_raw,
      "address": {
        "@type": "PostalAddress",
        "addressLocality": location.city,
        "addressCountry": location.country
      }
    })),
    "offers": company.company_benefits?.map(cb => ({
      "@type": "Offer",
      "name": cb.benefit?.name,
      "category": cb.benefit?.category,
      "description": cb.benefit?.description || `${cb.benefit?.name} benefit offered by ${company.name}`
    }))
  }

  // Remove undefined values
  const cleanedData = JSON.parse(JSON.stringify(structuredData))

  return (
    <Script
      id="company-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(cleanedData)
      }}
    />
  )
}

interface WebsiteStructuredDataProps {
  url?: string
}

export function WebsiteStructuredData({ url = 'https://benefitlens.de' }: WebsiteStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "BenefitLens",
    "description": "Find and compare companies based on their employee benefits. Search by location, benefits, and more.",
    "url": url,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${url}/companies?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "BenefitLens",
      "url": url
    }
  }

  return (
    <Script
      id="website-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  )
}

interface BreadcrumbStructuredDataProps {
  items: Array<{
    name: string
    url: string
  }>
}

export function BreadcrumbStructuredData({ items }: BreadcrumbStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  }

  return (
    <Script
      id="breadcrumb-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  )
}
