'use client'

import { useState, useEffect } from 'react'
import { AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import type { Company } from '@/types/database'

interface ApplyNowButtonProps {
  company: Company
}

export function ApplyNowButton({ company }: ApplyNowButtonProps) {
  const [showMessage, setShowMessage] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const handleApplyNow = () => {
    if (!mounted) {return}

    if (company.career_url) {
      // Open career URL in new tab
      window.open(company.career_url, '_blank', 'noopener,noreferrer')
    } else {
      // Show message that no career page is available
      setShowMessage(true)
      setTimeout(() => setShowMessage(false), 3000)
    }
  }

  return (
    <div className="relative">
      <Button onClick={handleApplyNow}>
        <span>Apply Now</span>
      </Button>
      
      {showMessage && !company.career_url && (
        <div className="absolute top-full left-0 mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg z-10 min-w-64">
          <div className="flex items-start gap-2">
            <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-yellow-800">No career page available</p>
              <p className="text-yellow-700 mt-1">
                This company hasn&apos;t configured their careers page yet. Try visiting their website or contacting them directly.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
