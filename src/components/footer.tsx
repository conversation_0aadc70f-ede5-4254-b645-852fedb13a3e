import Link from 'next/link'

export function Footer() {
  return (
    <footer className="bg-white border-t border-gray-200 mt-auto">
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          {/* Logo and Copyright */}
          <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
            <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">✓</span>
            </div>
            <span className="text-lg font-bold text-gray-900">BenefitLens</span>
          </Link>
          
          {/* Legal Links */}
          <div className="flex items-center space-x-6 text-sm text-gray-600">
            <Link
              href="/impressum"
              className="hover:text-gray-900 transition-colors"
            >
              Impressum
            </Link>
            <Link
              href="/datenschutz"
              className="hover:text-gray-900 transition-colors"
            >
              Datenschutz
            </Link>
            <Link
              href="/terms"
              className="hover:text-gray-900 transition-colors"
            >
              Terms
            </Link>
            <Link
              href="/about"
              className="hover:text-gray-900 transition-colors"
            >
              About
            </Link>
          </div>
          
          {/* Copyright */}
          <div className="text-sm text-gray-500">
            © {new Date().getFullYear()} BenefitLens. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  )
}
