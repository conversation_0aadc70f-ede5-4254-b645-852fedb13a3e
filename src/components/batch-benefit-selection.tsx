'use client'

import { useState, useEffect, useMemo } from 'react'
import { Search, Check, X, Plus, ArrowRight, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useToastHelpers } from '@/components/ui/toast'

interface Benefit {
  id: string
  name: string
  description?: string
  category_name: string
  category_display_name?: string
  category_icon?: string
  icon?: string
}

interface BatchBenefitSelectionProps {
  companyId: string
  companyName: string
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  existingBenefitIds: string[]
}

type SelectionStep = 'select' | 'review' | 'submitting'

// Review Step Component
interface ReviewStepProps {
  selectedBenefits: Benefit[]
  onRemoveFromSelection: (benefitId: string) => void
}

function ReviewStep({ selectedBenefits, onRemoveFromSelection }: ReviewStepProps) {
  const groupedSelected = selectedBenefits.reduce((acc, benefit) => {
    const category = benefit.category_name || 'other'
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(benefit)
    return acc
  }, {} as Record<string, Benefit[]>)

  return (
    <div className="p-3 sm:p-6 h-full flex flex-col">
      <div className="text-center mb-6 flex-shrink-0">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Review Selected Benefits</h3>
        <p className="text-sm text-gray-600">
          Review the {selectedBenefits.length} benefits you&apos;ve selected before adding them to the company.
        </p>
      </div>

      <div className="flex-1 overflow-y-auto min-h-0 webkit-overflow-scrolling-touch">
        <div className="space-y-4 pb-4">
          {Object.entries(groupedSelected).map(([category, benefits]) => (
            <div key={category} className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3 capitalize">
                {category.replace('_', ' ')}
              </h4>
              <div className="space-y-2">
                {benefits.map(benefit => (
                  <div key={benefit.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      {benefit.icon && (
                        <span className="text-lg">{benefit.icon}</span>
                      )}
                      <div>
                        <div className="font-medium text-gray-900">{benefit.name}</div>
                        {benefit.description && (
                          <div className="text-sm text-gray-600">{benefit.description}</div>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => onRemoveFromSelection(benefit.id)}
                      className="text-red-600 hover:text-red-800 p-1 min-h-[44px] min-w-[44px] flex items-center justify-center"
                      title="Remove from selection"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Submitting Step Component
interface SubmittingStepProps {
  selectedCount: number
}

function SubmittingStep({ selectedCount }: SubmittingStepProps) {
  return (
    <div className="p-6 flex items-center justify-center min-h-[300px]">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h4 className="text-lg font-medium text-gray-900 mb-2">Adding Benefits</h4>
        <p className="text-gray-600">Adding {selectedCount} benefits to the company...</p>
      </div>
    </div>
  )
}

export function BatchBenefitSelection({
  companyId,
  companyName,
  isOpen,
  onClose,
  onSuccess,
  existingBenefitIds
}: BatchBenefitSelectionProps) {
  const [allBenefits, setAllBenefits] = useState<Benefit[]>([])
  const [selectedBenefitIds, setSelectedBenefitIds] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [currentStep, setCurrentStep] = useState<SelectionStep>('select')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [categories, setCategories] = useState([
    { key: 'all', label: 'All Categories' }
  ])
  const { showSuccess, showError, showBenefitAdded } = useToastHelpers()

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/benefit-categories')
      if (response.ok) {
        const data = await response.json()
        setCategories([
          { key: 'all', label: 'All Categories' },
          ...data.map((cat: { name: string; display_name: string }) => ({
            key: cat.name,
            label: cat.display_name
          }))
        ])
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const fetchAllBenefits = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/benefits')
      if (response.ok) {
        const data = await response.json()
        setAllBenefits(data.benefits || [])
      } else {
        setError('Failed to load benefits')
      }
    } catch (error) {
      console.error('Error fetching benefits:', error)
      setError('Failed to load benefits')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isOpen) {
      fetchCategories()
      fetchAllBenefits()
      setSelectedBenefitIds(new Set())
      setSearchTerm('')
      setSelectedCategory('all')
      setCurrentStep('select')
      setError(null)
    }
  }, [isOpen])

  // Filter available benefits (not already added to company)
  const availableBenefits = useMemo(() => {
    return allBenefits.filter(benefit =>
      !existingBenefitIds.includes(benefit.id)
    )
  }, [allBenefits, existingBenefitIds])

  // Filter benefits based on search and category
  const filteredBenefits = useMemo(() => {
    return availableBenefits.filter(benefit => {
      const matchesSearch = benefit.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || benefit.category_name === selectedCategory
      return matchesSearch && matchesCategory
    })
  }, [availableBenefits, searchTerm, selectedCategory])

  // Group filtered benefits by category
  const groupedBenefits = useMemo(() => {
    const grouped = filteredBenefits.reduce((acc, benefit) => {
      const category = benefit.category_name || 'other'
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(benefit)
      return acc
    }, {} as Record<string, Benefit[]>)

    // Sort categories and benefits within each category
    const sortedGrouped: Record<string, Benefit[]> = {}
    categories.slice(1).forEach(cat => { // Skip 'all' category
      if (grouped[cat.key] && grouped[cat.key].length > 0) {
        sortedGrouped[cat.key] = grouped[cat.key].sort((a, b) => a.name.localeCompare(b.name))
      }
    })

    return sortedGrouped
  }, [filteredBenefits, categories])

  const selectedBenefits = useMemo(() => {
    return allBenefits.filter(benefit => selectedBenefitIds.has(benefit.id))
  }, [allBenefits, selectedBenefitIds])

  const handleBenefitToggle = (benefitId: string) => {
    const newSelected = new Set(selectedBenefitIds)
    if (newSelected.has(benefitId)) {
      newSelected.delete(benefitId)
    } else {
      newSelected.add(benefitId)
    }
    setSelectedBenefitIds(newSelected)
  }

  const handleSelectAll = () => {
    const newSelected = new Set(selectedBenefitIds)
    filteredBenefits.forEach(benefit => {
      newSelected.add(benefit.id)
    })
    setSelectedBenefitIds(newSelected)
  }

  const handleDeselectAll = () => {
    const filteredIds = new Set(filteredBenefits.map(b => b.id))
    const newSelected = new Set(
      Array.from(selectedBenefitIds).filter(id => !filteredIds.has(id))
    )
    setSelectedBenefitIds(newSelected)
  }

  const handleProceedToReview = () => {
    if (selectedBenefitIds.size > 0) {
      setCurrentStep('review')
    }
  }

  const handleBackToSelection = () => {
    setCurrentStep('select')
  }

  const handleRemoveFromSelection = (benefitId: string) => {
    const newSelected = new Set(selectedBenefitIds)
    newSelected.delete(benefitId)
    setSelectedBenefitIds(newSelected)
  }

  const handleSubmitBatch = async () => {
    if (selectedBenefitIds.size === 0) {return}

    setCurrentStep('submitting')
    setError(null)

    try {
      const response = await fetch(`/api/companies/${companyId}/benefits/bulk`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          benefitIds: Array.from(selectedBenefitIds),
          action: 'add'
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Add benefit API response:', result)

        // Get selected benefit names for the toast
        const selectedBenefits = allBenefits.filter(b => selectedBenefitIds.has(b.id))
        const benefitCount = selectedBenefits.length

        if (benefitCount === 1) {
          // Single benefit - show specific verification CTA
          const benefitName = selectedBenefits[0].name
          // The bulk API returns the array directly, not wrapped in 'added'
          const addedBenefit = Array.isArray(result) ? result[0] : result.added?.[0]
          console.log('Added benefit:', addedBenefit)

          if (addedBenefit?.id) {
            console.log('Company benefit ID found:', addedBenefit.id)
            showBenefitAdded(benefitName, companyName, async () => {
              console.log('Verify Now button clicked for company benefit ID:', addedBenefit.id)
              try {
                // Call verification API
                const verifyResponse = await fetch('/api/benefit-verifications', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    companyBenefitId: addedBenefit.id,
                    status: 'confirmed',
                    reason: ''
                  })
                })

                console.log('Verification API response status:', verifyResponse.status)

                if (verifyResponse.ok) {
                  const verifyResult = await verifyResponse.json()
                  console.log('Verification successful:', verifyResult)
                  showSuccess(`"${benefitName}" has been verified! Thank you for helping other users.`)
                  // Trigger UI refresh
                  onSuccess()
                } else {
                  const error = await verifyResponse.json()
                  console.error('Verification API error:', error)
                  showSuccess(`Please verify "${benefitName}" manually: ${error.error || 'Verification failed'}`)
                }
              } catch (error) {
                console.error('Error verifying benefit:', error)
                showSuccess(`Please verify "${benefitName}" manually - verification failed`)
              }
            })
          } else {
            console.log('No company benefit ID found, falling back to manual message')
            showBenefitAdded(benefitName, companyName, () => {
              showSuccess(`Navigate to the company page to verify "${benefitName}"`)
            })
          }
        } else {
          // Multiple benefits - show general success with verification reminder
          showSuccess(
            `${benefitCount} benefits added to ${companyName}! Don't forget to verify them to help other users.`,
            { duration: 8000 }
          )
        }

        onSuccess()
        onClose()
      } else {
        const error = await response.json()
        setError(error.error || 'Failed to add benefits')
        setCurrentStep('review')
      }
    } catch (error) {
      console.error('Error adding benefits:', error)
      setError('Failed to add benefits')
      setCurrentStep('review')
    }
  }

  const handleClose = () => {
    if (currentStep !== 'submitting') {
      onClose()
    }
  }

  if (!isOpen) {return null}

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60] p-2 sm:p-4 overflow-hidden">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full h-[95vh] sm:h-[90vh] flex flex-col" role="dialog" aria-modal="true" aria-labelledby="batch-benefit-selection-title">
        {/* Header */}
        <div className="p-4 sm:p-6 border-b border-gray-200 flex-shrink-0">
          <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div className="flex-1 min-w-0">
              <h3 id="batch-benefit-selection-title" className="text-lg font-semibold text-gray-900">
                {currentStep === 'select' && 'Select Benefits'}
                {currentStep === 'review' && 'Review Selection'}
                {currentStep === 'submitting' && 'Adding Benefits'}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {currentStep === 'select' && `Choose multiple benefits to add to ${companyName}`}
                {currentStep === 'review' && `Review and confirm ${selectedBenefitIds.size} selected benefit${selectedBenefitIds.size !== 1 ? 's' : ''}`}
                {currentStep === 'submitting' && 'Please wait while we add the benefits...'}
              </p>
            </div>
            {currentStep !== 'submitting' && (
              <Button variant="outline" onClick={handleClose} className="w-full sm:w-auto">
                <X className="w-4 h-4" />
                <span className="ml-2 sm:hidden">Close</span>
              </Button>
            )}
          </div>

          {/* Progress indicator */}
          <div className="flex items-center mt-4 space-x-2">
            <div className={`w-3 h-3 rounded-full ${currentStep === 'select' ? 'bg-blue-600' : 'bg-green-600'}`} />
            <div className={`flex-1 h-1 ${currentStep === 'review' || currentStep === 'submitting' ? 'bg-green-600' : 'bg-gray-200'}`} />
            <div className={`w-3 h-3 rounded-full ${currentStep === 'review' || currentStep === 'submitting' ? 'bg-blue-600' : 'bg-gray-200'}`} />
            <div className={`flex-1 h-1 ${currentStep === 'submitting' ? 'bg-green-600' : 'bg-gray-200'}`} />
            <div className={`w-3 h-3 rounded-full ${currentStep === 'submitting' ? 'bg-blue-600' : 'bg-gray-200'}`} />
          </div>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-b border-red-200 flex-shrink-0">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {/* Content - scrollable area */}
        <div className="flex-1 overflow-hidden min-h-0" style={{ minHeight: '200px' }}>
          {currentStep === 'select' && (
            <SelectionStep
              filteredBenefits={filteredBenefits}
              groupedBenefits={groupedBenefits}
              selectedBenefitIds={selectedBenefitIds}
              searchTerm={searchTerm}
              selectedCategory={selectedCategory}
              categories={categories}
              loading={loading}
              onBenefitToggle={handleBenefitToggle}
              onSearchChange={setSearchTerm}
              onCategoryChange={setSelectedCategory}
              onSelectAll={handleSelectAll}
              onDeselectAll={handleDeselectAll}
            />
          )}

          {currentStep === 'review' && (
            <ReviewStep
              selectedBenefits={selectedBenefits}
              onRemoveFromSelection={handleRemoveFromSelection}
            />
          )}

          {currentStep === 'submitting' && (
            <SubmittingStep selectedCount={selectedBenefitIds.size} />
          )}
        </div>

        {/* Footer - always visible */}
        <div className="p-4 sm:p-6 border-t border-gray-200 flex-shrink-0 bg-white">
          <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div className="text-sm text-gray-600">
              {selectedBenefitIds.size} benefit{selectedBenefitIds.size !== 1 ? 's' : ''} selected
            </div>

            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-3">
              {currentStep === 'select' && (
                <>
                  <Button variant="outline" onClick={handleClose} className="w-full sm:w-auto">
                    Cancel
                  </Button>
                  <Button
                    onClick={handleProceedToReview}
                    disabled={selectedBenefitIds.size === 0}
                    className="flex items-center justify-center gap-2 w-full sm:w-auto min-h-[44px]"
                  >
                    Review Selection
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </>
              )}

              {currentStep === 'review' && (
                <>
                  <Button variant="outline" onClick={handleBackToSelection} className="w-full sm:w-auto min-h-[44px]">
                    Back to Selection
                  </Button>
                  <Button
                    onClick={handleSubmitBatch}
                    disabled={selectedBenefitIds.size === 0}
                    className="flex items-center justify-center gap-2 w-full sm:w-auto min-h-[44px]"
                  >
                    Add {selectedBenefitIds.size} Benefit{selectedBenefitIds.size !== 1 ? 's' : ''}
                    <Plus className="w-4 h-4" />
                  </Button>
                </>
              )}

              {currentStep === 'submitting' && (
                <div className="flex items-center justify-center gap-2 text-blue-600">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Adding benefits...
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Selection Step Component
interface SelectionStepProps {
  filteredBenefits: Benefit[]
  groupedBenefits: Record<string, Benefit[]>
  selectedBenefitIds: Set<string>
  searchTerm: string
  selectedCategory: string
  categories: Array<{ key: string; label: string }>
  loading: boolean
  onBenefitToggle: (benefitId: string) => void
  onSearchChange: (term: string) => void
  onCategoryChange: (category: string) => void
  onSelectAll: () => void
  onDeselectAll: () => void
}

function SelectionStep({
  filteredBenefits,
  groupedBenefits,
  selectedBenefitIds,
  searchTerm,
  selectedCategory,
  categories,
  loading,
  onBenefitToggle,
  onSearchChange,
  onCategoryChange,
  onSelectAll,
  onDeselectAll
}: SelectionStepProps) {
  return (
    <div className="p-3 sm:p-6 h-full flex flex-col" style={{ maxHeight: '100%' }}>
      {/* Search and Filter Controls - Fixed at top */}
      <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6 flex-shrink-0">
        <div className="relative">
          <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
          <input
            type="text"
            placeholder="Search benefits..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm sm:text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <select
            value={selectedCategory}
            onChange={(e) => onCategoryChange(e.target.value)}
            className="w-full sm:w-auto px-3 py-2 text-sm sm:text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
          >
            {categories.map(category => (
              <option key={category.key} value={category.key}>
                {category.label}
              </option>
            ))}
          </select>

          <div className="flex items-center space-x-2">
            <Button size="sm" variant="outline" onClick={onSelectAll}>
              Select All Visible
            </Button>
            <Button size="sm" variant="outline" onClick={onDeselectAll}>
              Deselect All Visible
            </Button>
          </div>
        </div>
      </div>

      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto min-h-0" style={{
        WebkitOverflowScrolling: 'touch',
        overscrollBehavior: 'contain',
        maxHeight: 'calc(100% - 120px)' // Account for header space
      }}>
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading benefits...</p>
          </div>
        ) : filteredBenefits.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-600">
              {searchTerm || selectedCategory !== 'all'
                ? 'No benefits found matching your criteria.'
                : 'All benefits have been added to this company.'}
            </p>
          </div>
        ) : (
          <div className="space-y-6 pb-4">
            {Object.entries(groupedBenefits).map(([categoryKey, benefits]) => {
              const categoryLabel = categories.find(c => c.key === categoryKey)?.label || categoryKey

              return (
                <div key={categoryKey} className="space-y-3">
                  <h4 className="font-medium text-gray-900 text-sm uppercase tracking-wide">
                    {categoryLabel}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {benefits.map((benefit) => (
                      <div
                        key={benefit.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedBenefitIds.has(benefit.id)
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => onBenefitToggle(benefit.id)}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`w-5 h-5 border-2 rounded flex items-center justify-center ${
                            selectedBenefitIds.has(benefit.id)
                              ? 'border-blue-500 bg-blue-500'
                              : 'border-gray-300'
                          }`}>
                            {selectedBenefitIds.has(benefit.id) && (
                              <Check className="w-3 h-3 text-white" />
                            )}
                          </div>
                          {benefit.icon && (
                            <span className="text-lg">{benefit.icon}</span>
                          )}
                          <div className="flex-1">
                            <h5 className="font-medium text-gray-900">{benefit.name}</h5>
                            <p className="text-sm text-gray-500">
                              {benefit.category_display_name || (benefit.category_name?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())) || 'Other'}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}

// Review Step Component
interface ReviewStepProps {
  selectedBenefits: Benefit[]
  onRemoveFromSelection: (benefitId: string) => void
}


