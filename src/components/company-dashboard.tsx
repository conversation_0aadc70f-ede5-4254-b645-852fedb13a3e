'use client'

import { useState, useEffect } from 'react'
import { Building2, Mail } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { StatusFocusedBenefitManagement } from '@/components/status-focused-benefit-management'
import type { Company } from '@/types/database'

interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
}

interface CompanyDashboardProps {
  user: User
  company: Company | null
}

export function CompanyDashboard({ user, company }: CompanyDashboardProps) {
  const [isReporting, setIsReporting] = useState(false)
  const [reportStatus, setReportStatus] = useState<{type: 'success' | 'error', message: string} | null>(null)
  const [canManageCompany, setCanManageCompany] = useState(false)

  const userEmail = user.email
  const emailDomain = userEmail?.split('@')[1]

  // Check if user can manage this company based on email domain
  useEffect(() => {
    if (company && userEmail) {
      const userDomain = emailDomain
      const companyDomain = company.domain
      setCanManageCompany(userDomain === companyDomain)
    }
  }, [company, userEmail, emailDomain])





  const handleReportMissingCompany = async () => {
    setIsReporting(true)
    setReportStatus(null)

    try {
      const response = await fetch('/api/report-missing-company', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userEmail: user.email,
          emailDomain: emailDomain,
          firstName: user.firstName,
          lastName: user.lastName
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setReportStatus({
          type: 'success',
          message: data.message || 'Thank you! We have received your report and will review it shortly. You will be notified via email once your company is added.'
        })
      } else {
        setReportStatus({
          type: 'error',
          message: data.error || 'Failed to submit report. Please try again.'
        })
      }
    } catch {
      setReportStatus({
        type: 'error',
        message: 'Network error. Please try again.'
      })
    } finally {
      setIsReporting(false)
    }
  }

  const scrollToBenefits = () => {
    const benefitsSection = document.querySelector('[data-section="benefits"]')
    if (benefitsSection) {
      benefitsSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  if (!company) {
    return (
      <div className="space-y-6">
        {/* No Company Found */}
        <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
          <Building2 className="w-12 h-12 text-gray-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            No Company Found
          </h2>
          <p className="text-gray-600 mb-6">
            We couldn&apos;t find a company associated with your email domain ({emailDomain}).
          </p>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              If your company should be listed, please click the button below to report a missing company.
            </p>
            <Button onClick={handleReportMissingCompany} disabled={isReporting}>
              {isReporting ? 'Reporting...' : 'Report Missing Company'}
            </Button>
            {reportStatus && (
              <div className={`text-sm p-3 rounded-md ${
                reportStatus.type === 'success'
                  ? 'bg-green-50 text-green-700 border border-green-200'
                  : 'bg-red-50 text-red-700 border border-red-200'
              }`}>
                {reportStatus.message}
              </div>
            )}
          </div>
        </div>

        {/* Email Domain Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <Mail className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900 mb-1">
                How Company Verification Works
              </h3>
              <p className="text-blue-700 text-sm">
                We match your email domain ({emailDomain}) with companies in our database. 
                If your company isn&apos;t listed yet, you can request to add it by contacting our support team.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Company Overview */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Building2 className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {company.name}
              </h2>
              <p className="text-gray-600">
                {company.locations && company.locations.length > 0
                  ? company.locations.map(loc => loc.city).join(', ')
                  : 'Location not specified'
                } • {company.industry}
              </p>
            </div>
          </div>
        </div>

        {company.description && (
          <p className="text-gray-600 mb-4">{company.description}</p>
        )}

        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Size:</span>
            <p className="font-medium text-gray-900 capitalize">{company.size}</p>
          </div>
          <div>
            <span className="text-gray-500">Domain:</span>
            <p className="font-medium text-gray-900">{company.domain || 'Not set'}</p>
          </div>
          <div>
            <span className="text-gray-500">Career URL:</span>
            <p className="font-medium text-gray-900">
              {company.career_url ? (
                <a
                  href={company.career_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  View Careers Page
                </a>
              ) : (
                'Not set'
              )}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-2 gap-4 text-sm mt-4">
          <div>
            <span className="text-gray-500">Your Email:</span>
            <p className="font-medium text-gray-900">{userEmail}</p>
          </div>
        </div>
      </div>

      {/* Benefits Management */}
      <div className="bg-white rounded-lg shadow-sm border p-6" data-section="benefits">
        <StatusFocusedBenefitManagement
          companyId={company.id}
          companyName={company.name}
          canManage={canManageCompany}
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">

          <Button
            variant="outline"
            className="justify-start"
            onClick={scrollToBenefits}
          >
            View Benefits
          </Button>
          <Button
            variant="outline"
            className="justify-start"
            onClick={() => window.open(`/companies/${company.id}`, '_blank')}
          >
            View Public Profile
          </Button>
        </div>
      </div>


    </div>
  )
}
