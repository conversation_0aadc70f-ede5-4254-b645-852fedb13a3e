'use client'

import { useImpersonation } from '@/hooks/use-impersonation'
import { ImpersonationBanner } from '@/components/impersonation-banner'

interface LayoutWrapperProps {
  children: React.ReactNode
}

export function LayoutWrapper({ children }: LayoutWrapperProps) {
  const { isImpersonating, impersonatedUserEmail, originalAdminEmail, exitImpersonation } = useImpersonation()

  return (
    <>
      {isImpersonating && impersonatedUserEmail && originalAdminEmail && (
        <ImpersonationBanner
          impersonatedUserEmail={impersonatedUserEmail}
          originalAdminEmail={originalAdminEmail}
          onExitImpersonation={exitImpersonation}
        />
      )}
      {children}
    </>
  )
}
