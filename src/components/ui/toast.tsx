'use client'

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

export interface ToastAction {
  label: string
  onClick: () => void
  variant?: 'default' | 'destructive' | 'outline'
}

export interface Toast {
  id: string
  title?: string
  description: string
  variant?: 'default' | 'success' | 'error' | 'warning' | 'info'
  duration?: number
  action?: ToastAction
  onClose?: () => void
}

interface ToastContextType {
  toasts: Toast[]
  addToast: (toast: Omit<Toast, 'id'>) => string
  removeToast: (id: string) => void
  clearToasts: () => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([])

  const addToast = useCallback((toast: Omit<Toast, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 11)
    const newToast: Toast = {
      ...toast,
      id,
      duration: toast.duration ?? 5000,
    }

    setToasts(prev => [...prev, newToast])

    // Auto-remove toast after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        removeToast(id)
      }, newToast.duration)
    }

    return id
  }, [])

  const removeToast = useCallback((id: string) => {
    setToasts(prev => {
      const toast = prev.find(t => t.id === id)
      if (toast?.onClose) {
        toast.onClose()
      }
      return prev.filter(t => t.id !== id)
    })
  }, [])

  const clearToasts = useCallback(() => {
    setToasts([])
  }, [])

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast, clearToasts }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  )
}

export function useToast() {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

function ToastContainer() {
  const { toasts } = useToast()

  if (toasts.length === 0) {
    return null
  }

  return (
    <div className="fixed top-4 right-4 z-[9999] flex flex-col gap-2 max-w-sm w-full pointer-events-none">
      {toasts.map(toast => (
        <ToastComponent key={toast.id} toast={toast} />
      ))}
    </div>
  )
}

function ToastComponent({ toast }: { toast: Toast }) {
  const { removeToast } = useToast()
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Trigger animation
    const timer = setTimeout(() => setIsVisible(true), 10)
    return () => clearTimeout(timer)
  }, [])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(() => removeToast(toast.id), 150)
  }

  const getIcon = () => {
    switch (toast.variant) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-600" />
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />
      case 'info':
        return <Info className="w-5 h-5 text-blue-600" />
      default:
        return <Info className="w-5 h-5 text-gray-600" />
    }
  }

  const getBackgroundColor = () => {
    switch (toast.variant) {
      case 'success':
        return 'bg-green-50 border-green-200'
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200'
      case 'info':
        return 'bg-blue-50 border-blue-200'
      default:
        return 'bg-white border-gray-200'
    }
  }

  return (
    <div
      className={cn(
        'transform transition-all duration-300 ease-in-out pointer-events-auto',
        'border rounded-lg shadow-lg p-4 max-w-sm w-full',
        getBackgroundColor(),
        isVisible
          ? 'translate-x-0 opacity-100'
          : 'translate-x-full opacity-0'
      )}
    >
      <div className="flex items-start gap-3">
        {getIcon()}
        <div className="flex-1 min-w-0">
          {toast.title && (
            <h4 className="font-medium text-gray-900 text-sm mb-1">
              {toast.title}
            </h4>
          )}
          <p className="text-sm text-gray-700 break-words">
            {toast.description}
          </p>
          {toast.action && (
            <div className="mt-3">
              <Button
                size="sm"
                variant={toast.action.variant || 'default'}
                onClick={() => {
                  toast.action!.onClick()
                  handleClose()
                }}
                className="text-xs"
              >
                {toast.action.label}
              </Button>
            </div>
          )}
        </div>
        <button
          onClick={handleClose}
          className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

// Convenience functions for common toast types
export function useToastHelpers() {
  const { addToast } = useToast()

  const showSuccess = useCallback((description: string, options?: Partial<Toast>) => {
    return addToast({
      variant: 'success',
      description,
      ...options,
    })
  }, [addToast])

  const showError = useCallback((description: string, options?: Partial<Toast>) => {
    return addToast({
      variant: 'error',
      description,
      ...options,
    })
  }, [addToast])

  const showWarning = useCallback((description: string, options?: Partial<Toast>) => {
    return addToast({
      variant: 'warning',
      description,
      ...options,
    })
  }, [addToast])

  const showInfo = useCallback((description: string, options?: Partial<Toast>) => {
    return addToast({
      variant: 'info',
      description,
      ...options,
    })
  }, [addToast])

  const showBenefitAdded = useCallback((
    benefitName: string,
    companyName: string,
    onVerifyClick: () => void
  ) => {
    return addToast({
      variant: 'success',
      title: 'Benefit Added!',
      description: `"${benefitName}" has been added to ${companyName}. Help others by verifying it!`,
      duration: 8000, // Longer duration for action-required toasts
      action: {
        label: 'Verify Now',
        onClick: onVerifyClick,
        variant: 'default'
      }
    })
  }, [addToast])

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showBenefitAdded,
  }
}
