import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-gray-300 bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        search: "bg-primary text-primary-foreground hover:bg-primary/90",
        warning:
          "bg-warning text-warning-foreground hover:bg-warning/90",
      },
      size: {
        default: "h-11 px-4 py-2 sm:h-10", // Mobile-first: 44px (h-11) on mobile, 40px (h-10) on desktop
        sm: "h-11 rounded-md px-3 sm:h-10", // Mobile-first: 44px (h-11) on mobile, 40px (h-10) on desktop
        lg: "h-12 rounded-md px-8 sm:h-11", // Mobile-first: 48px (h-12) on mobile, 44px (h-11) on desktop
        icon: "h-11 w-11 sm:h-10 sm:w-10", // Mobile-first: 44px on mobile, 40px on desktop
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild: _asChild = false, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
