/* Admin Dashboard Mobile Improvements */

/* Hide scrollbars for tab navigation */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Line clamp utility for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Ensure mobile tables don't break layout */
.mobile-table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.mobile-table-container::-webkit-scrollbar {
  height: 4px;
}

.mobile-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.mobile-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.mobile-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Mobile-friendly button spacing */
@media (max-width: 640px) {
  .mobile-button-stack {
    flex-direction: column;
    align-items: stretch;
  }
  
  .mobile-button-stack > * {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .mobile-button-stack > *:last-child {
    margin-bottom: 0;
  }
}

/* Prevent text overflow in mobile cards */
.mobile-card-content {
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Improve mobile form layouts */
@media (max-width: 640px) {
  .mobile-form-stack {
    flex-direction: column;
  }
  
  .mobile-form-stack > * {
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .mobile-form-stack > *:last-child {
    margin-bottom: 0;
  }
}

/* Mobile-friendly spacing adjustments */
@media (max-width: 640px) {
  .mobile-spacing-tight {
    padding: 1rem;
  }

  .mobile-spacing-normal {
    padding: 1.5rem;
  }
}

/* Optimized spacing for larger mobile devices (like Samsung Galaxy S25+) */
@media (min-width: 390px) and (max-width: 640px) {
  /* Reduce padding on mobile cards for larger mobile screens */
  .lg\:hidden .bg-gray-50 {
    padding: 0.75rem !important;
  }

  /* Optimize admin interface boxes for larger mobile screens */
  .bg-white.rounded-lg.shadow-sm.border {
    padding: 1rem !important;
  }

  /* Better spacing for mobile card content */
  .mobile-card-optimized {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }
}

/* Specific optimizations for very large mobile screens */
@media (min-width: 430px) and (max-width: 640px) {
  /* Further reduce padding for very large mobile screens */
  .lg\:hidden .bg-gray-50 {
    padding: 0.5rem !important;
  }

  /* Compact admin interface for large mobile screens */
  .admin-mobile-compact {
    padding: 0.75rem !important;
  }

  /* Optimize grid spacing */
  .grid.gap-4 {
    gap: 0.75rem !important;
  }

  .space-y-4 > * + * {
    margin-top: 0.75rem !important;
  }
}
