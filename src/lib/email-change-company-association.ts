import { query } from '@/lib/local-db'
import { extractDomainFromEmail, isValidEmail } from '@/lib/utils'
import { getCompanyByDomain } from '@/lib/database'

// Common personal email domains that should not trigger company associations
const PERSONAL_EMAIL_DOMAINS = new Set([
  'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'icloud.com',
  'aol.com', 'protonmail.com', 'tutanota.com', 'gmx.com', 'web.de',
  't-online.de', 'freenet.de', 'arcor.de', 'mail.de'
])

// Rate limiting for verification emails (max 3 per hour per user)
const VERIFICATION_EMAIL_RATE_LIMIT = 3
const VERIFICATION_EMAIL_WINDOW_HOURS = 1

/**
 * Checks if user has exceeded verification email rate limit
 */
export async function checkVerificationEmailRateLimit(userId: string): Promise<{
  allowed: boolean
  message: string
  remainingAttempts?: number
}> {
  // Check if rate limiting is disabled (for testing)
  const rateLimitingDisabled = process.env.DISABLE_RATE_LIMITING === 'true'

  if (rateLimitingDisabled) {
    return {
      allowed: true,
      message: 'Rate limiting disabled for testing',
      remainingAttempts: VERIFICATION_EMAIL_RATE_LIMIT
    }
  }

  try {
    const result = await query(`
      SELECT COUNT(*) as count
      FROM company_verification_tokens
      WHERE user_id = $1
        AND created_at > NOW() - INTERVAL '${VERIFICATION_EMAIL_WINDOW_HOURS} hours'
    `, [userId])

    const count = parseInt(result.rows[0]?.count || '0')
    const remainingAttempts = Math.max(0, VERIFICATION_EMAIL_RATE_LIMIT - count)

    if (count >= VERIFICATION_EMAIL_RATE_LIMIT) {
      return {
        allowed: false,
        message: `You can only request ${VERIFICATION_EMAIL_RATE_LIMIT} verification emails per ${VERIFICATION_EMAIL_WINDOW_HOURS} hour(s). Please try again later.`,
        remainingAttempts: 0
      }
    }

    return {
      allowed: true,
      message: `${remainingAttempts} verification email(s) remaining this hour.`,
      remainingAttempts
    }
  } catch (error) {
    console.error('Error checking verification email rate limit:', error)
    return {
      allowed: false,
      message: 'Unable to check rate limit. Please try again later.'
    }
  }
}

// Note: Company verification token functions removed as users are now automatically
// assigned to companies via email domain matching. Manual verification is no longer needed.

// Note: Company verification functions removed as users are now automatically
// assigned to companies via email domain matching. Manual verification is no longer needed.

/**
 * Records that a verification email was sent (for rate limiting)
 */
export async function recordVerificationEmailSent(_userId: string): Promise<void> {
  // This is automatically handled by the createCompanyVerificationToken function
  // which inserts a record with the current timestamp
}

export interface EmailChangeCompanyResult {
  success: boolean
  action: 'no_change' | 'company_updated' | 'company_removed' | 'verification_required'
  previousCompanyId?: string | null
  newCompanyId?: string | null
  companyName?: string
  verificationToken?: string
  message: string
  error?: string
}

export interface CompanyVerificationToken {
  id: string
  token: string
  user_id: string
  user_email: string
  company_id: string
  expires_at: string
  used_at?: string
  created_at: string
}

/**
 * Handles company association changes when a user's email address is updated
 */
export async function handleEmailChangeCompanyAssociation(
  userId: string,
  oldEmail: string,
  newEmail: string
): Promise<EmailChangeCompanyResult> {
  try {
    // Validate email format
    if (!isValidEmail(newEmail)) {
      return {
        success: false,
        action: 'no_change',
        message: 'Invalid email format',
        error: 'Invalid email format'
      }
    }

    const oldDomain = extractDomainFromEmail(oldEmail)
    const newDomain = extractDomainFromEmail(newEmail)

    // If domain hasn't changed, no action needed
    if (oldDomain === newDomain) {
      return {
        success: true,
        action: 'no_change',
        message: 'Email domain unchanged, no company association update needed'
      }
    }

    // Check if new domain is a personal email domain
    if (PERSONAL_EMAIL_DOMAINS.has(newDomain)) {
      // Remove company association if switching to personal email
      const currentUserResult = await query(
        'SELECT company_id FROM users WHERE id = $1',
        [userId]
      )

      if (currentUserResult.rows.length > 0 && currentUserResult.rows[0].company_id) {
        await query(
          'UPDATE users SET company_id = NULL WHERE id = $1',
          [userId]
        )

        return {
          success: true,
          action: 'company_removed',
          previousCompanyId: currentUserResult.rows[0].company_id,
          newCompanyId: null,
          message: 'Company association removed as new email is a personal email address'
        }
      }

      return {
        success: true,
        action: 'no_change',
        message: 'Personal email domain detected, no company association needed'
      }
    }

    // Get current user's company association
    const currentUserResult = await query(
      'SELECT company_id FROM users WHERE id = $1',
      [userId]
    )

    if (currentUserResult.rows.length === 0) {
      return {
        success: false,
        action: 'no_change',
        message: 'User not found',
        error: 'User not found'
      }
    }

    const currentCompanyId = currentUserResult.rows[0].company_id

    // Find company for new email domain
    const newCompany = await getCompanyByDomain(newDomain)

    // Case 1: New domain matches a company
    if (newCompany) {
      // If it's the same company, no change needed
      if (currentCompanyId === newCompany.id) {
        return {
          success: true,
          action: 'no_change',
          previousCompanyId: currentCompanyId,
          newCompanyId: newCompany.id,
          companyName: newCompany.name,
          message: `Email domain still matches ${newCompany.name}, no change needed`
        }
      }

      // Different company - automatically assign (no verification needed)
      // Update user's company association
      await query(
        'UPDATE users SET company_id = $1 WHERE id = $2',
        [newCompany.id, userId]
      )

      return {
        success: true,
        action: 'company_updated',
        previousCompanyId: currentCompanyId,
        newCompanyId: newCompany.id,
        companyName: newCompany.name,
        message: `Your account has been automatically associated with ${newCompany.name} based on your new email domain.`
      }
    }

    // Case 2: New domain doesn't match any company - remove association
    if (currentCompanyId) {
      await query(
        'UPDATE users SET company_id = NULL WHERE id = $1',
        [userId]
      )

      return {
        success: true,
        action: 'company_removed',
        previousCompanyId: currentCompanyId,
        newCompanyId: null,
        message: 'Company association removed as new email domain does not match any registered company'
      }
    }

    // Case 3: No current company and new domain doesn't match - no change
    return {
      success: true,
      action: 'no_change',
      message: 'No company association changes needed'
    }

  } catch (error) {
    console.error('Error handling email change company association:', error)
    return {
      success: false,
      action: 'no_change',
      message: 'Failed to process company association change',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}



/**
 * Verifies a company association token and updates user's company
 */
export async function verifyCompanyAssociationToken(token: string): Promise<{
  success: boolean
  userId?: string
  companyId?: string
  companyName?: string
  message: string
  error?: string
}> {
  try {
    // Find and validate token
    const tokenResult = await query(
      `SELECT cvt.*, c.name as company_name
       FROM company_verification_tokens cvt
       JOIN companies c ON cvt.company_id = c.id
       WHERE cvt.token = $1 AND cvt.used_at IS NULL AND cvt.expires_at > NOW()`,
      [token]
    )

    if (tokenResult.rows.length === 0) {
      return {
        success: false,
        message: 'Invalid or expired verification token',
        error: 'Token not found or expired'
      }
    }

    const tokenData = tokenResult.rows[0]

    // Update user's company association
    await query(
      'UPDATE users SET company_id = $1 WHERE id = $2',
      [tokenData.company_id, tokenData.user_id]
    )

    // Mark token as used
    await query(
      'UPDATE company_verification_tokens SET used_at = NOW() WHERE token = $1',
      [token]
    )

    return {
      success: true,
      userId: tokenData.user_id,
      companyId: tokenData.company_id,
      companyName: tokenData.company_name,
      message: `Successfully associated with ${tokenData.company_name}`
    }

  } catch (error) {
    console.error('Error verifying company association token:', error)
    return {
      success: false,
      message: 'Failed to verify company association',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
