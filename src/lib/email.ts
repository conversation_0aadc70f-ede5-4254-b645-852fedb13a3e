import nodemailer from 'nodemailer'
import { v4 as uuidv4 } from 'uuid'

// Email configuration
const smtpPort = parseInt(process.env.SMTP_PORT || '1025')
const isProduction = process.env.NODE_ENV === 'production'
const needsAuth = isProduction || (process.env.SMTP_USER && process.env.SMTP_PASS)

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'localhost',
  port: smtpPort,
  secure: smtpPort === 465, // true for 465 (SSL), false for other ports (TLS/STARTTLS)
  auth: needsAuth ? {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  } : undefined, // No auth needed for MailHog in development
  // Additional options for better deliverability
  ...(isProduction && {
    tls: {
      rejectUnauthorized: true
    }
  })
})

export interface EmailOptions {
  to: string
  subject: string
  html: string
  text?: string
}

export async function sendEmail(options: EmailOptions) {
  try {
    const fromEmail = process.env.FROM_EMAIL || '<EMAIL>'

    // Log email attempt (without sensitive data)
    console.log(`Attempting to send email to: ${options.to}, subject: ${options.subject}`)
    console.log(`Using SMTP: ${process.env.SMTP_HOST}:${process.env.SMTP_PORT}`)

    const info = await transporter.sendMail({
      from: fromEmail,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
    })

    console.log('Email sent successfully:', {
      messageId: info.messageId,
      to: options.to,
      subject: options.subject,
      response: info.response
    })

    return { success: true, messageId: info.messageId }
  } catch (error) {
    console.error('Error sending email:', {
      error: error instanceof Error ? error.message : error,
      to: options.to,
      subject: options.subject,
      smtpHost: process.env.SMTP_HOST,
      smtpPort: process.env.SMTP_PORT
    })
    throw new Error(`Failed to send email: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export function generateVerificationToken(): string {
  return uuidv4()
}




