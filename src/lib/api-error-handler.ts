import { NextRequest, NextResponse } from 'next/server'
import { logger } from './logger'
import { APIAccessDeniedError } from './api-access-control'
import { isNextRedirectError } from './auth'

/**
 * Determines if an error should include stack trace based on log level and error type
 */
function shouldIncludeStackTrace(error: Error): boolean {
  const logLevel = process.env.LOG_LEVEL || 'info'
  
  // Never include stack traces for expected security errors
  if (error instanceof APIAccessDeniedError) {
    return false
  }
  
  // Include stack traces only for debug level or actual server errors
  return logLevel === 'debug' || logLevel === 'error'
}

/**
 * Logs an API error with appropriate verbosity based on log level
 */
export function logAPIError(
  error: Error, 
  request: NextRequest, 
  context?: Record<string, unknown>
): void {
  const url = new URL(request.url)
  const userAgent = request.headers.get('user-agent') || 'unknown'
  const ip = request.headers.get('x-forwarded-for')?.split(',')[0] || 
            request.headers.get('x-real-ip') || 
            'unknown'

  const logContext = {
    url: url.pathname,
    method: request.method,
    userAgent: shouldIncludeStackTrace(error) ? userAgent : userAgent.substring(0, 50) + '...',
    ip,
    errorType: error.constructor.name,
    ...context
  }

  // Handle different error types with appropriate log levels
  if (error instanceof APIAccessDeniedError) {
    // These are expected security events - log at debug level
    logger.debug('API access denied', logContext)
  } else if (error.message.includes('Authentication required') || 
             error.message.includes('Admin access required')) {
    // Authentication/authorization errors - log at info level
    logger.info('API authorization error', {
      ...logContext,
      error: error.message
    })
  } else {
    // Actual server errors - log at error level with full context
    logger.error('API server error', {
      ...logContext,
      error: shouldIncludeStackTrace(error) ? error : error.message
    })
  }
}

/**
 * Handles API errors and returns appropriate responses with minimal logging for expected errors
 */
export function handleAPIError(error: Error, request: NextRequest): NextResponse {
  // Log the error appropriately
  logAPIError(error, request)

  // Return appropriate response based on error type
  if (error instanceof APIAccessDeniedError) {
    return NextResponse.json(
      { error: error.message },
      { status: error.statusCode }
    )
  }

  if (error.message.includes('Authentication required')) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    )
  }

  if (error.message.includes('Admin access required')) {
    return NextResponse.json(
      { error: 'Admin access required' },
      { status: 403 }
    )
  }

  // For other errors, return generic server error
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  )
}

/**
 * Wrapper function for API routes to handle errors consistently
 */
export function withErrorHandling<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      return await handler(request, ...args)
    } catch (error) {
      // Handle Next.js redirect errors - these should be re-thrown, not caught
      if (isNextRedirectError(error)) {
        throw error
      }

      if (error instanceof Error) {
        return handleAPIError(error, request)
      }

      // Handle non-Error objects
      const errorMessage = typeof error === 'string' ? error : 'Unknown error'
      return handleAPIError(new Error(errorMessage), request)
    }
  }
}
