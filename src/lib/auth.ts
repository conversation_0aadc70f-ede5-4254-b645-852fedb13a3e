import { extractDomainFromEmail } from './utils'
import { getCompanyByDomain, getCompanyById } from './database'
import { getCurrentUser as getLocalUser, requireAuth as requireLocalAuth } from './local-auth'

export function isNextRedirectError(error: unknown): boolean {
  try {
    const err: any = error as any
    if (typeof err?.digest === 'string' && err.digest.startsWith('NEXT_REDIRECT')) {
      return true
    }
    if (err instanceof Error && typeof err.message === 'string' && err.message.includes('NEXT_REDIRECT')) {
      return true
    }
  } catch {
    // ignore
  }
  return false
}

// Always use local authentication
export async function getCurrentUser() {
  return await getLocalUser()
}

export async function requireAuth() {
  const user = await requireLocalAuth()
  return user.id
}

export async function requireAdmin() {
  try {
    const user = await requireLocalAuth()
    if (user.role !== 'admin') {
      throw new Error('Admin access required')
    }
    return user
  } catch (error) {
    // Re-throw authentication errors (like redirects)
    if (isNextRedirectError(error)) {
      throw error as any
    }
    // For admin access errors, throw a specific error that can be caught
    throw new Error('Admin access required')
  }
}

export async function isAdmin() {
  const user = await getCurrentUser()
  return user?.role === 'admin'
}

export async function getUserCompany() {
  const user = await getCurrentUser()
  if (!user?.email) {
    return null
  }

  // First, check if user has an explicit company_id
  if (user.companyId) {
    const company = await getCompanyById(user.companyId)
    if (company) {
      return company
    }
  }

  // Fallback to email domain matching for backward compatibility
  const email = user.email
  if (!email) {return null}

  const domain = extractDomainFromEmail(email)
  const company = await getCompanyByDomain(domain)

  return company
}

export async function canUserManageCompany(companyId: string) {
  const user = await getCurrentUser()
  if (!user?.email) {
    return false
  }

  // First, check if user has an explicit company_id that matches
  if (user.companyId === companyId) {
    return true
  }

  // Fallback to email domain matching for backward compatibility
  const email = user.email
  if (!email) {return false}

  const domain = extractDomainFromEmail(email)
  const company = await getCompanyByDomain(domain)

  return company?.id === companyId
}



export function isValidCompanyEmail(email: string, companyDomain: string): boolean {
  const emailDomain = extractDomainFromEmail(email)
  return emailDomain === companyDomain.toLowerCase()
}
