import { Metadata } from 'next'
import { getCompanyById } from '@/lib/database'
import type { CompanyBenefit, CompanyLocation } from '@/types/database'

export async function generateCompanyMetadata(
  params: { id: string }
): Promise<Metadata> {
  try {
    const company = await getCompanyById(params.id)
    
    if (!company) {
      return {
        title: 'Company Not Found',
        description: 'The requested company could not be found.',
      }
    }

    const benefitNames = company.company_benefits
      ?.map((cb: any) => cb.benefit?.name)
      .filter(Boolean)
      .slice(0, 5) // Limit to first 5 benefits
      .join(', ') || ''

    const locationNames = company.locations
      ?.map((l: any) => l.city)
      .filter(Boolean)
      .slice(0, 3) // Limit to first 3 locations
      .join(', ') || ''

    const title = `${company.name} - Employee Benefits & Company Info`
    const description = `Discover ${company.name}'s employee benefits${benefitNames ? ` including ${benefitNames}` : ''}. ${company.description || `Learn about ${company.name}'s workplace culture and benefits.`}${locationNames ? ` Located in ${locationNames}.` : ''}`

    return {
      title,
      description: description.slice(0, 160), // Limit description length
      keywords: [
        company.name,
        `${company.name} benefits`,
        `${company.name} employee benefits`,
        company.industry,
        ...benefitNames.split(', ').filter(Boolean),
        ...locationNames.split(', ').filter(Boolean),
        'company benefits',
        'employee benefits',
        'workplace benefits'
      ].filter(Boolean),
      openGraph: {
        title,
        description: description.slice(0, 160),
        type: 'website',
        url: `https://benefitlens.de/companies/${company.id}`,
      },
      twitter: {
        card: 'summary',
        title,
        description: description.slice(0, 160),
      },
      alternates: {
        canonical: `/companies/${company.id}`,
      },
    }
  } catch (error) {
    console.error('Error generating company metadata:', error)
    return {
      title: 'Company Information',
      description: 'View company information and employee benefits.',
    }
  }
}
