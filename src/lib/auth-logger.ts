import { query } from '@/lib/local-db'
import { logger } from '@/lib/logger'

export interface AuthLogEntry {
  id?: string
  event_type: 'sign_in_request' | 'sign_up_request' | 'magic_link_verification' | 'session_creation' | 'rate_limit_hit'
  status: 'success' | 'failure'
  email?: string
  error_type?: string
  error_message?: string
  ip_address?: string
  user_agent?: string
  token_used?: string
  failure_reason?: string
  additional_context?: Record<string, unknown>
  created_at?: string
}

/**
 * Log an authentication event to both the structured logger and database
 */
export async function logAuthEvent(entry: AuthLogEntry): Promise<void> {
  try {
    // Log to structured logger first
    const logLevel = entry.status === 'failure' ? 'warn' : 'info'
    const message = `Auth ${entry.event_type}: ${entry.status}`
    
    logger[logLevel](message, {
      email: entry.email,
      eventType: entry.event_type,
      status: entry.status,
      errorType: entry.error_type,
      errorMessage: entry.error_message,
      ip: entry.ip_address,
      userAgent: entry.user_agent,
      failureReason: entry.failure_reason,
      additionalContext: entry.additional_context
    })

    // Store in database for admin review
    await query(`
      INSERT INTO auth_logs (
        event_type, status, email, error_type, error_message, 
        ip_address, user_agent, token_used, failure_reason, additional_context
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
    `, [
      entry.event_type,
      entry.status,
      entry.email || null,
      entry.error_type || null,
      entry.error_message || null,
      entry.ip_address || null,
      entry.user_agent || null,
      entry.token_used || null,
      entry.failure_reason || null,
      entry.additional_context ? JSON.stringify(entry.additional_context) : null
    ])

  } catch (error) {
    // Don't fail the main operation if logging fails
    console.error('Failed to log auth event:', error)
  }
}

/**
 * Log a successful sign-in request
 */
export async function logSignInRequest(email: string, ip?: string, userAgent?: string): Promise<void> {
  await logAuthEvent({
    event_type: 'sign_in_request',
    status: 'success',
    email,
    ip_address: ip,
    user_agent: userAgent
  })
}

/**
 * Log a failed sign-in request
 */
export async function logSignInFailure(
  email: string, 
  errorType: string, 
  errorMessage: string, 
  ip?: string, 
  userAgent?: string,
  additionalContext?: Record<string, unknown>
): Promise<void> {
  await logAuthEvent({
    event_type: 'sign_in_request',
    status: 'failure',
    email,
    error_type: errorType,
    error_message: errorMessage,
    failure_reason: getFailureReason(errorType, errorMessage),
    ip_address: ip,
    user_agent: userAgent,
    additional_context: additionalContext
  })
}

/**
 * Log a successful sign-up request
 */
export async function logSignUpRequest(email: string, ip?: string, userAgent?: string): Promise<void> {
  await logAuthEvent({
    event_type: 'sign_up_request',
    status: 'success',
    email,
    ip_address: ip,
    user_agent: userAgent
  })
}

/**
 * Log a failed sign-up request
 */
export async function logSignUpFailure(
  email: string, 
  errorType: string, 
  errorMessage: string, 
  ip?: string, 
  userAgent?: string,
  additionalContext?: Record<string, unknown>
): Promise<void> {
  await logAuthEvent({
    event_type: 'sign_up_request',
    status: 'failure',
    email,
    error_type: errorType,
    error_message: errorMessage,
    failure_reason: getFailureReason(errorType, errorMessage),
    ip_address: ip,
    user_agent: userAgent,
    additional_context: additionalContext
  })
}

/**
 * Log a successful magic link verification
 */
export async function logMagicLinkSuccess(
  email: string, 
  tokenUsed: string, 
  ip?: string, 
  userAgent?: string
): Promise<void> {
  await logAuthEvent({
    event_type: 'magic_link_verification',
    status: 'success',
    email,
    token_used: tokenUsed.substring(0, 8) + '...', // Only log partial token for security
    ip_address: ip,
    user_agent: userAgent
  })
}

/**
 * Log a failed magic link verification
 */
export async function logMagicLinkFailure(
  tokenUsed: string,
  errorType: string,
  errorMessage: string,
  email?: string,
  ip?: string,
  userAgent?: string,
  additionalContext?: Record<string, unknown>
): Promise<void> {
  await logAuthEvent({
    event_type: 'magic_link_verification',
    status: 'failure',
    email,
    error_type: errorType,
    error_message: errorMessage,
    failure_reason: getFailureReason(errorType, errorMessage),
    token_used: tokenUsed.substring(0, 8) + '...', // Only log partial token for security
    ip_address: ip,
    user_agent: userAgent,
    additional_context: additionalContext
  })
}

/**
 * Log a rate limit hit
 */
export async function logRateLimitHit(
  email: string,
  eventType: 'sign_in_request' | 'sign_up_request',
  ip?: string,
  userAgent?: string
): Promise<void> {
  await logAuthEvent({
    event_type: 'rate_limit_hit',
    status: 'failure',
    email,
    error_type: 'rate_limit_exceeded',
    error_message: `Rate limit exceeded for ${eventType}`,
    failure_reason: 'Too many requests in short time period',
    ip_address: ip,
    user_agent: userAgent,
    additional_context: { original_event_type: eventType }
  })
}

/**
 * Get a human-readable failure reason based on error type and message
 */
function getFailureReason(errorType: string, errorMessage: string): string {
  switch (errorType) {
    case 'user_not_found':
      return 'No account exists with this email address'
    case 'user_already_exists':
      return 'Account already exists with this email address'
    case 'invalid_email':
      return 'Email address format is invalid'
    case 'rate_limit_exceeded':
      return 'Too many requests in short time period'
    case 'invalid_token':
      return 'Magic link token is invalid or malformed'
    case 'expired_token':
      return 'Magic link has expired (older than 30 minutes)'
    case 'used_token':
      return 'Magic link has already been used'
    case 'token_not_found':
      return 'Magic link token not found in database'
    case 'session_creation_failed':
      return 'Failed to create user session after authentication'
    case 'email_send_failed':
      return 'Failed to send magic link email'
    case 'database_error':
      return 'Database operation failed during authentication'
    default:
      return errorMessage || 'Unknown authentication error'
  }
}

/**
 * Get authentication failure statistics for admin dashboard
 */
export async function getAuthFailureStats(days: number = 7): Promise<{
  totalFailures: number
  failuresByType: Array<{ event_type: string; count: number }>
  failuresByReason: Array<{ failure_reason: string; count: number }>
  recentFailures: AuthLogEntry[]
}> {
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - days)

  const [totalResult, byTypeResult, byReasonResult, recentResult] = await Promise.all([
    // Total failures
    query(`
      SELECT COUNT(*) as total_failures
      FROM auth_logs
      WHERE status = 'failure' AND created_at >= $1
    `, [startDate.toISOString()]),

    // Failures by event type
    query(`
      SELECT event_type, COUNT(*) as count
      FROM auth_logs
      WHERE status = 'failure' AND created_at >= $1
      GROUP BY event_type
      ORDER BY count DESC
    `, [startDate.toISOString()]),

    // Failures by reason
    query(`
      SELECT failure_reason, COUNT(*) as count
      FROM auth_logs
      WHERE status = 'failure' AND created_at >= $1 AND failure_reason IS NOT NULL
      GROUP BY failure_reason
      ORDER BY count DESC
      LIMIT 10
    `, [startDate.toISOString()]),

    // Recent failures
    query(`
      SELECT *
      FROM auth_logs
      WHERE status = 'failure' AND created_at >= $1
      ORDER BY created_at DESC
      LIMIT 20
    `, [startDate.toISOString()])
  ])

  return {
    totalFailures: parseInt(totalResult.rows[0]?.total_failures || 0),
    failuresByType: byTypeResult.rows.map(row => ({
      event_type: row.event_type,
      count: parseInt(row.count)
    })),
    failuresByReason: byReasonResult.rows.map(row => ({
      failure_reason: row.failure_reason,
      count: parseInt(row.count)
    })),
    recentFailures: recentResult.rows
  }
}
