/**
 * Email Generation Functions
 * 
 * This file contains email generation functions that can be safely imported
 * in both client and server components. It doesn't import any server-side
 * dependencies like 'next/headers'.
 */

import { EmailOptions } from './email'
import { createUnifiedEmailTemplate, createEmailButton, createInfoBox, createHigh<PERSON>Box } from './email-templates'

// Create magic link email for sign-in
export function createSignInMagicLinkEmail(email: string, token: string): EmailOptions {
  const magicLinkUrl = `${process.env.APP_URL || 'http://localhost:3000'}/auth/magic-link#${token}`
  
  const content = `
    <p style="font-size: 16px; margin-bottom: 20px;">
      Click the button below to sign in to your BenefitLens account:
    </p>
    
    <div style="text-align: center; margin: 30px 0;">
      ${createEmailButton(magicLinkUrl, 'Sign In to BenefitLens')}
    </div>
    
    <p style="font-size: 16px; margin-bottom: 20px;">
      <strong>This link will expire in 30 minutes</strong> for your security.
    </p>
    
    <p style="font-size: 16px; margin-bottom: 20px;">
      If you didn't request this sign-in link, you can safely ignore this email.
    </p>
  `

  const html = createUnifiedEmailTemplate({
    title: 'Sign in to BenefitLens',
    subtitle: 'Access your account',
    content,
    recipientEmail: email,
    includeFooterLinks: true
  })

  const text = `
    Sign in to BenefitLens
    
    Hello,
    
    Click the link below to sign in to your BenefitLens account:
    
    ${magicLinkUrl}
    
    This link will expire in 30 minutes for your security.
    
    If you didn't request this sign-in link, you can safely ignore this email.
    
    If the button doesn't work, copy and paste this link into your browser:
    ${magicLinkUrl}
  `

  return {
    to: email,
    subject: 'Sign in to BenefitLens',
    html,
    text,
  }
}

// Create magic link email for sign-up
export function createSignUpMagicLinkEmail(email: string, firstName: string | undefined, token: string): EmailOptions {
  const magicLinkUrl = `${process.env.APP_URL || 'http://localhost:3000'}/auth/magic-link#${token}`
  const name = firstName || 'there'

  const content = `
    <p style="font-size: 16px; margin-bottom: 20px;">
      Hello ${name},
    </p>

    <p style="font-size: 16px; margin-bottom: 20px;">
      Welcome to BenefitLens! Click the button below to complete your account creation and start exploring company benefits:
    </p>

    <div style="text-align: center; margin: 30px 0;">
      ${createEmailButton(magicLinkUrl, 'Complete Account Setup')}
    </div>

    <div style="background: #e0f2fe; padding: 20px; border-radius: 8px; margin: 25px 0;">
      <h3 style="color: #0369a1; margin-top: 0; font-size: 18px;">What you can do with BenefitLens:</h3>
      <ul style="margin: 10px 0; padding-left: 20px; color: #333;">
        <li style="margin-bottom: 8px;">🏢 Discover companies and their benefits</li>
        <li style="margin-bottom: 8px;">✅ Verify benefits for your company</li>
        <li style="margin-bottom: 8px;">💾 Save companies you're interested in</li>
        <li style="margin-bottom: 8px;">📊 Compare benefits across companies</li>
      </ul>
    </div>

    <p style="font-size: 16px; margin-bottom: 20px;">
      <strong>This link will expire in 30 minutes</strong> for your security.
    </p>

    <p style="font-size: 16px; margin-bottom: 20px;">
      If you didn't create this account, you can safely ignore this email.
    </p>
  `

  const html = createUnifiedEmailTemplate({
    title: 'Welcome to BenefitLens! 🎉',
    subtitle: 'Complete your account setup',
    content,
    recipientEmail: email,
    includeFooterLinks: true
  })

  const text = `
    Welcome to BenefitLens!

    Hello ${name},

    Welcome to BenefitLens! Click the link below to complete your account creation:

    ${magicLinkUrl}

    What you can do with BenefitLens:
    - Discover companies and their benefits
    - Verify benefits for your company
    - Save companies you're interested in
    - Compare benefits across companies

    This link will expire in 30 minutes for your security.

    If you didn't create this account, you can safely ignore this email.
    
    If the button doesn't work, copy and paste this link into your browser:
    ${magicLinkUrl}
  `

  return {
    to: email,
    subject: 'Complete your BenefitLens account',
    html,
    text,
  }
}

// Create company discovery email
export function createCompanyDiscoveryEmail(
  userEmail: string,
  firstName: string,
  companyName: string,
  companyDomain: string,
  companyId: string
): EmailOptions {
  // Create the content using the unified email template system
  const companyUrl = `${process.env.APP_URL || 'http://localhost:3000'}/companies/${companyId}`
  
  const content = `
    <h2 style="color: #333; margin-top: 0;">Hello ${firstName}!</h2>

    <p style="font-size: 16px; margin-bottom: 20px;">
      Great news! Your company <strong>${companyName}</strong> is now available on BenefitLens!
    </p>

    <p style="font-size: 16px; margin-bottom: 25px;">
      We noticed you have an email address with the domain @${companyDomain}, which matches ${companyName} in our platform.
    </p>

    ${createInfoBox('What you can do now:', `
      <ul style="margin: 10px 0; padding-left: 20px; color: #333;">
        <li style="margin-bottom: 8px;">View and verify your company's benefits</li>
        <li style="margin-bottom: 8px;">Help improve benefit accuracy for your colleagues</li>
        <li style="margin-bottom: 8px;">Discover benefits you might not know about</li>
        <li style="margin-bottom: 8px;">Compare with other companies in your industry</li>
      </ul>
    `)}

    <div style="text-align: center; margin: 30px 0;">
      ${createEmailButton(companyUrl, `🏢 View ${companyName} Benefits`)}
    </div>

    ${createHighlightBox('💡 Pro Tip', `
      <p style="color: #333; margin: 0;">
        Sign in with your @${companyDomain} email to automatically associate with ${companyName}
        and access company-specific features!
      </p>
    `)}

    <p style="font-size: 14px; color: #333; margin-top: 25px;">
      This email was sent because we detected your email domain matches a company in our database.
      If you don't want to receive these notifications, you can unsubscribe in your account settings.
    </p>
  `

  const html = createUnifiedEmailTemplate({
    title: `🎉 ${companyName} is now available on BenefitLens!`,
    subtitle: 'Your Company is now in BenefitLens',
    content,
    recipientEmail: userEmail,
    includeFooterLinks: true
  })

  const text = `
    ${companyName} is now available on BenefitLens!

    Hello ${firstName}!

    Great news! Your company ${companyName} is now available on BenefitLens!

    We noticed you have an email address with the domain @${companyDomain}, which matches ${companyName} in our platform.

    What you can do now:
    - View and verify your company's benefits
    - Help improve benefit accuracy for your colleagues
    - Discover benefits you might not know about
    - Compare with other companies in your industry

    View your company benefits: ${companyUrl}

    Pro Tip: Sign in with your @${companyDomain} email to automatically associate with ${companyName} and access company-specific features!

    This email was sent because we detected your email domain matches a company in our database.
    If you don't want to receive these notifications, you can unsubscribe in your account settings.
  `

  return {
    to: userEmail,
    subject: `🎉 ${companyName} is now available on BenefitLens!`,
    html,
    text,
  }
}

// Create missing company report admin notification email
export function createMissingCompanyReportEmail(
  reportId: string,
  userEmail: string,
  emailDomain: string,
  firstName?: string,
  lastName?: string
): EmailOptions {
  const userName = firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName || 'User'

  const content = `
    <p style="font-size: 16px; margin-bottom: 20px;">
      A user has reported a missing company on BenefitLens and needs admin review.
    </p>

    ${createInfoBox('📋 Report Details', `
      <p><strong>Report ID:</strong> ${reportId}</p>
      <p><strong>User:</strong> ${userName}</p>
      <p><strong>Email:</strong> ${userEmail}</p>
      <p><strong>Company Domain:</strong> ${emailDomain}</p>
    `)}

    ${createInfoBox('🎯 Next Steps', `
      <p>Please review this company report and consider:</p>
      <ul style="margin: 10px 0; padding-left: 20px;">
        <li style="margin-bottom: 8px;">Adding the company to the database</li>
        <li style="margin-bottom: 8px;">Verifying the company domain</li>
        <li style="margin-bottom: 8px;">Setting up initial benefits data</li>
        <li style="margin-bottom: 8px;">Contacting the user if needed</li>
      </ul>
    `)}

    <p style="font-size: 14px; color: #666; margin-top: 25px; text-align: center;">
      BenefitLens Admin Notification System
    </p>
  `

  const html = createUnifiedEmailTemplate({
    title: `🏢 Missing Company Report: ${emailDomain}`,
    subtitle: 'Admin Action Required',
    content,
    recipientEmail: process.env.ADMIN_EMAIL || '<EMAIL>',
    includeFooterLinks: false
  })

  const text = `
    Missing Company Report: ${emailDomain}

    A user has reported a missing company on BenefitLens and needs admin review.

    Report Details:
    - Report ID: ${reportId}
    - User: ${userName}
    - Email: ${userEmail}
    - Company Domain: ${emailDomain}

    Next Steps:
    - Add the company to the database
    - Verify the company domain
    - Set up initial benefits data
    - Contact the user if needed

    BenefitLens Admin Notification System
  `

  return {
    to: process.env.ADMIN_EMAIL || '<EMAIL>',
    subject: `🏢 New Company Report: ${emailDomain}`,
    html,
    text,
  }
}
