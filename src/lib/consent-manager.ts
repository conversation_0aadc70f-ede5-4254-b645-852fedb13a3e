'use client'

import { useState, useEffect } from 'react'

export interface ConsentPreferences {
  necessary: boolean // Always true, cannot be disabled
  analytics: boolean
  functional: boolean
}

export const DEFAULT_CONSENT: ConsentPreferences = {
  necessary: true,
  analytics: false,
  functional: false
}

const CONSENT_STORAGE_KEY = 'benefitlens-consent-preferences'
const CONSENT_VERSION = '1.0'
const CONSENT_VERSION_KEY = 'benefitlens-consent-version'

export class ConsentManager {
  private static instance: ConsentManager
  private preferences: ConsentPreferences = DEFAULT_CONSENT
  private hasConsent: boolean = false
  private listeners: Array<(preferences: ConsentPreferences) => void> = []

  private constructor() {
    if (typeof window !== 'undefined') {
      this.loadPreferences()
    }
  }

  static getInstance(): ConsentManager {
    if (!ConsentManager.instance) {
      ConsentManager.instance = new ConsentManager()
    }
    return ConsentManager.instance
  }

  private loadPreferences(): void {
    try {
      const stored = localStorage.getItem(CONSENT_STORAGE_KEY)
      const version = localStorage.getItem(CONSENT_VERSION_KEY)
      
      if (stored && version === CONSENT_VERSION) {
        this.preferences = { ...DEFAULT_CONSENT, ...JSON.parse(stored) }
        this.hasConsent = true
      } else {
        // Reset if version mismatch
        this.resetConsent()
      }
    } catch (error) {
      console.error('Error loading consent preferences:', error)
      this.resetConsent()
    }
  }

  private savePreferences(): void {
    try {
      localStorage.setItem(CONSENT_STORAGE_KEY, JSON.stringify(this.preferences))
      localStorage.setItem(CONSENT_VERSION_KEY, CONSENT_VERSION)
      this.hasConsent = true
      this.notifyListeners()
    } catch (error) {
      console.error('Error saving consent preferences:', error)
    }
  }

  private resetConsent(): void {
    localStorage.removeItem(CONSENT_STORAGE_KEY)
    localStorage.removeItem(CONSENT_VERSION_KEY)
    this.preferences = { ...DEFAULT_CONSENT }
    this.hasConsent = false
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.preferences))
  }

  // Public API
  getPreferences(): ConsentPreferences {
    return { ...this.preferences }
  }

  hasUserConsent(): boolean {
    return this.hasConsent
  }

  updatePreferences(newPreferences: Partial<ConsentPreferences>): void {
    this.preferences = {
      ...this.preferences,
      ...newPreferences,
      necessary: true // Always true
    }
    this.savePreferences()
  }

  acceptAll(): void {
    this.updatePreferences({
      analytics: true,
      functional: true
    })
  }

  acceptNecessaryOnly(): void {
    this.updatePreferences({
      analytics: false,
      functional: false
    })
  }

  revokeConsent(): void {
    this.resetConsent()
    this.notifyListeners()
  }

  // Analytics helpers
  canTrackAnalytics(): boolean {
    return this.preferences.analytics
  }

  canUseFunctionalCookies(): boolean {
    return this.preferences.functional
  }

  // Event listeners
  onConsentChange(callback: (preferences: ConsentPreferences) => void): () => void {
    this.listeners.push(callback)
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  // Get consent status for specific purposes
  getConsentStatus() {
    return {
      hasConsent: this.hasConsent,
      preferences: this.getPreferences(),
      version: CONSENT_VERSION,
      timestamp: this.hasConsent ? new Date().toISOString() : null
    }
  }
}

// Export singleton instance
export const consentManager = ConsentManager.getInstance()

// React hook for using consent in components
export function useConsent() {
  const [preferences, setPreferences] = useState<ConsentPreferences>(consentManager.getPreferences())
  const [hasConsent, setHasConsent] = useState(consentManager.hasUserConsent())

  useEffect(() => {
    const unsubscribe = consentManager.onConsentChange((newPreferences) => {
      setPreferences(newPreferences)
      setHasConsent(consentManager.hasUserConsent())
    })

    return unsubscribe
  }, [])

  return {
    preferences,
    hasConsent,
    updatePreferences: consentManager.updatePreferences.bind(consentManager),
    acceptAll: consentManager.acceptAll.bind(consentManager),
    acceptNecessaryOnly: consentManager.acceptNecessaryOnly.bind(consentManager),
    revokeConsent: consentManager.revokeConsent.bind(consentManager),
    canTrackAnalytics: consentManager.canTrackAnalytics.bind(consentManager),
    canUseFunctionalCookies: consentManager.canUseFunctionalCookies.bind(consentManager)
  }
}


