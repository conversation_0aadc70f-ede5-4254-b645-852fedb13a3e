// PostgreSQL Cache System
// Complete replacement for Redis caching functionality

import { query } from './local-db'
import { logger } from './logger'
import {
  refreshCacheViews,
  cleanupExpiredSessions
} from './postgresql-session'
import { cleanupExpiredRateLimits } from './postgresql-rate-limit'

// Re-export session and rate limiting functions
export {
  setSession,
  getSession,
  deleteSession,
  deleteAllUserSessions,
  cleanupExpiredSessions,
  getSessionStats,
  scheduledSessionCleanup,
  refreshCacheViews
} from './postgresql-session'

// Re-export CSRF functions with different names to avoid conflicts
export { setCSRFToken, getCSRFToken } from './postgresql-session'

export {
  checkRateLimit,
  checkSlidingWindowRateLimit,
  checkAPIRateLimit,
  checkAuthRateLimit,
  checkSearchRateLimit,
  checkUploadRateLimit,
  cleanupExpiredRateLimits,
  getRateLimitStats,
  resetRateLimit,
  rateLimitConfigs
} from './postgresql-rate-limit'

// Cache management functions
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function setCache(key: string, value: any, ttlSeconds: number = 3600): Promise<boolean> {
  try {
    const result = await query('SELECT set_cache($1, $2, $3)', [key, JSON.stringify(value), ttlSeconds])
    return result.rows[0]?.set_cache || false
  } catch (error) {
    logger.error('Error setting cache in PostgreSQL', { error: error as Error, key })
    return false
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function getCache(key: string): Promise<any> {
  try {
    const result = await query('SELECT get_cache($1)', [key])
    const cachedData = result.rows[0]?.get_cache

    if (!cachedData) {
      return null
    }

    // PostgreSQL JSONB is already parsed by the pg library, no need to JSON.parse again
    return cachedData
  } catch (error) {
    logger.error('Error getting cache from PostgreSQL', { error: error as Error, key })
    return null
  }
}

export async function deleteCache(key: string): Promise<boolean> {
  try {
    const result = await query('SELECT delete_cache($1)', [key])
    return result.rows[0]?.delete_cache || false
  } catch (error) {
    logger.error('Error deleting cache from PostgreSQL', { error: error as Error, key })
    return false
  }
}

export async function clearCachePattern(pattern: string): Promise<number> {
  try {
    const result = await query('SELECT clear_cache_pattern($1)', [pattern])
    return result.rows[0]?.clear_cache_pattern || 0
  } catch (error) {
    logger.error('Error clearing cache pattern from PostgreSQL', { error: error as Error, pattern })
    return 0
  }
}

// Optimized company queries using materialized views
export async function getCachedCompaniesWithBenefits(filters?: {
  benefits?: string[]
  location?: string
  size?: string
  industry?: string
// eslint-disable-next-line @typescript-eslint/no-explicit-any
}): Promise<any[]> {
  try {
    let sql = 'SELECT * FROM companies_with_benefits_cache WHERE 1=1'
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params: any[] = []
    let paramCount = 0

    if (filters?.benefits && filters.benefits.length > 0) {
      paramCount++
      sql += ` AND EXISTS (
        SELECT 1 FROM json_array_elements(benefits) AS benefit
        WHERE benefit->>'name' = ANY($${paramCount})
      )`
      params.push(filters.benefits)
    }

    if (filters?.location) {
      paramCount++
      sql += ` AND EXISTS (
        SELECT 1 FROM json_array_elements(locations) AS location
        WHERE location->>'city' ILIKE $${paramCount} 
        OR location->>'state' ILIKE $${paramCount}
        OR location->>'country' ILIKE $${paramCount}
      )`
      params.push(`%${filters.location}%`)
    }

    if (filters?.size) {
      paramCount++
      sql += ` AND size = $${paramCount}`
      params.push(filters.size)
    }

    if (filters?.industry) {
      paramCount++
      sql += ` AND industry ILIKE $${paramCount}`
      params.push(`%${filters.industry}%`)
    }

    sql += ' ORDER BY name'

    const result = await query(sql, params)
    return result.rows
  } catch (error) {
    logger.error('Error getting cached companies with benefits', { error: error as Error, filters })
    return []
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function getCachedBenefitsWithCategories(categoryId?: string): Promise<any[]> {
  try {
    let sql = 'SELECT * FROM benefits_with_categories_cache'
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params: any[] = []

    if (categoryId) {
      sql += ' WHERE category_id = $1'
      params.push(categoryId)
    }

    sql += ' ORDER BY name'

    const result = await query(sql, params)
    return result.rows
  } catch (error) {
    logger.error('Error getting cached benefits with categories', { error: error as Error, categoryId })
    return []
  }
}

// Cache invalidation helpers
export async function invalidateCompanyCache(companyId?: string): Promise<void> {
  try {
    logger.info('Starting aggressive cache invalidation', { companyId })

    // Clear all possible cache patterns for this company
    if (companyId) {
      const patterns = [
        `company:${companyId}`,
        `company:${companyId}:*`,
        `company:${companyId}*`
      ]

      for (const pattern of patterns) {
        const deletedCount = await clearCachePattern(pattern)
        logger.info('Cleared cache pattern', { pattern, deletedCount })
      }
    }

    // Clear all companies-related cache
    const companiesPatternsToDelete = [
      'companies:*',
      'companies_*',
      'company_*'
    ]

    for (const pattern of companiesPatternsToDelete) {
      const deletedCount = await clearCachePattern(pattern)
      logger.info('Cleared companies cache pattern', { pattern, deletedCount })
    }

    // Force refresh materialized views synchronously
    try {
      await refreshCacheViews()
      logger.info('Cache views refreshed successfully')
    } catch (error) {
      logger.warn('Cache view refresh failed', { error: error as Error })
    }

    // Additional cleanup - clear any expired cache entries
    try {
      const expiredCount = await query('SELECT cleanup_expired_cache()').then(r => r.rows[0]?.cleanup_expired_cache || 0)
      logger.info('Cleaned up expired cache entries', { expiredCount })
    } catch (error) {
      logger.warn('Failed to cleanup expired cache', { error: error as Error })
    }

    logger.info('Aggressive cache invalidation completed', { companyId })
  } catch (error) {
    logger.error('Error invalidating company cache', { error: error as Error, companyId })
  }
}

export async function invalidateBenefitCache(): Promise<void> {
  try {
    await clearCachePattern('benefits:*')

    // Refresh materialized view in background
    setTimeout(async () => {
      try {
        await refreshCacheViews()
      } catch (error) {
        logger.warn('Background cache refresh failed', { error: error as Error })
      }
    }, 1000)
  } catch (error) {
    logger.error('Error invalidating benefit cache', { error: error as Error })
  }
}

// Force clear all cache - nuclear option
export async function clearAllCache(): Promise<void> {
  try {
    logger.info('Starting nuclear cache clear - clearing ALL cache')

    // Clear all cache entries
    const totalDeleted = await query('DELETE FROM cache_store').then(r => r.rowCount || 0)
    logger.info('Cleared all cache entries', { totalDeleted })

    // Refresh all materialized views
    await refreshCacheViews()
    logger.info('All cache cleared and views refreshed')

  } catch (error) {
    logger.error('Error clearing all cache', { error: error as Error })
  }
}

// Health check function
export async function checkCacheHealth(): Promise<boolean> {
  try {
    // Test basic cache operations
    const testKey = 'health_check_test'
    const testValue = { timestamp: Date.now() }
    
    const setResult = await setCache(testKey, testValue, 60)
    if (!setResult) {return false}
    
    const getValue = await getCache(testKey)
    if (!getValue || getValue.timestamp !== testValue.timestamp) {return false}
    
    const deleteResult = await deleteCache(testKey)
    if (!deleteResult) {return false}
    
    // Test database connectivity
    const result = await query('SELECT 1 as test')
    return result.rows.length > 0
  } catch (error) {
    logger.error('Cache health check failed', { error: error as Error })
    return false
  }
}

// Cache statistics
export async function getCacheStats(): Promise<{
  totalCacheEntries: number
  expiredEntries: number
  cacheHitRate?: number
  oldestEntry?: string
  newestEntry?: string
}> {
  try {
    const result = await query(`
      SELECT 
        COUNT(*) as total_entries,
        COUNT(*) FILTER (WHERE expires_at < NOW()) as expired_entries,
        MIN(created_at) as oldest_entry,
        MAX(created_at) as newest_entry
      FROM cache_store
    `)
    
    const stats = result.rows[0]
    return {
      totalCacheEntries: parseInt(stats.total_entries) || 0,
      expiredEntries: parseInt(stats.expired_entries) || 0,
      oldestEntry: stats.oldest_entry?.toISOString(),
      newestEntry: stats.newest_entry?.toISOString(),
    }
  } catch (error) {
    logger.error('Error getting cache stats', { error: error as Error })
    return {
      totalCacheEntries: 0,
      expiredEntries: 0,
    }
  }
}

// Scheduled maintenance function
export async function scheduledCacheMaintenance(): Promise<void> {
  try {
    // Clean up expired cache entries
    const expiredCacheCount = await query('SELECT cleanup_expired_cache()').then(r => r.rows[0]?.cleanup_expired_cache || 0)
    
    // Clean up expired rate limits
    const expiredRateLimitCount = await cleanupExpiredRateLimits()
    
    // Clean up expired sessions
    const expiredSessionCount = await cleanupExpiredSessions()
    
    // Refresh materialized views
    await refreshCacheViews()
    
    logger.info('Scheduled cache maintenance completed', {
      expiredCacheCount,
      expiredRateLimitCount,
      expiredSessionCount
    })
  } catch (error) {
    logger.error('Error in scheduled cache maintenance', { error: error as Error })
  }
}

// Utility function to warm up cache
export async function warmUpCache(): Promise<void> {
  try {
    logger.info('Starting cache warm-up')
    
    // Pre-load frequently accessed data
    await getCachedCompaniesWithBenefits()
    await getCachedBenefitsWithCategories()
    
    logger.info('Cache warm-up completed')
  } catch (error) {
    logger.error('Error warming up cache', { error: error as Error })
  }
}
