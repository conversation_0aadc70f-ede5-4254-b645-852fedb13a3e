import { v4 as uuidv4 } from 'uuid'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { query } from './local-db'
import { setSession, getSession as _getSession, deleteSession as deleteSessionStorage } from './session-storage'

export interface LocalUser {
  id: string
  email: string
  firstName: string | null
  lastName: string | null
  role: string
  paymentStatus: 'free' | 'paying'
  emailVerified: boolean
  createdAt: string
  companyId?: string | null
  // Impersonation fields
  isImpersonation?: boolean
  originalAdminId?: string | null
  originalAdminEmail?: string | null
}

// Get user by email (for magic link auth)
export async function getUserByEmail(email: string): Promise<LocalUser | null> {
  const result = await query(
    'SELECT * FROM users WHERE email = $1',
    [email.toLowerCase()]
  )

  if (result.rows.length === 0) {
    return null
  }

  const user = result.rows[0]

  return {
    id: user.id,
    email: user.email,
    firstName: user.first_name,
    lastName: user.last_name,
    role: user.role,
    paymentStatus: user.payment_status || 'free',
    emailVerified: user.email_verified,
    createdAt: user.created_at,
    companyId: user.company_id,
  }
}

// Create session
export async function createSession(userId: string): Promise<string> {
  const sessionToken = uuidv4()
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

  // Store session in PostgreSQL
  await setSession(sessionToken, userId, expiresAt)

  // Set cookie
  const cookieStore = await cookies()
  cookieStore.set('session_token', sessionToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    expires: expiresAt,
  })

  return sessionToken
}

// Get current user from session
export async function getCurrentUser(): Promise<LocalUser | null> {
  let sessionToken: string | undefined

  // In test environment, also check for session token in headers
  if (process.env.NODE_ENV === 'test' || process.env.USE_LOCAL_AUTH === 'true') {
    try {
      const { headers } = await import('next/headers')
      const headersList = await headers()
      const cookieHeader = headersList.get('cookie')

      if (cookieHeader) {
        const sessionMatch = cookieHeader.match(/session_token=([^;]+)/)
        if (sessionMatch) {
          sessionToken = sessionMatch[1]
        }
      }
    } catch (error) {
      // Headers not available, fall back to cookies
    }
  }

  // Fall back to normal cookie reading if no session token found in headers
  if (!sessionToken) {
    const cookieStore = await cookies()
    sessionToken = cookieStore.get('session_token')?.value
  }

  if (!sessionToken) {return null}

  // Get session from PostgreSQL with impersonation info
  const result = await query(
    `SELECT
      s.user_id,
      s.expires_at,
      s.is_impersonation,
      s.original_admin_id,
      u.id,
      u.email,
      u.first_name,
      u.last_name,
      u.role,
      u.payment_status,
      u.email_verified,
      u.created_at,
      u.company_id,
      admin_u.email as original_admin_email
     FROM user_sessions s
     JOIN users u ON s.user_id = u.id
     LEFT JOIN users admin_u ON s.original_admin_id = admin_u.id
     WHERE s.session_token = $1`,
    [sessionToken]
  )

  if (result.rows.length === 0) {return null}

  const session = result.rows[0]

  // Check if session is expired
  if (new Date(session.expires_at) < new Date()) {
    await deleteSessionStorage(sessionToken)
    return null
  }

  return {
    id: session.id,
    email: session.email,
    firstName: session.first_name,
    lastName: session.last_name,
    role: session.role,
    paymentStatus: session.payment_status || 'free',
    emailVerified: session.email_verified,
    createdAt: session.created_at,
    companyId: session.company_id,
    // Include impersonation information
    isImpersonation: session.is_impersonation || false,
    originalAdminId: session.original_admin_id || null,
    originalAdminEmail: session.original_admin_email || null,
  }
}

// Delete session (sign out)
export async function deleteSession(sessionToken?: string): Promise<void> {
  const cookieStore = await cookies()
  const token = sessionToken || cookieStore.get('session_token')?.value

  if (token) {
    // Delete from session storage (PostgreSQL)
    await deleteSessionStorage(token)
  }

  // Clear cookie
  cookieStore.delete('session_token')
}

// Require authentication
export async function requireAuth(): Promise<LocalUser> {
  const user = await getCurrentUser()
  if (!user) {
    redirect('/sign-in')
  }
  return user
}

// Check if user is signed in
export async function isSignedIn(): Promise<boolean> {
  const user = await getCurrentUser()
  return !!user
}
