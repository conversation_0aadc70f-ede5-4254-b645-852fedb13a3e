'use client'

import { consentManager } from './consent-manager'

/**
 * Client-side analytics wrapper that respects user consent
 */
export class ClientAnalytics {
  private static instance: ClientAnalytics
  
  private constructor() {}
  
  static getInstance(): ClientAnalytics {
    if (!ClientAnalytics.instance) {
      ClientAnalytics.instance = new ClientAnalytics()
    }
    return ClientAnalytics.instance
  }

  /**
   * Track a company page view (client-side)
   */
  async trackCompanyView(companyId: string, referrer?: string): Promise<void> {
    if (!consentManager.canTrackAnalytics()) {
      console.log('Analytics tracking disabled by user consent')
      return
    }

    try {
      await fetch('/api/analytics/track-company-view', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyId,
          referrer
        })
      })
    } catch (error) {
      console.error('Error tracking company view:', error)
    }
  }

  /**
   * Track a search query (client-side)
   */
  async trackSearch(
    queryText: string, 
    resultsCount: number, 
    filtersApplied?: Record<string, unknown>
  ): Promise<string | null> {
    if (!consentManager.canTrackAnalytics()) {
      console.log('Analytics tracking disabled by user consent')
      return null
    }

    try {
      const response = await fetch('/api/analytics/track-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          queryText,
          resultsCount,
          filtersApplied
        })
      })

      if (response.ok) {
        const data = await response.json()
        return data.searchQueryId || null
      }
    } catch (error) {
      console.error('Error tracking search:', error)
    }
    
    return null
  }

  /**
   * Track a benefit interaction (client-side)
   */
  async trackBenefitInteraction(
    benefitId: string,
    companyId: string,
    interactionType: 'view' | 'click' | 'verify' | 'dispute',
    searchQueryId?: string
  ): Promise<void> {
    if (!consentManager.canTrackAnalytics()) {
      console.log('Analytics tracking disabled by user consent')
      return
    }

    try {
      await fetch('/api/analytics/track-benefit-interaction', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          benefitId,
          companyId,
          interactionType,
          searchQueryId
        })
      })
    } catch (error) {
      console.error('Error tracking benefit interaction:', error)
    }
  }

  /**
   * Track page view with consent check
   */
  async trackPageView(path: string, title?: string): Promise<void> {
    if (!consentManager.canTrackAnalytics()) {
      console.log('Analytics tracking disabled by user consent')
      return
    }

    try {
      await fetch('/api/analytics/track-page-view', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          path,
          title,
          timestamp: new Date().toISOString()
        })
      })
    } catch (error) {
      console.error('Error tracking page view:', error)
    }
  }

  /**
   * Track custom event with consent check
   */
  async trackEvent(
    eventName: string, 
    properties?: Record<string, unknown>
  ): Promise<void> {
    if (!consentManager.canTrackAnalytics()) {
      console.log('Analytics tracking disabled by user consent')
      return
    }

    try {
      await fetch('/api/analytics/track-event', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventName,
          properties,
          timestamp: new Date().toISOString()
        })
      })
    } catch (error) {
      console.error('Error tracking custom event:', error)
    }
  }

  /**
   * Get consent status for analytics
   */
  getConsentStatus() {
    return {
      canTrack: consentManager.canTrackAnalytics(),
      preferences: consentManager.getPreferences(),
      hasConsent: consentManager.hasUserConsent()
    }
  }
}

// Export singleton instance
export const clientAnalytics = ClientAnalytics.getInstance()

// React hook for analytics with consent
export function useAnalytics() {
  const canTrack = consentManager.canTrackAnalytics()
  
  return {
    trackCompanyView: canTrack ? clientAnalytics.trackCompanyView.bind(clientAnalytics) : () => {},
    trackSearch: canTrack ? clientAnalytics.trackSearch.bind(clientAnalytics) : () => Promise.resolve(null),
    trackBenefitInteraction: canTrack ? clientAnalytics.trackBenefitInteraction.bind(clientAnalytics) : () => {},
    trackPageView: canTrack ? clientAnalytics.trackPageView.bind(clientAnalytics) : () => {},
    trackEvent: canTrack ? clientAnalytics.trackEvent.bind(clientAnalytics) : () => {},
    canTrack,
    getConsentStatus: clientAnalytics.getConsentStatus.bind(clientAnalytics)
  }
}

// Utility function to initialize analytics with consent checking
export function initializeAnalytics() {
  // Listen for consent changes and update analytics accordingly
  consentManager.onConsentChange((preferences) => {
    console.log('Analytics consent updated:', preferences.analytics)
    
    if (!preferences.analytics) {
      // Clear any existing analytics data if consent is revoked
      console.log('Analytics consent revoked - stopping tracking')
    }
  })
}

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
  initializeAnalytics()
}
