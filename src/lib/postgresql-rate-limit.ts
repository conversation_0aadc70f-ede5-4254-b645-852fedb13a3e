// PostgreSQL-based Rate Limiting
// Replaces Redis rate limiting with PostgreSQL implementation

import { query } from './local-db'
import { logger } from './logger'

export interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (identifier: string) => string // Custom key generator
}

export interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  total: number
}

// Default configurations for different endpoints
export const rateLimitConfigs = {
  api: { windowMs: 60000, maxRequests: 100 }, // 100 requests per minute
  auth: { windowMs: 300000, maxRequests: 5 }, // 5 requests per 5 minutes
  search: { windowMs: 60000, maxRequests: 50 }, // 50 searches per minute
  upload: { windowMs: 3600000, maxRequests: 10 }, // 10 uploads per hour
}

function _generateKey(identifier: string, config: RateLimitConfig): string {
  if (config.keyGenerator) {
    return config.keyGenerator(identifier)
  }
  return `rate_limit:${identifier}:${config.windowMs}`
}

export async function checkRateLimit(
  identifier: string,
  config: RateLimitConfig
): Promise<RateLimitResult> {
  // Check if rate limiting is disabled (for testing)
  const rateLimitingDisabled = process.env.DISABLE_RATE_LIMITING === 'true'

  if (rateLimitingDisabled) {
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime: Date.now() + config.windowMs,
      total: config.maxRequests,
    }
  }

  const now = Date.now()
  const windowStart = Math.floor(now / config.windowMs) * config.windowMs
  const windowEnd = windowStart + config.windowMs

  try {
    // Get or create rate limit record
    const result = await query(`
      INSERT INTO rate_limits (identifier, window_start, request_count, request_timestamps, expires_at)
      VALUES ($1, $2, 1, $3, $4)
      ON CONFLICT (identifier, window_start)
      DO UPDATE SET 
        request_count = rate_limits.request_count + 1,
        request_timestamps = rate_limits.request_timestamps || $3,
        updated_at = NOW()
      RETURNING request_count, request_timestamps
    `, [
      identifier,
      new Date(windowStart),
      JSON.stringify([now]),
      new Date(windowEnd)
    ])

    const record = result.rows[0]
    const currentCount = record.request_count
    const timestamps: number[] = JSON.parse(record.request_timestamps || '[]')

    // Filter timestamps to current window (sliding window approach)
    const validTimestamps = timestamps.filter(ts => ts >= windowStart && ts < windowEnd)
    const actualCount = validTimestamps.length

    const allowed = actualCount <= config.maxRequests
    const remaining = Math.max(0, config.maxRequests - actualCount)

    // If we're over the limit, update the database to reflect the correct count
    if (actualCount !== currentCount) {
      await query(`
        UPDATE rate_limits 
        SET request_count = $1, request_timestamps = $2
        WHERE identifier = $3 AND window_start = $4
      `, [
        actualCount,
        JSON.stringify(validTimestamps),
        identifier,
        new Date(windowStart)
      ])
    }

    return {
      allowed,
      remaining,
      resetTime: windowEnd,
      total: config.maxRequests,
    }
  } catch (error) {
    logger.error('Error checking rate limit', { error: error as Error, identifier })
    // On error, allow the request (fail open)
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime: windowEnd,
      total: config.maxRequests,
    }
  }
}

// Sliding window rate limiter (more accurate but more expensive)
export async function checkSlidingWindowRateLimit(
  identifier: string,
  config: RateLimitConfig
): Promise<RateLimitResult> {
  // Check if rate limiting is disabled (for testing)
  const rateLimitingDisabled = process.env.DISABLE_RATE_LIMITING === 'true'

  if (rateLimitingDisabled) {
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime: Date.now() + config.windowMs,
      total: config.maxRequests,
    }
  }

  const now = Date.now()
  const windowStart = now - config.windowMs

  try {
    // Get current requests in the sliding window
    const result = await query(`
      SELECT request_timestamps
      FROM rate_limits
      WHERE identifier = $1 
      AND expires_at > NOW()
      ORDER BY window_start DESC
      LIMIT 10
    `, [identifier])

    // Collect all timestamps from recent windows
    let allTimestamps: number[] = []
    for (const row of result.rows) {
      const timestamps: number[] = JSON.parse(row.request_timestamps || '[]')
      allTimestamps = allTimestamps.concat(timestamps)
    }

    // Filter to sliding window
    const validTimestamps = allTimestamps.filter(ts => ts > windowStart)
    const currentCount = validTimestamps.length

    const allowed = currentCount < config.maxRequests
    
    if (allowed) {
      // Add current request
      validTimestamps.push(now)
      
      // Store in current window bucket
      const currentWindowStart = Math.floor(now / config.windowMs) * config.windowMs
      await query(`
        INSERT INTO rate_limits (identifier, window_start, request_count, request_timestamps, expires_at)
        VALUES ($1, $2, 1, $3, $4)
        ON CONFLICT (identifier, window_start)
        DO UPDATE SET 
          request_count = rate_limits.request_count + 1,
          request_timestamps = rate_limits.request_timestamps || $3,
          updated_at = NOW()
      `, [
        identifier,
        new Date(currentWindowStart),
        JSON.stringify([now]),
        new Date(currentWindowStart + config.windowMs)
      ])
    }

    return {
      allowed,
      remaining: Math.max(0, config.maxRequests - validTimestamps.length),
      resetTime: Math.min(...validTimestamps) + config.windowMs,
      total: config.maxRequests,
    }
  } catch (error) {
    logger.error('Error checking sliding window rate limit', { error: error as Error, identifier })
    // On error, allow the request (fail open)
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime: now + config.windowMs,
      total: config.maxRequests,
    }
  }
}

// Cleanup expired rate limit records
export async function cleanupExpiredRateLimits(): Promise<number> {
  try {
    const result = await query('DELETE FROM rate_limits WHERE expires_at < NOW()')
    const deletedCount = result.rowCount || 0
    
    if (deletedCount > 0) {
      logger.info('Cleaned up expired rate limit records', { deletedCount })
    }
    
    return deletedCount
  } catch (error) {
    logger.error('Error cleaning up expired rate limits', { error: error as Error })
    return 0
  }
}

// Get rate limit stats for monitoring
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function getRateLimitStats(identifier?: string): Promise<any> {
  try {
    let sql = `
      SELECT 
        identifier,
        COUNT(*) as total_windows,
        SUM(request_count) as total_requests,
        MAX(updated_at) as last_request,
        AVG(request_count) as avg_requests_per_window
      FROM rate_limits
      WHERE expires_at > NOW()
    `
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params: any[] = []
    
    if (identifier) {
      sql += ' AND identifier = $1'
      params.push(identifier)
    }
    
    sql += ' GROUP BY identifier ORDER BY total_requests DESC'
    
    const result = await query(sql, params)
    return result.rows
  } catch (error) {
    logger.error('Error getting rate limit stats', { error: error as Error })
    return []
  }
}

// Reset rate limit for a specific identifier (admin function)
export async function resetRateLimit(identifier: string): Promise<boolean> {
  try {
    const result = await query('DELETE FROM rate_limits WHERE identifier = $1', [identifier])
    const deletedCount = result.rowCount || 0
    
    logger.info('Rate limit reset', { identifier, deletedCount })
    return deletedCount > 0
  } catch (error) {
    logger.error('Error resetting rate limit', { error: error as Error, identifier })
    return false
  }
}

// Convenience functions for common rate limiting scenarios
export async function checkAPIRateLimit(identifier: string): Promise<RateLimitResult> {
  return checkRateLimit(identifier, rateLimitConfigs.api)
}

export async function checkAuthRateLimit(identifier: string): Promise<RateLimitResult> {
  return checkRateLimit(identifier, rateLimitConfigs.auth)
}

export async function checkSearchRateLimit(identifier: string): Promise<RateLimitResult> {
  return checkRateLimit(identifier, rateLimitConfigs.search)
}

export async function checkUploadRateLimit(identifier: string): Promise<RateLimitResult> {
  return checkRateLimit(identifier, rateLimitConfigs.upload)
}
