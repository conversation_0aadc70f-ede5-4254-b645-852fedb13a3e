import { query } from '@/lib/local-db'
import { sendEmail, EmailOptions } from '@/lib/email'
import { createCompanyDiscoveryEmail as generateCompanyDiscoveryEmail } from '@/lib/email-generators'

// Create email notification function - moved to top to avoid TDZ
export const createCompanyDiscoveryEmail = (
  userEmail: string,
  firstName: string,
  companyName: string,
  companyDomain: string,
  companyId: string
): EmailOptions => {
  return generateCompanyDiscoveryEmail(userEmail, firstName, companyName, companyDomain, companyId)
}

export interface UserDiscoveryResult {
  companyId: string
  companyName: string
  domain: string
  discoveredUsers: string[]
  notificationsSent: number
  errors: string[]
}

/**
 * Discovers existing users with email domains matching a company
 * and sends them notification emails about the new company
 */
export async function discoverAndNotifyUsers(companyId: string): Promise<UserDiscoveryResult> {
  const result: UserDiscoveryResult = {
    companyId,
    companyName: '',
    domain: '',
    discoveredUsers: [],
    notificationsSent: 0,
    errors: []
  }



  try {
    // Get company details
    const companyResult = await query(
      'SELECT id, name, domain FROM companies WHERE id = $1',
      [companyId]
    )

    if (companyResult.rows.length === 0) {
      throw new Error('Company not found')
    }

    const company = companyResult.rows[0]
    result.companyName = company.name
    result.domain = company.domain

    if (!company.domain) {
      result.errors.push('Company has no domain configured')
      return result
    }

    // Find existing users with matching email domain who are not already associated with this company
    const usersResult = await query(
      `SELECT email, first_name, last_name
       FROM users
       WHERE email LIKE $1
       AND (company_id IS NULL OR company_id != $2)`,
      [`%@${company.domain}`, companyId]
    )

    result.discoveredUsers = usersResult.rows.map(user => user.email)

    if (result.discoveredUsers.length === 0) {
      return result
    }

    // Associate users with company and send notification emails
    for (const user of usersResult.rows) {
      try {
        // Associate user with company via users.company_id
        await query(
          'UPDATE users SET company_id = $1 WHERE email = $2',
          [companyId, user.email.toLowerCase()]
        )

        // Send notification email
        const emailOptions = createCompanyDiscoveryEmail(
          user.email,
          user.first_name || 'User',
          company.name,
          company.domain,
          companyId
        )

        await sendEmail(emailOptions)
        result.notificationsSent++
      } catch (error) {
        result.errors.push(`Failed to process user ${user.email}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return result

  } catch (error) {
    result.errors.push(error instanceof Error ? error.message : 'Unknown error')
    return result
  }
}

/**
 * Automatically match a newly registered user to an existing company based on email domain
 * This is called during user registration to provide automatic company association
 */
export async function autoMatchUserToCompany(userEmail: string, firstName: string): Promise<{
  matched: boolean
  companyId?: string
  companyName?: string
  error?: string
}> {
  try {
    const emailDomain = userEmail.split('@')[1]?.toLowerCase()
    if (!emailDomain) {
      return { matched: false, error: 'Invalid email format' }
    }

    // Find company with matching domain
    const companyResult = await query(
      'SELECT id, name, domain FROM companies WHERE LOWER(domain) = $1',
      [emailDomain]
    )

    if (companyResult.rows.length === 0) {
      return { matched: false }
    }

    const company = companyResult.rows[0]

    // Update user's company_id
    await query(
      'UPDATE users SET company_id = $1 WHERE email = $2',
      [company.id, userEmail.toLowerCase()]
    )

    // Send welcome email about company match
    try {
      const emailOptions = createCompanyDiscoveryEmail(
        userEmail,
        firstName,
        company.name,
        company.domain,
        company.id
      )

      await sendEmail(emailOptions)
    } catch (emailError) {
      console.error('Failed to send company match email:', emailError)
      // Don't fail the matching if email fails
    }

    return {
      matched: true,
      companyId: company.id,
      companyName: company.name
    }

  } catch (error) {
    console.error('Error in auto-matching user to company:', error)
    return {
      matched: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}