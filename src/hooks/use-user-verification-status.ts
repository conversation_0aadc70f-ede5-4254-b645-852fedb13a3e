import { useState, useEffect } from 'react'

interface UserVerificationStatus {
  hasVerified: boolean
  isAdminVerified?: boolean
  verification: {
    id: string
    status: string
    createdAt: string
  } | null
  requiresAuth?: boolean
  message: string
}

interface UseUserVerificationStatusReturn {
  hasVerified: boolean
  isAdminVerified: boolean
  verification: UserVerificationStatus['verification']
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

// Global cache to avoid duplicate requests
const verificationStatusCache = new Map<string, UserVerificationStatus>()

export function useUserVerificationStatus(companyBenefitId: string): UseUserVerificationStatusReturn {
  const [status, setStatus] = useState<UserVerificationStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchVerificationStatus = async () => {
    if (!companyBenefitId || companyBenefitId.trim() === '') {
      setIsLoading(false)
      setStatus({
        hasVerified: false,
        isAdminVerified: false,
        verification: null,
        message: 'No benefit ID provided'
      })
      return
    }

    // Check cache first
    const cached = verificationStatusCache.get(companyBenefitId)
    if (cached) {
      setStatus(cached)
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/benefit-verifications/user/${companyBenefitId}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: UserVerificationStatus = await response.json()
      
      // Cache the result
      verificationStatusCache.set(companyBenefitId, data)
      setStatus(data)
    } catch (err) {
      console.error('Error fetching user verification status:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }

  const refetch = async () => {
    // Clear cache for this benefit and refetch
    verificationStatusCache.delete(companyBenefitId)
    await fetchVerificationStatus()
  }

  useEffect(() => {
    fetchVerificationStatus()
  }, [companyBenefitId])

  return {
    hasVerified: status?.hasVerified || false,
    isAdminVerified: status?.isAdminVerified || false,
    verification: status?.verification || null,
    isLoading,
    error,
    refetch
  }
}

// Utility function to clear cache when user verifies a benefit
export function clearUserVerificationCache(companyBenefitId: string) {
  verificationStatusCache.delete(companyBenefitId)
}

// Utility function to update cache when user verifies a benefit
export function updateUserVerificationCache(companyBenefitId: string, hasVerified: boolean, verification: UserVerificationStatus['verification'], isAdminVerified = false) {
  verificationStatusCache.set(companyBenefitId, {
    hasVerified,
    isAdminVerified,
    verification,
    message: hasVerified ? 'User has verified this benefit' : 'User has not verified this benefit'
  })
}
