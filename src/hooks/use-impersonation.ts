'use client'

import { useState, useEffect } from 'react'

interface ImpersonationState {
  isImpersonating: boolean
  impersonatedUserEmail: string | null
  originalAdminEmail: string | null
}

export function useImpersonation() {
  const [impersonationState, setImpersonationState] = useState<ImpersonationState>({
    isImpersonating: false,
    impersonatedUserEmail: null,
    originalAdminEmail: null
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function checkImpersonationStatus() {
      try {
        const response = await fetch('/api/auth/me')
        if (response.ok) {
          const data = await response.json()
          const user = data.user
          
          if (user && user.isImpersonation) {
            setImpersonationState({
              isImpersonating: true,
              impersonatedUserEmail: user.email,
              originalAdminEmail: user.originalAdminEmail
            })
          } else {
            setImpersonationState({
              isImpersonating: false,
              impersonatedUserEmail: null,
              originalAdminEmail: null
            })
          }
        }
      } catch (error) {
        console.error('Error checking impersonation status:', error)
        setImpersonationState({
          isImpersonating: false,
          impersonatedUserEmail: null,
          originalAdminEmail: null
        })
      } finally {
        setLoading(false)
      }
    }

    checkImpersonationStatus()
  }, [])

  const exitImpersonation = async () => {
    try {
      const response = await fetch('/api/admin/exit-impersonation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        // Redirect to admin dashboard
        window.location.href = '/admin'
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to exit impersonation')
      }
    } catch (error) {
      console.error('Error exiting impersonation:', error)
      throw error
    }
  }

  return {
    ...impersonationState,
    loading,
    exitImpersonation
  }
}
