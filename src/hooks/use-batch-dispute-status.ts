import { useState, useEffect, useMemo, useCallback } from 'react'

interface DisputeStats {
  pending: number
  approved: number
  rejected: number
  total: number
}

interface UserDispute {
  id: string
  reason: string
  status: 'pending' | 'approved' | 'rejected' | 'cancelled'
  created_at: string
  admin_comment?: string
}

interface DisputeStatus {
  canDispute: boolean
  dispute: UserDispute | null
  stats: DisputeStats
}

interface BatchDisputeStatusHook {
  getDisputeStatus: (companyBenefitId: string) => DisputeStatus | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

// Global cache to store dispute status and avoid duplicate requests
const disputeStatusCache = new Map<string, DisputeStatus>()

export function useBatchDisputeStatus(companyBenefitIds: string[]): BatchDisputeStatusHook {
  const [disputeStatuses, setDisputeStatuses] = useState<Record<string, DisputeStatus>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Create a stable string representation of the IDs
  const idsKey = useMemo(() => companyBenefitIds.sort().join(','), [companyBenefitIds])

  const fetchData = useCallback(async () => {
    if (companyBenefitIds.length === 0) {
      setIsLoading(false)
      setDisputeStatuses({})
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/benefit-removal-disputes/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyBenefitIds: companyBenefitIds
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Batch dispute status error:', response.status, errorText)
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`)
      }

      const data: Record<string, DisputeStatus> = await response.json()

      // Cache the results
      Object.entries(data).forEach(([id, status]) => {
        disputeStatusCache.set(id, status)
      })

      setDisputeStatuses(data)
    } catch (err) {
      console.error('Error fetching batch dispute status:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')

      // Fallback: create empty results for all requested IDs
      const fallbackData: Record<string, DisputeStatus> = {}
      companyBenefitIds.forEach(id => {
        fallbackData[id] = {
          canDispute: false,
          dispute: null,
          stats: { pending: 0, approved: 0, rejected: 0, total: 0 }
        }
      })
      setDisputeStatuses(fallbackData)
    } finally {
      setIsLoading(false)
    }
  }, [companyBenefitIds])

  useEffect(() => {
    if (companyBenefitIds.length === 0) {
      setIsLoading(false)
      return
    }

    // Check if all data is already cached
    const allCached = companyBenefitIds.every(id => disputeStatusCache.has(id))
    if (allCached) {
      const cachedData: Record<string, DisputeStatus> = {}
      companyBenefitIds.forEach(id => {
        const cached = disputeStatusCache.get(id)
        if (cached) {
          cachedData[id] = cached
        }
      })
      setDisputeStatuses(cachedData)
      setIsLoading(false)
      return
    }

    fetchData()
  }, [idsKey, fetchData])

  const getDisputeStatus = (companyBenefitId: string): DisputeStatus | null => {
    return disputeStatuses[companyBenefitId] || disputeStatusCache.get(companyBenefitId) || null
  }

  const refetch = async () => {
    // Clear cache for these benefits and refetch
    companyBenefitIds.forEach(id => disputeStatusCache.delete(id))
    await fetchData()
  }

  return {
    getDisputeStatus,
    isLoading,
    error,
    refetch
  }
}

// Utility function to clear cache when dispute status changes
export function clearDisputeStatusCache(companyBenefitId: string) {
  disputeStatusCache.delete(companyBenefitId)
}
