import { useState, useEffect, useCallback, useMemo } from 'react'

interface UserVerificationStatus {
  hasVerified: boolean
  isAdminVerified: boolean
  verification: {
    id: string
    status: string
    createdAt: string
  } | null
  message: string
}

interface BatchUserVerificationStatus {
  [companyBenefitId: string]: UserVerificationStatus
}

interface UseBatchUserVerificationStatusReturn {
  getVerificationStatus: (companyBenefitId: string) => UserVerificationStatus | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

// Global cache to avoid duplicate requests
const batchVerificationStatusCache = new Map<string, UserVerificationStatus>()

export function useBatchUserVerificationStatus(companyBenefitIds: string[]): UseBatchUserVerificationStatusReturn {
  const [batchData, setBatchData] = useState<BatchUserVerificationStatus>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Stabilize the companyBenefitIds array to prevent infinite re-renders
  const stableIds = useMemo(() => {
    return companyBenefitIds.slice().sort()
  }, [companyBenefitIds.join(',')])

  const fetchBatchVerificationStatus = useCallback(async () => {
    if (!stableIds || stableIds.length === 0) {
      setIsLoading(false)
      return
    }

    // Check cache first - if all IDs are cached, use cache
    const uncachedIds = stableIds.filter(id => !batchVerificationStatusCache.has(id))

    if (uncachedIds.length === 0) {
      // All data is cached
      const cachedData: BatchUserVerificationStatus = {}
      stableIds.forEach(id => {
        const cached = batchVerificationStatusCache.get(id)
        if (cached) {
          cachedData[id] = cached
        }
      })
      setBatchData(cachedData)
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/benefit-verifications/user/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyBenefitIds: uncachedIds
        })
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const newData: BatchUserVerificationStatus = await response.json()
      
      // Cache the new results
      Object.entries(newData).forEach(([id, status]) => {
        batchVerificationStatusCache.set(id, status)
      })

      // Combine cached and new data
      const combinedData: BatchUserVerificationStatus = {}
      stableIds.forEach(id => {
        const cached = batchVerificationStatusCache.get(id)
        if (cached) {
          combinedData[id] = cached
        }
      })

      setBatchData(combinedData)
    } catch (err) {
      console.error('Error fetching batch user verification status:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }, [stableIds])

  const refetch = useCallback(async () => {
    // Clear cache for these benefits and refetch
    stableIds.forEach(id => {
      batchVerificationStatusCache.delete(id)
    })
    await fetchBatchVerificationStatus()
  }, [stableIds, fetchBatchVerificationStatus])

  const getVerificationStatus = useCallback((companyBenefitId: string): UserVerificationStatus | null => {
    return batchData[companyBenefitId] || null
  }, [batchData])

  useEffect(() => {
    if (stableIds.length > 0) {
      fetchBatchVerificationStatus()
    } else {
      setIsLoading(false)
      setBatchData({})
    }
  }, [fetchBatchVerificationStatus, stableIds.length])

  return {
    getVerificationStatus,
    isLoading,
    error,
    refetch
  }
}

// Utility function to clear cache when user verifies a benefit
export function clearBatchUserVerificationCache(companyBenefitId?: string) {
  if (companyBenefitId) {
    batchVerificationStatusCache.delete(companyBenefitId)
  } else {
    batchVerificationStatusCache.clear()
  }
}

// Update cache when user verifies a benefit
export function updateBatchUserVerificationCache(
  companyBenefitId: string, 
  hasVerified: boolean, 
  verification: UserVerificationStatus['verification']
) {
  const existing = batchVerificationStatusCache.get(companyBenefitId)
  if (existing) {
    batchVerificationStatusCache.set(companyBenefitId, {
      ...existing,
      hasVerified,
      verification
    })
  }
}
