import { useState, useEffect, useMemo, useCallback } from 'react'

interface VerificationCounts {
  confirmed: number
  disputed: number
  total: number
}

interface BatchVerificationHook {
  getVerificationCounts: (companyBenefitId: string) => VerificationCounts | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

// Global cache to store verification counts and avoid duplicate requests
const verificationCache = new Map<string, VerificationCounts>()

export function useBatchBenefitVerifications(companyBenefitIds: string[]): BatchVerificationHook {
  const [verificationCounts, setVerificationCounts] = useState<Record<string, VerificationCounts>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Create a stable string representation of the IDs
  const idsKey = useMemo(() => companyBenefitIds.sort().join(','), [companyBenefitIds])

  const fetchData = useCallback(async () => {
    if (companyBenefitIds.length === 0) {
      setIsLoading(false)
      setVerificationCounts({})
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/benefit-verifications/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyBenefitIds: companyBenefitIds
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: Record<string, VerificationCounts> = await response.json()

      // Cache the results
      Object.entries(data).forEach(([id, counts]) => {
        verificationCache.set(id, counts)
      })

      setVerificationCounts(data)
    } catch (err) {
      console.error('Error fetching batch verification counts:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }, [companyBenefitIds])

  useEffect(() => {
    if (companyBenefitIds.length === 0) {
      setIsLoading(false)
      return
    }

    // Check if all data is already cached
    const allCached = companyBenefitIds.every(id => verificationCache.has(id))
    if (allCached) {
      const cachedData: Record<string, VerificationCounts> = {}
      companyBenefitIds.forEach(id => {
        const cached = verificationCache.get(id)
        if (cached) {
          cachedData[id] = cached
        }
      })
      setVerificationCounts(cachedData)
      setIsLoading(false)
      return
    }

    fetchData()
  }, [idsKey, fetchData])

  const getVerificationCounts = (companyBenefitId: string): VerificationCounts | null => {
    return verificationCounts[companyBenefitId] || verificationCache.get(companyBenefitId) || null
  }

  const refetch = useCallback(async () => {
    // Clear cache for these benefits and refetch
    companyBenefitIds.forEach(id => {
      verificationCache.delete(id)
    })
    await fetchData()
  }, [companyBenefitIds, fetchData])

  return {
    getVerificationCounts,
    isLoading,
    error,
    refetch
  }
}
