/**
 * User Benefit Verification Status API Tests
 * Tests for the new API endpoint that checks if a user has verified a specific benefit
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { GET } from '@/app/api/benefit-verifications/user/[companyBenefitId]/route'
import type { LocalUser } from '@/lib/local-auth'

// Mock dependencies
vi.mock('@/lib/auth', () => ({
  getCurrentUser: vi.fn(),
}))

vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

vi.mock('@/lib/api-error-handler', () => ({
  withErrorHandling: (handler: any) => handler,
}))

// Get the mocked functions
const mockGetCurrentUser = vi.mocked((await import('@/lib/auth')).getCurrentUser)
const mockQuery = vi.mocked((await import('@/lib/local-db')).query)

// Helper function to create mock users
const createMockUser = (overrides: Partial<LocalUser> = {}): LocalUser => ({
  id: 'user-123',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'user',
  paymentStatus: 'free',
  emailVerified: true,
  createdAt: '2024-01-01T12:00:00Z',
  companyId: null,
  ...overrides
})

describe('GET /api/benefit-verifications/user/[companyBenefitId]', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return hasVerified: false when user is not authenticated', async () => {
    mockGetCurrentUser.mockResolvedValue(null)

    // Mock admin verification check (not admin verified)
    mockQuery.mockResolvedValueOnce({ rows: [{ is_verified: false }], rowCount: 1 })

    const request = new NextRequest('http://localhost/api/benefit-verifications/user/test-benefit-id')
    const params = Promise.resolve({ companyBenefitId: 'test-benefit-id' })

    const response = await GET(request, { params })
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data).toEqual({
      hasVerified: false,
      isAdminVerified: false,
      requiresAuth: true,
      message: 'Authentication required'
    })
    expect(mockQuery).toHaveBeenCalledWith(
      'SELECT is_verified FROM company_benefits WHERE id = $1',
      ['test-benefit-id']
    )
  })

  it('should return hasVerified: false when user has not verified the benefit', async () => {
    const mockUser = createMockUser()
    mockGetCurrentUser.mockResolvedValue(mockUser)

    // Mock admin verification check (not admin verified)
    mockQuery.mockResolvedValueOnce({ rows: [{ is_verified: false }], rowCount: 1 })
    // Mock user verification check (no verification)
    mockQuery.mockResolvedValueOnce({ rows: [], rowCount: 0 })

    const request = new NextRequest('http://localhost/api/benefit-verifications/user/test-benefit-id')
    const params = Promise.resolve({ companyBenefitId: 'test-benefit-id' })

    const response = await GET(request, { params })
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data).toEqual({
      hasVerified: false,
      isAdminVerified: false,
      verification: null,
      message: 'User has not verified this benefit'
    })
    expect(mockQuery).toHaveBeenCalledWith(
      'SELECT is_verified FROM company_benefits WHERE id = $1',
      ['test-benefit-id']
    )
    expect(mockQuery).toHaveBeenCalledWith(
      'SELECT id, status, created_at FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2',
      ['test-benefit-id', 'user-123']
    )
  })

  it('should return hasVerified: true when user has verified the benefit', async () => {
    const mockUser = createMockUser()
    const mockVerification = {
      id: 'verification-456',
      status: 'confirmed',
      created_at: '2024-01-01T12:00:00Z'
    }

    mockGetCurrentUser.mockResolvedValue(mockUser)
    // Mock admin verification check (not admin verified)
    mockQuery.mockResolvedValueOnce({ rows: [{ is_verified: false }], rowCount: 1 })
    // Mock user verification check (has verification)
    mockQuery.mockResolvedValueOnce({ rows: [mockVerification], rowCount: 1 })

    const request = new NextRequest('http://localhost/api/benefit-verifications/user/test-benefit-id')
    const params = Promise.resolve({ companyBenefitId: 'test-benefit-id' })

    const response = await GET(request, { params })
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data).toEqual({
      hasVerified: true,
      isAdminVerified: false,
      verification: {
        id: 'verification-456',
        status: 'confirmed',
        createdAt: '2024-01-01T12:00:00Z'
      },
      message: 'User has verified this benefit'
    })
    expect(mockQuery).toHaveBeenCalledWith(
      'SELECT is_verified FROM company_benefits WHERE id = $1',
      ['test-benefit-id']
    )
    expect(mockQuery).toHaveBeenCalledWith(
      'SELECT id, status, created_at FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2',
      ['test-benefit-id', 'user-123']
    )
  })

  it('should return hasVerified: true when benefit is admin-verified', async () => {
    const mockUser = createMockUser()

    mockGetCurrentUser.mockResolvedValue(mockUser)
    // Mock benefit verification check (verified)
    mockQuery.mockResolvedValueOnce({ rows: [{ is_verified: true }], rowCount: 1 })
    // Mock verification counts check (no user verifications = admin verified)
    mockQuery.mockResolvedValueOnce({ rows: [], rowCount: 0 })
    // Mock user verification check (user hasn't verified)
    mockQuery.mockResolvedValueOnce({ rows: [], rowCount: 0 })

    const request = new NextRequest('http://localhost/api/benefit-verifications/user/test-benefit-id')
    const params = Promise.resolve({ companyBenefitId: 'test-benefit-id' })

    const response = await GET(request, { params })
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data).toEqual({
      hasVerified: true,
      isAdminVerified: true,
      verification: null,
      message: 'Benefit is admin-verified'
    })
    expect(mockQuery).toHaveBeenCalledWith(
      'SELECT is_verified FROM company_benefits WHERE id = $1',
      ['test-benefit-id']
    )
    // New logic makes 3 calls: benefit check, verification counts, user verification
    expect(mockQuery).toHaveBeenCalledTimes(3)
  })

  it('should return 404 when company benefit does not exist', async () => {
    const mockUser = createMockUser()

    mockGetCurrentUser.mockResolvedValue(mockUser)
    // Mock admin verification check (benefit not found)
    mockQuery.mockResolvedValueOnce({ rows: [], rowCount: 0 })

    const request = new NextRequest('http://localhost/api/benefit-verifications/user/test-benefit-id')
    const params = Promise.resolve({ companyBenefitId: 'test-benefit-id' })

    const response = await GET(request, { params })
    const data = await response.json()

    expect(response.status).toBe(404)
    expect(data).toEqual({
      hasVerified: false,
      isAdminVerified: false,
      verification: null,
      message: 'Company benefit not found'
    })
  })

  it('should handle database errors gracefully', async () => {
    const mockUser = createMockUser()
    mockGetCurrentUser.mockResolvedValue(mockUser)
    mockQuery.mockRejectedValue(new Error('Database connection failed'))

    const request = new NextRequest('http://localhost/api/benefit-verifications/user/test-benefit-id')
    const params = Promise.resolve({ companyBenefitId: 'test-benefit-id' })

    await expect(GET(request, { params })).rejects.toThrow('Database connection failed')
  })

  it('should handle different verification statuses', async () => {
    const mockUser = createMockUser()
    const mockVerification = {
      id: 'verification-456',
      status: 'disputed',
      created_at: '2024-01-01T12:00:00Z'
    }

    mockGetCurrentUser.mockResolvedValue(mockUser)
    mockQuery.mockResolvedValue({ rows: [mockVerification], rowCount: 1 })

    const request = new NextRequest('http://localhost/api/benefit-verifications/user/test-benefit-id')
    const params = Promise.resolve({ companyBenefitId: 'test-benefit-id' })

    const response = await GET(request, { params })
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.hasVerified).toBe(true)
    expect(data.verification.status).toBe('disputed')
  })
})
