/**
 * API Access Control Tests
 * Tests the actual API security logic without test environment bypasses
 */

import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest'
import { NextRequest } from 'next/server'
import { isWebUIRequest, canAccessAPIData } from '@/lib/api-access-control'

describe('API Access Control', () => {
  beforeAll(() => {
    // Mock environment variables to disable test environment bypasses
    vi.stubEnv('NODE_ENV', 'production')
    vi.stubEnv('VITEST', undefined)
    vi.stubEnv('TEST_DATABASE_URL', undefined)
  })

  afterAll(() => {
    // Restore original environment
    vi.unstubAllEnvs()
  })

  describe('isWebUIRequest', () => {
    it('should allow requests with matching origin header', () => {
      const request = new NextRequest('https://benefitlens.de/api/companies', {
        headers: {
          'origin': 'https://benefitlens.de',
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      expect(isWebUIRequest(request)).toBe(true)
    })

    it('should allow requests with matching referer header', () => {
      const request = new NextRequest('https://benefitlens.de/api/companies', {
        headers: {
          'referer': 'https://benefitlens.de/',
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      expect(isWebUIRequest(request)).toBe(true)
    })

    it('should allow requests with Next.js headers', () => {
      const request = new NextRequest('https://benefitlens.de/api/companies', {
        headers: {
          'x-nextjs-data': '1',
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      expect(isWebUIRequest(request)).toBe(true)
    })

    it('should allow browser-like requests with domain in referer', () => {
      const request = new NextRequest('https://benefitlens.de/api/companies', {
        headers: {
          'referer': 'https://benefitlens.de/search',
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      expect(isWebUIRequest(request)).toBe(true)
    })

    it('should allow browser-like requests with empty referer', () => {
      const request = new NextRequest('https://benefitlens.de/api/companies', {
        headers: {
          'referer': '',
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      expect(isWebUIRequest(request)).toBe(true)
    })

    it('should block direct curl requests', () => {
      const request = new NextRequest('https://benefitlens.de/api/companies', {
        headers: {
          'user-agent': 'curl/7.68.0'
        }
      })

      expect(isWebUIRequest(request)).toBe(false)
    })

    it('should block requests with wrong origin', () => {
      const request = new NextRequest('https://benefitlens.de/api/companies', {
        headers: {
          'origin': 'https://malicious-site.com',
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      expect(isWebUIRequest(request)).toBe(false)
    })

    it('should block requests with wrong referer', () => {
      const request = new NextRequest('https://benefitlens.de/api/companies', {
        headers: {
          'referer': 'https://malicious-site.com/',
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      expect(isWebUIRequest(request)).toBe(false)
    })

    it('should block non-browser user agents without proper headers', () => {
      const request = new NextRequest('https://benefitlens.de/api/companies', {
        headers: {
          'user-agent': 'python-requests/2.25.1'
        }
      })

      expect(isWebUIRequest(request)).toBe(false)
    })
  })

  describe('canAccessAPIData (without test bypass)', () => {
    it('should allow web UI requests', async () => {
      const request = new NextRequest('https://benefitlens.de/api/companies', {
        headers: {
          'origin': 'https://benefitlens.de',
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      const result = await canAccessAPIData(request)
      expect(result.allowed).toBe(true)
      expect(result.reason).toBe('web-ui')
    })

    it('should block direct API requests', async () => {
      const request = new NextRequest('https://benefitlens.de/api/companies', {
        headers: {
          'user-agent': 'curl/7.68.0'
        }
      })

      const result = await canAccessAPIData(request)
      expect(result.allowed).toBe(false)
      expect(result.reason).toBe('blocked')
    })
  })
})
