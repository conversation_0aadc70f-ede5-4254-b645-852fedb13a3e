import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import SavedCompaniesPage from '@/app/saved-companies/page'

// Mock the fetch function
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock the components
vi.mock('@/components/company-card', () => ({
  CompanyCard: ({ company }: { company: { name: string } }) => (
    <div data-testid="company-card">{company.name}</div>
  )
}))

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) => (
    <button onClick={onClick} data-testid="button">{children}</button>
  )
}))

describe('SavedCompaniesPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render without TDZ errors', async () => {
    // Mock authentication failure
    const mockFetch = vi.mocked(fetch)
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 401,
    } as Response)

    // This test verifies that the component can be rendered without
    // "Cannot access 'fetchSavedCompanies' before initialization" error
    expect(() => {
      render(<SavedCompaniesPage />)
    }).not.toThrow()

    // Verify the component renders the loading state initially
    expect(screen.getByText('Loading your saved companies...')).toBeInTheDocument()

    // Wait for the authentication check to complete
    await waitFor(() => {
      expect(screen.getByText('Please sign in to view your saved companies')).toBeInTheDocument()
    })

    // Verify fetch was called for authentication
    expect(mockFetch).toHaveBeenCalledWith('/api/auth/me')
  })

  it('should handle successful authentication and fetch saved companies', async () => {
    const mockFetch = vi.mocked(fetch)
    
    // Mock successful authentication
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
    } as Response)

    // Mock successful saved companies fetch
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: async () => [
        {
          id: '1',
          name: 'Test Company',
          saved_at: '2023-01-01',
          benefit_count: 5
        }
      ]
    } as Response)

    render(<SavedCompaniesPage />)

    // Wait for both API calls to complete
    await waitFor(() => {
      expect(screen.getByText('Saved Companies')).toBeInTheDocument()
    })

    // Verify both API calls were made
    expect(mockFetch).toHaveBeenCalledWith('/api/auth/me')
    expect(mockFetch).toHaveBeenCalledWith('/api/saved-companies')
  })

  it('should handle empty saved companies list', async () => {
    const mockFetch = vi.mocked(fetch)
    
    // Mock successful authentication
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
    } as Response)

    // Mock empty saved companies response
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: async () => []
    } as Response)

    render(<SavedCompaniesPage />)

    await waitFor(() => {
      expect(screen.getByText('No saved companies yet')).toBeInTheDocument()
    })
  })
})
