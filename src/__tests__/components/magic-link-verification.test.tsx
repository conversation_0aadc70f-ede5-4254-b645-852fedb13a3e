/**
 * Magic Link Verification Component Tests
 * Tests the actual component rendering and state transitions to catch UI issues
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { MagicLinkVerification } from '@/components/auth/magic-link-verification'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

// Mock fetch
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock window.location.hash
const mockLocation = {
  hash: '',
}
Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
})

describe('MagicLinkVerification Component', () => {
  const mockPush = vi.fn()
  const mockRouter = { push: mockPush }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
    mockLocation.hash = ''
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should show loading state initially when validating token', async () => {
    // Set up a valid token in the hash
    mockLocation.hash = '#valid-token-123'

    // Mock validation endpoint to be slow (to test loading state)
    mockFetch.mockImplementation(() => new Promise(resolve => {
      setTimeout(() => resolve({
        ok: true,
        json: async () => ({ success: true, message: 'Token is valid' })
      }), 100)
    }))

    render(<MagicLinkVerification />)

    // Should show validating state initially
    expect(screen.getByText('Validating...')).toBeInTheDocument()
    expect(screen.getByText('Please wait while we validate your magic link.')).toBeInTheDocument()
  })

  it('should show confirmation button after successful validation', async () => {
    mockLocation.hash = '#valid-token-123'

    // Mock successful validation
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, message: 'Token is valid' })
    })

    render(<MagicLinkVerification />)

    // Wait for validation to complete
    await waitFor(() => {
      expect(screen.getByText('Confirm Your Sign In')).toBeInTheDocument()
    })

    expect(screen.getByText('Your magic link is valid! Click the button below to complete your sign in.')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Complete Sign In' })).toBeInTheDocument()
  })

  it('should show error state for invalid token', async () => {
    mockLocation.hash = '#invalid-token'

    // Mock validation failure
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 401,
      json: async () => ({ error: 'Invalid or expired magic link' })
    })

    render(<MagicLinkVerification />)

    await waitFor(() => {
      expect(screen.getByText('Verification Failed')).toBeInTheDocument()
    })

    expect(screen.getByText('Invalid or expired magic link')).toBeInTheDocument()
  })

  it('should show error state when no token is provided', () => {
    // No hash token
    mockLocation.hash = ''

    render(<MagicLinkVerification />)

    expect(screen.getByText('Invalid Link')).toBeInTheDocument()
    expect(screen.getByText('This magic link is invalid or missing required parameters.')).toBeInTheDocument()
  })

  it('should transition smoothly from confirmation to verification without showing error state', async () => {
    mockLocation.hash = '#valid-token-123'

    // Mock validation success
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, message: 'Token is valid' })
    })

    render(<MagicLinkVerification />)

    // Wait for confirmation button to appear
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Complete Sign In' })).toBeInTheDocument()
    })

    // Mock verification success
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ 
        success: true, 
        user: { id: '123', email: '<EMAIL>', firstName: 'Test', lastName: 'User', role: 'user' }
      })
    })

    // Click the confirmation button
    fireEvent.click(screen.getByRole('button', { name: 'Complete Sign In' }))

    // Should immediately show "Signing In..." state, NOT error state
    expect(screen.getByText('Signing In...')).toBeInTheDocument()
    
    // Should never show error states during this transition
    expect(screen.queryByText('Verification Failed')).not.toBeInTheDocument()
    expect(screen.queryByText('Invalid Link')).not.toBeInTheDocument()

    // Wait for success state
    await waitFor(() => {
      expect(screen.getByText('Authentication Successful!')).toBeInTheDocument()
    })

    expect(screen.getByText('Welcome to BenefitLens! You\'re being redirected to your dashboard...')).toBeInTheDocument()
  })

  it('should handle verification failure after confirmation', async () => {
    mockLocation.hash = '#valid-token-123'

    // Mock validation success
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, message: 'Token is valid' })
    })

    render(<MagicLinkVerification />)

    // Wait for confirmation button
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Complete Sign In' })).toBeInTheDocument()
    })

    // Mock verification failure (token already used)
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 401,
      json: async () => ({ error: 'Invalid or expired magic link' })
    })

    // Click confirmation button
    fireEvent.click(screen.getByRole('button', { name: 'Complete Sign In' }))

    // Should show signing in state first
    expect(screen.getByText('Signing In...')).toBeInTheDocument()

    // Wait for error state
    await waitFor(() => {
      expect(screen.getByText('Verification Failed')).toBeInTheDocument()
    })

    expect(screen.getByText('Invalid or expired magic link')).toBeInTheDocument()
  })

  it('should show dedicated loading state during verification', async () => {
    mockLocation.hash = '#valid-token-123'

    // Mock validation success
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, message: 'Token is valid' })
    })

    render(<MagicLinkVerification />)

    // Wait for confirmation button
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Complete Sign In' })).toBeInTheDocument()
    })

    // Mock slow verification
    mockFetch.mockImplementation(() => new Promise(resolve => {
      setTimeout(() => resolve({
        ok: true,
        json: async () => ({
          success: true,
          user: { id: '123', email: '<EMAIL>', firstName: 'Test', lastName: 'User', role: 'user' }
        })
      }), 100)
    }))

    const button = screen.getByRole('button', { name: 'Complete Sign In' })
    fireEvent.click(button)

    // Should show dedicated loading state with no buttons
    expect(screen.getByText('Signing In...')).toBeInTheDocument()
    expect(screen.getByText('Please wait while we complete your sign in.')).toBeInTheDocument()

    // Should not show the confirmation button anymore
    expect(screen.queryByRole('button', { name: 'Complete Sign In' })).not.toBeInTheDocument()
  })

  it('should never show error states during normal successful flow', async () => {
    mockLocation.hash = '#valid-token-123'

    // Mock validation success
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, message: 'Token is valid' })
    })

    render(<MagicLinkVerification />)

    // During validation phase
    expect(screen.queryByText('Verification Failed')).not.toBeInTheDocument()
    expect(screen.queryByText('Invalid Link')).not.toBeInTheDocument()

    // Wait for confirmation button
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Complete Sign In' })).toBeInTheDocument()
    })

    // During confirmation phase
    expect(screen.queryByText('Verification Failed')).not.toBeInTheDocument()
    expect(screen.queryByText('Invalid Link')).not.toBeInTheDocument()

    // Mock verification success
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        user: { id: '123', email: '<EMAIL>', firstName: 'Test', lastName: 'User', role: 'user' }
      })
    })

    // Click confirmation button
    fireEvent.click(screen.getByRole('button', { name: 'Complete Sign In' }))

    // During verification phase - should never show error states
    expect(screen.queryByText('Verification Failed')).not.toBeInTheDocument()
    expect(screen.queryByText('Invalid Link')).not.toBeInTheDocument()

    // Wait for success
    await waitFor(() => {
      expect(screen.getByText('Authentication Successful!')).toBeInTheDocument()
    })

    // During success phase
    expect(screen.queryByText('Verification Failed')).not.toBeInTheDocument()
    expect(screen.queryByText('Invalid Link')).not.toBeInTheDocument()
  })
})
