/**
 * Benefit Verification Component UI Tests
 * Tests the component behavior when user has/hasn't verified benefits
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BenefitVerification } from '@/components/benefit-verification'
import { ToastProvider } from '@/components/ui/toast'

// Mock the custom hook
vi.mock('@/hooks/use-user-verification-status', () => ({
  useUserVerificationStatus: vi.fn(),
  updateUserVerificationCache: vi.fn(),
}))

// Get the mocked function
const mockUseUserVerificationStatus = vi.mocked((await import('@/hooks/use-user-verification-status')).useUserVerificationStatus)

// Mock fetch
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('BenefitVerification Component UI', () => {
  const defaultProps = {
    companyBenefitId: 'test-benefit-id',
    benefitName: 'Health Insurance',
    companyName: 'Test Company',
    benefitId: 'benefit-123',
    companyId: 'company-123',
    onVerificationComplete: vi.fn(),
    companyAuthStatus: {
      authorized: true,
      message: 'You are authorized to verify benefits for this company'
    }
  }

  // Helper function to render component with ToastProvider
  const renderWithToast = (component: React.ReactElement) => {
    return render(
      <ToastProvider>
        {component}
      </ToastProvider>
    )
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Mock successful auth check
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ user: { id: 'user-123' } })
    })
  })

  it('should show "Confirm Benefit" button when user has not verified the benefit', async () => {
    mockUseUserVerificationStatus.mockReturnValue({
      hasVerified: false,
      isAdminVerified: false,
      verification: null,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    })

    renderWithToast(<BenefitVerification {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('Confirm Benefit')).toBeInTheDocument()
    })

    const confirmButton = screen.getByRole('button', { name: /Confirm Benefit/ })
    expect(confirmButton).not.toBeDisabled()
    expect(confirmButton).toHaveClass('bg-blue-600')
  })

  it('should not render anything when user has verified the benefit', async () => {
    mockUseUserVerificationStatus.mockReturnValue({
      hasVerified: true,
      isAdminVerified: false,
      verification: {
        id: 'verification-123',
        status: 'confirmed',
        createdAt: '2024-01-01T12:00:00Z'
      },
      isLoading: false,
      error: null,
      refetch: vi.fn()
    })

    const { container } = renderWithToast(<BenefitVerification {...defaultProps} />)

    await waitFor(() => {
      // Component should return null for verified benefits
      expect(container.firstChild).toBeNull()
    })
  })

  it('should not render anything when benefit is admin-verified', async () => {
    mockUseUserVerificationStatus.mockReturnValue({
      hasVerified: false,
      isAdminVerified: true,
      verification: null,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    })

    const { container } = renderWithToast(<BenefitVerification {...defaultProps} />)

    await waitFor(() => {
      // Component should return null for admin-verified benefits
      expect(container.firstChild).toBeNull()
    })
  })

  it('should show loading state while checking verification status', async () => {
    mockUseUserVerificationStatus.mockReturnValue({
      hasVerified: false,
      isAdminVerified: false,
      verification: null,
      isLoading: true,
      error: null,
      refetch: vi.fn()
    })

    renderWithToast(<BenefitVerification {...defaultProps} />)

    expect(screen.getByText('Checking verification status...')).toBeInTheDocument()
    expect(screen.queryByText('Confirm Benefit')).not.toBeInTheDocument()
  })

  it('should allow verification when user has not verified and is authorized', async () => {
    const mockRefetch = vi.fn()
    mockUseUserVerificationStatus.mockReturnValue({
      hasVerified: false,
      isAdminVerified: false,
      verification: null,
      isLoading: false,
      error: null,
      refetch: mockRefetch
    })

    // Mock successful verification API call
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true })
    })

    renderWithToast(<BenefitVerification {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('Confirm Benefit')).toBeInTheDocument()
    })

    const confirmButton = screen.getByRole('button', { name: /Confirm Benefit/ })
    fireEvent.click(confirmButton)

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/benefit-verifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyBenefitId: 'test-benefit-id',
          status: 'confirmed',
          comment: ''
        })
      })
    })

    expect(mockRefetch).toHaveBeenCalled()
    expect(defaultProps.onVerificationComplete).toHaveBeenCalled()
  })

  it('should not render anything when user has already verified', async () => {
    mockUseUserVerificationStatus.mockReturnValue({
      hasVerified: true,
      isAdminVerified: false,
      verification: {
        id: 'verification-123',
        status: 'confirmed',
        createdAt: '2024-01-01T12:00:00Z'
      },
      isLoading: false,
      error: null,
      refetch: vi.fn()
    })

    const { container } = renderWithToast(<BenefitVerification {...defaultProps} />)

    await waitFor(() => {
      // Component should return null for verified benefits
      expect(container.firstChild).toBeNull()
    })
  })

  it('should redirect to sign-in when user is not authenticated', async () => {
    mockUseUserVerificationStatus.mockReturnValue({
      hasVerified: false,
      isAdminVerified: false,
      verification: null,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    })

    // Mock failed auth check
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 401
    })

    // Mock window.location.href
    const mockLocation = { href: '' }
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true,
    })

    renderWithToast(<BenefitVerification {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('Confirm Benefit')).toBeInTheDocument()
    })

    const confirmButton = screen.getByRole('button', { name: /Confirm Benefit/ })
    fireEvent.click(confirmButton)

    await waitFor(() => {
      expect(mockLocation.href).toBe('/sign-in')
    })
  })

  it('should not render when user is not authorized', async () => {
    mockUseUserVerificationStatus.mockReturnValue({
      hasVerified: false,
      isAdminVerified: false,
      verification: null,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    })

    const unauthorizedProps = {
      ...defaultProps,
      companyAuthStatus: {
        authorized: false,
        message: 'You are not authorized to verify benefits for this company'
      }
    }

    const { container } = renderWithToast(<BenefitVerification {...unauthorizedProps} />)

    await waitFor(() => {
      expect(container.firstChild).toBeNull()
    })
  })
})
