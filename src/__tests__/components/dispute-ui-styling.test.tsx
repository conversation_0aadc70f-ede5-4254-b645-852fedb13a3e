import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { BenefitRemovalDispute } from '@/components/benefit-removal-dispute'

// Mock fetch
global.fetch = vi.fn()

// Mock auth
vi.mock('@/lib/auth', () => ({
  getCurrentUser: vi.fn(() => Promise.resolve({
    id: 'user-123',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User'
  }))
}))

describe('Dispute UI Styling', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock the dispute status API call
    ;(global.fetch as any).mockImplementation((url: string) => {
      if (url.includes('/api/benefit-removal-disputes')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            disputes: [],
            total: 0,
            approved: 0,
            pending: 0,
            rejected: 0,
            userDispute: null,
            canDispute: true
          })
        })
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({})
      })
    })
  })

  it('should have correct text color in dispute textarea', async () => {
    render(
      <BenefitRemovalDispute
        companyBenefitId="test-benefit-id"
        benefitName="Test Benefit"
        companyName="Test Company"
      />
    )

    // Wait for component to load
    await waitFor(() => {
      expect(screen.queryByText('Request Removal')).toBeInTheDocument()
    })

    // Click the Request Removal button to show the form
    const requestButton = screen.getByText('Request Removal')
    fireEvent.click(requestButton)

    // Wait for textarea to appear
    await waitFor(() => {
      const textarea = screen.getByPlaceholderText(/Why should "Test Benefit" be removed/)
      expect(textarea).toBeInTheDocument()
      
      // Check that textarea has black text color (text-gray-900)
      expect(textarea).toHaveClass('text-gray-900')
      
      // Check that textarea has proper focus styling
      expect(textarea).toHaveClass('focus:ring-2')
      expect(textarea).toHaveClass('focus:ring-blue-500')
      expect(textarea).toHaveClass('focus:border-blue-500')
    })
  })

  it('should show Request Removal button with consistent destructive styling', async () => {
    render(
      <BenefitRemovalDispute
        companyBenefitId="test-benefit-id"
        benefitName="Test Benefit"
        companyName="Test Company"
      />
    )

    // Wait for component to load
    await waitFor(() => {
      const requestButton = screen.getByText('Request Removal')
      expect(requestButton).toBeInTheDocument()

      // Check that button has proper destructive styling (no icons as per user request)
      expect(requestButton).toHaveClass('bg-destructive')
      expect(requestButton).toHaveClass('text-destructive-foreground')
      expect(requestButton).toHaveClass('hover:bg-destructive/90')
    })
  })

  it('should show Submit Dispute button with yellow styling', async () => {
    render(
      <BenefitRemovalDispute
        companyBenefitId="test-benefit-id"
        benefitName="Test Benefit"
        companyName="Test Company"
      />
    )

    // Wait for component to load and click Request Removal
    await waitFor(() => {
      expect(screen.queryByText('Request Removal')).toBeInTheDocument()
    })

    const requestButton = screen.getByText('Request Removal')
    fireEvent.click(requestButton)

    // Wait for form to appear
    await waitFor(() => {
      const submitButton = screen.getByText('Submit Dispute')
      expect(submitButton).toBeInTheDocument()
      
      // Check that submit button has yellow styling
      expect(submitButton).toHaveClass('bg-yellow-600')
      expect(submitButton).toHaveClass('hover:bg-yellow-700')
      expect(submitButton).toHaveClass('text-white')
    })
  })
})
