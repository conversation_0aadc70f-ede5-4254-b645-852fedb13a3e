/**
 * Global teardown for E2E tests
 * Cleans up test database and environment
 */

import { config } from 'dotenv'
import { cleanupAllE2ETestData } from './test-cleanup'

// Load environment variables
config({ path: '.env.local' })

async function globalTeardown() {
  console.log('🎭 Starting E2E test teardown...')

  try {
    // Clean up E2E test data using shared utility
    await cleanupAllE2ETestData()

    console.log('✅ E2E test teardown complete')
  } catch (error) {
    console.error('❌ E2E test teardown failed:', error)
    // Don't throw - teardown failures shouldn't break the build
  }
}

// Legacy function - now using shared cleanup utility
// Kept for backward compatibility but delegates to shared utility
async function cleanupE2EDatabase() {
  await cleanupAllE2ETestData()
}



export default globalTeardown
