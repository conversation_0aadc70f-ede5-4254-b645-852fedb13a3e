/**
 * Accessibility E2E Tests
 * Tests accessibility features, keyboard navigation, and ARIA compliance
 */

import { test, expect } from '@playwright/test'
import { waitForPageLoad } from './auth-helpers'

test.describe('Accessibility Tests', () => {
  test('Keyboard Navigation', async ({ page }) => {
    await page.goto('/')
    await waitForPageLoad(page)

    // Test tab navigation through main elements
    await page.keyboard.press('Tab') // Should focus on first interactive element
    
    // Check that focus is visible
    const focusedElement = page.locator(':focus')
    await expect(focusedElement).toBeVisible()
    
    // Continue tabbing through navigation
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    
    // Test Enter key activation on focused button
    const searchInput = page.locator('input[placeholder*="Search for benefits or companies"]')
    await searchInput.focus()
    await searchInput.fill('Tech')
    await page.keyboard.press('Enter')
    await waitForPageLoad(page)
    
    // Verify search worked
    const results = page.locator('.company-card, [data-testid="company-card"], .search-result')
    await expect(results.first()).toBeVisible({ timeout: 10000 })
    
    console.log('✅ Keyboard navigation working')
  })

  test('ARIA Labels and Roles', async ({ page }) => {
    await page.goto('/')
    await waitForPageLoad(page)

    // Check for proper ARIA labels on interactive elements
    const mobileMenuButton = page.locator('button[aria-label="Toggle mobile menu"]')
    if (await mobileMenuButton.isVisible()) {
      await expect(mobileMenuButton).toHaveAttribute('aria-label', 'Toggle mobile menu')
      console.log('✅ Mobile menu button has proper ARIA label')
    }

    // Check for role="alert" on alert components
    const alerts = page.locator('[role="alert"]')
    if (await alerts.count() > 0) {
      console.log('✅ Alert components have proper role attribute')
    }

    // Check for proper heading hierarchy
    const h1Elements = page.locator('h1')
    const h1Count = await h1Elements.count()
    expect(h1Count).toBeGreaterThanOrEqual(1) // Should have at least one h1
    console.log(`✅ Found ${h1Count} h1 element(s)`)

    console.log('✅ ARIA labels and roles verified')
  })

  test('Color Contrast and Readability', async ({ page }) => {
    await page.goto('/')
    await waitForPageLoad(page)

    // Check that text elements have sufficient contrast
    const textElements = [
      page.locator('h1').first(),
      page.locator('p').first(),
      page.locator('a').first(),
      page.locator('button').first()
    ]

    for (const element of textElements) {
      if (await element.isVisible()) {
        const styles = await element.evaluate((el) => {
          const computed = window.getComputedStyle(el)
          return {
            color: computed.color,
            backgroundColor: computed.backgroundColor,
            fontSize: computed.fontSize
          }
        })
        
        // Basic checks for readable font sizes
        const fontSize = parseInt(styles.fontSize)
        expect(fontSize).toBeGreaterThanOrEqual(12) // Minimum readable font size
        
        console.log(`✅ Element has readable font size: ${styles.fontSize}`)
      }
    }

    console.log('✅ Color contrast and readability verified')
  })

  test('Focus Management', async ({ page }) => {
    await page.goto('/')
    await waitForPageLoad(page)

    // Test focus trap in mobile menu (if available)
    const mobileMenuButton = page.locator('button[aria-label="Toggle mobile menu"]')
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click()
      
      // Check that focus moves to menu items
      await page.keyboard.press('Tab')
      const focusedElement = page.locator(':focus')
      await expect(focusedElement).toBeVisible()
      
      // Close menu with Escape key
      await page.keyboard.press('Escape')
      
      console.log('✅ Mobile menu focus management working')
    }

    console.log('✅ Focus management verified')
  })

  test('Screen Reader Compatibility', async ({ page }) => {
    await page.goto('/')
    await waitForPageLoad(page)

    // Check for proper semantic HTML structure
    const nav = page.locator('nav')
    const navCount = await nav.count()
    expect(navCount).toBeGreaterThan(0) // Nav exists, even if hidden on mobile

    const main = page.locator('main')
    if (await main.count() > 0) {
      await expect(main.first()).toBeVisible()
      console.log('✅ Main content area properly marked')
    }

    // Check for proper form labels
    const inputs = page.locator('input')
    const inputCount = await inputs.count()
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i)
      const hasLabel = await input.evaluate((el) => {
        const id = el.id
        const placeholder = (el as HTMLInputElement).placeholder
        const ariaLabel = el.getAttribute('aria-label')
        const ariaLabelledBy = el.getAttribute('aria-labelledby')
        
        // Check if input has proper labeling
        return !!(
          (id && document.querySelector(`label[for="${id}"]`)) ||
          placeholder ||
          ariaLabel ||
          ariaLabelledBy
        )
      })
      
      expect(hasLabel).toBe(true)
    }

    console.log(`✅ All ${inputCount} inputs have proper labeling`)
  })

  test('Mobile Accessibility', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip()
    }

    await page.goto('/')
    await waitForPageLoad(page)

    // Check touch target sizes for primary buttons (minimum 44px)
    const primaryButtons = page.locator('button:not(.icon-only-small)')
    const buttonCount = await primaryButtons.count()

    let validTouchTargets = 0
    for (let i = 0; i < Math.min(buttonCount, 5); i++) { // Check first 5 buttons
      const button = primaryButtons.nth(i)
      if (await button.isVisible()) {
        const boundingBox = await button.boundingBox()
        if (boundingBox) {
          if (boundingBox.height >= 44 && boundingBox.width >= 44) {
            validTouchTargets++
          }
        }
      }
    }

    // At least some primary buttons should meet touch target requirements
    expect(validTouchTargets).toBeGreaterThan(0)

    // Check that mobile menu is accessible
    const mobileMenuButton = page.locator('button[aria-label="Toggle mobile menu"]')
    if (await mobileMenuButton.isVisible()) {
      const boundingBox = await mobileMenuButton.boundingBox()
      if (boundingBox) {
        expect(boundingBox.height).toBeGreaterThanOrEqual(44)
        expect(boundingBox.width).toBeGreaterThanOrEqual(44)
      }
      console.log('✅ Mobile menu button meets touch target size requirements')
    }

    console.log('✅ Mobile accessibility verified')
  })

  test('Error Message Accessibility', async ({ page }) => {
    // Test error message accessibility by triggering a form error
    await page.goto('/sign-in')
    await waitForPageLoad(page)

    // Try to submit form without email
    const submitButton = page.locator('button[type="submit"]')
    if (await submitButton.isVisible()) {
      await submitButton.click()
      
      // Check for error messages with proper ARIA attributes
      const errorMessages = page.locator('[role="alert"], .error, [aria-invalid="true"]')
      if (await errorMessages.count() > 0) {
        console.log('✅ Error messages have proper accessibility attributes')
      }
    }

    console.log('✅ Error message accessibility verified')
  })
})
