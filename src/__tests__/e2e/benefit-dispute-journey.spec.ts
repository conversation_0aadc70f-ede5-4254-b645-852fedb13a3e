import { test, expect } from '@playwright/test'
import { signInUser, signInAdmin, waitForPageLoad } from './auth-helpers'
import { query } from '@/lib/local-db'

test.describe('Benefit Dispute Simple Test', () => {
  let testCompanyId: string
  let testBenefitId: string
  let testCompanyBenefitId: string

  test.beforeAll(async () => {
    // Set up test data for dispute journey
    console.log('🔧 Setting up simple dispute test data...')

    // Clean up any existing test data first
    await query(`DELETE FROM users WHERE email IN ('simple-user1@simpletest.e2e', 'simple-user2@simpletest.e2e')`)
    await query(`DELETE FROM company_benefits WHERE company_id IN (SELECT id FROM companies WHERE domain = 'simpletest.e2e')`)
    await query(`DELETE FROM companies WHERE domain = 'simpletest.e2e'`)
    await query(`DELETE FROM benefits WHERE name = 'Simple Test Benefit'`)

    // Create test company
    const companyResult = await query(`
      INSERT INTO companies (name, domain, industry, size, description)
      VALUES ('Simple Test Corp', 'simpletest.e2e', 'Technology', 'medium', 'Test company for simple dispute testing')
      RETURNING id
    `)
    testCompanyId = companyResult.rows[0].id

    // Create test benefit (using proper category_id lookup)
    const benefitResult = await query(`
      INSERT INTO benefits (name, category_id, icon)
      VALUES ('Simple Test Benefit', (SELECT id FROM benefit_categories WHERE name = 'e2e_health'), '🧪')
      RETURNING id
    `)
    testBenefitId = benefitResult.rows[0].id

    // Create verified company benefit
    const companyBenefitResult = await query(`
      INSERT INTO company_benefits (company_id, benefit_id, is_verified)
      VALUES ($1, $2, true)
      RETURNING id
    `, [testCompanyId, testBenefitId])
    testCompanyBenefitId = companyBenefitResult.rows[0].id

    // Create test users
    await query(`
      INSERT INTO users (email, first_name, last_name, role, payment_status, email_verified)
      VALUES
        ('simple-user1@simpletest.e2e', 'Simple', 'User1', 'user', 'free', true),
        ('simple-user2@simpletest.e2e', 'Simple', 'User2', 'user', 'free', true)
    `)

    console.log('✅ Simple dispute test data setup complete')
  })

  test.afterAll(async () => {
    // Clean up test data
    console.log('🧹 Cleaning up simple dispute test data...')
    
    await query(`DELETE FROM benefit_removal_disputes WHERE company_benefit_id = $1`, [testCompanyBenefitId])
    await query(`DELETE FROM company_benefits WHERE id = $1`, [testCompanyBenefitId])
    await query(`DELETE FROM benefits WHERE id = $1`, [testBenefitId])
    await query(`DELETE FROM companies WHERE id = $1`, [testCompanyId])
    await query(`DELETE FROM users WHERE email IN ('simple-user1@simpletest.e2e', 'simple-user2@simpletest.e2e')`)
    
    console.log('✅ Simple dispute test data cleanup complete')
  })

  test('Complete Benefit Dispute Journey - API Level Test', async ({ page }) => {
    console.log('🎯 Starting API-level benefit dispute journey test')

    // Step 1: User 1 submits dispute via API
    console.log('👤 Step 1: User 1 submits dispute')
    await signInUser(page, 'simple-user1@simpletest.e2e')
    
    const dispute1Response = await page.request.post('/api/benefit-removal-disputes', {
      data: {
        companyBenefitId: testCompanyBenefitId,
        reason: 'This benefit is no longer offered by our company'
      }
    })
    expect(dispute1Response.ok()).toBeTruthy()
    const dispute1Data = await dispute1Response.json()
    console.log('✅ User 1 dispute submitted:', dispute1Data.message)

    // Step 2: User 2 submits dispute via API
    console.log('👤 Step 2: User 2 submits dispute')
    await signInUser(page, 'simple-user2@simpletest.e2e')
    
    const dispute2Response = await page.request.post('/api/benefit-removal-disputes', {
      data: {
        companyBenefitId: testCompanyBenefitId,
        reason: 'I can confirm this benefit was discontinued last month'
      }
    })
    expect(dispute2Response.ok()).toBeTruthy()
    const dispute2Data = await dispute2Response.json()
    console.log('✅ User 2 dispute submitted:', dispute2Data.message)

    // Step 3: Admin approves first dispute
    console.log('👨‍💼 Step 3: Admin approves first dispute')
    await signInAdmin(page, '<EMAIL>')

    const approve1Response = await page.request.post('/api/admin/benefit-removal-disputes', {
      data: {
        disputeId: dispute1Data.dispute.id,
        action: 'approve',
        adminComment: 'First dispute approved - investigating benefit status'
      }
    })

    // Debug information if the request fails
    if (!approve1Response.ok()) {
      console.error('❌ Admin approval failed with status:', approve1Response.status())
      const errorText = await approve1Response.text()
      console.error('❌ Error response:', errorText)

      // Try to get more details about the error
      try {
        const errorJson = JSON.parse(errorText)
        console.error('❌ Error details:', errorJson)
      } catch (e) {
        console.error('❌ Could not parse error as JSON')
      }
    }

    expect(approve1Response.ok()).toBeTruthy()
    const approve1Data = await approve1Response.json()
    console.log('✅ First dispute approved:', approve1Data.message)
    expect(approve1Data.benefitRemoved).toBe(false)

    // Step 4: Admin approves second dispute (should trigger benefit removal)
    console.log('👨‍💼 Step 4: Admin approves second dispute - should trigger benefit removal')
    
    const approve2Response = await page.request.post('/api/admin/benefit-removal-disputes', {
      data: {
        disputeId: dispute2Data.dispute.id,
        action: 'approve',
        adminComment: 'Second dispute approved - benefit should be removed'
      }
    })
    expect(approve2Response.ok()).toBeTruthy()
    const approve2Data = await approve2Response.json()
    console.log('✅ Second dispute approved:', approve2Data.message)
    
    // Verify benefit was automatically removed
    expect(approve2Data.benefitRemoved).toBe(true)
    expect(approve2Data.message).toContain('Benefit automatically removed')
    expect(approve2Data.approvedDisputesCount).toBe(2)
    expect(approve2Data.uniqueUserCount).toBe(2)

    // Step 5: Verify the benefit has been removed from the database
    console.log('🔍 Step 5: Verify benefit removal from database')
    const benefitCheck = await query(
      'SELECT * FROM company_benefits WHERE id = $1',
      [testCompanyBenefitId]
    )
    expect(benefitCheck.rows.length).toBe(0)
    console.log('✅ Benefit confirmed removed from database')

    console.log('🎉 Complete benefit dispute journey test completed successfully!')
  })
})
