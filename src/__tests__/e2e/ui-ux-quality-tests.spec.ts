/**
 * UI/UX Quality Tests
 * 
 * These tests focus on visual layout, scrolling behavior, and user experience
 * quality that functional tests might miss. They help catch issues like:
 * - Modal scrolling problems
 * - Content overflow issues
 * - Layout inconsistencies
 * - Touch interaction problems
 * - Visual regressions
 */

import { test, expect, Page } from '@playwright/test'
import { signInUser, waitForPageLoad } from './auth-helpers'

test.describe('UI/UX Quality Tests', () => {
  
  test('Modal Scrolling and Layout Quality', async ({ page }) => {
    // Test the benefit selection modal that had scrolling issues
    await signInUser(page, 'user1@techcorp.e2e')
    await page.goto('/dashboard')
    await waitForPageLoad(page)

    // Open the benefit selection modal
    const addBenefitsButton = page.locator('button').filter({ hasText: /Add Benefits/ }).first()
    if (await addBenefitsButton.isVisible()) {
      await addBenefitsButton.click()
      await page.waitForTimeout(2000)

      const modal = page.locator('[role="dialog"]')
      await expect(modal).toBeVisible()

      // Test modal dimensions and scrolling
      const modalBox = await modal.boundingBox()
      if (modalBox) {
        // Modal should not exceed viewport height
        const viewportSize = page.viewportSize()
        if (viewportSize) {
          expect(modalBox.height).toBeLessThanOrEqual(viewportSize.height * 0.95)
          console.log(`✅ Modal height (${modalBox.height}px) fits within viewport (${viewportSize.height}px)`)
        }

        // Test scrolling within modal
        const scrollableContent = modal.locator('.overflow-y-auto, .overflow-auto').first()
        if (await scrollableContent.isVisible()) {
          // Test that content can be scrolled
          const initialScrollTop = await scrollableContent.evaluate(el => el.scrollTop)
          
          // Scroll down
          await scrollableContent.evaluate(el => el.scrollTo(0, 100))
          await page.waitForTimeout(500)
          
          const newScrollTop = await scrollableContent.evaluate(el => el.scrollTop)
          if (newScrollTop > initialScrollTop) {
            console.log('✅ Modal content is scrollable')
          } else {
            console.log('⚠️ Modal content may not be scrollable or has no overflow')
          }

          // Test that all benefits are accessible via scrolling
          const benefitItems = modal.locator('[class*="cursor-pointer"], .benefit-item, [data-testid*="benefit"]')
          const benefitCount = await benefitItems.count()
          
          if (benefitCount > 0) {
            // Scroll to bottom to ensure all items are accessible
            await scrollableContent.evaluate(el => el.scrollTo(0, el.scrollHeight))
            await page.waitForTimeout(500)
            
            const lastBenefit = benefitItems.last()
            const isLastBenefitVisible = await lastBenefit.isVisible()
            
            if (isLastBenefitVisible) {
              console.log(`✅ All ${benefitCount} benefits are accessible via scrolling`)
            } else {
              console.log(`⚠️ Last benefit may not be accessible via scrolling`)
            }
          }
        }
      }

      // Close modal
      const closeButton = modal.locator('button').filter({ hasText: /close|cancel|×/i }).first()
      if (await closeButton.isVisible()) {
        await closeButton.click()
      } else {
        await page.keyboard.press('Escape')
      }
    }
  })

  test('Responsive Layout Quality', async ({ page }) => {
    const viewports = [
      { width: 375, height: 667, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1024, height: 768, name: 'Tablet Landscape' },
      { width: 1440, height: 900, name: 'Desktop' }
    ]

    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      await page.goto('/')
      await waitForPageLoad(page)

      // Test header layout
      const header = page.locator('header')
      await expect(header).toBeVisible()
      
      const headerBox = await header.boundingBox()
      if (headerBox) {
        // Header should not overflow viewport width
        expect(headerBox.width).toBeLessThanOrEqual(viewport.width)
        console.log(`✅ Header fits within ${viewport.name} viewport`)
      }

      // Test main content layout
      const main = page.locator('main, [role="main"]').first()
      if (await main.isVisible()) {
        const mainBox = await main.boundingBox()
        if (mainBox) {
          expect(mainBox.width).toBeLessThanOrEqual(viewport.width)
          console.log(`✅ Main content fits within ${viewport.name} viewport`)
        }
      }

      // Test that interactive elements are properly sized for touch
      if (viewport.width <= 768) { // Mobile/tablet
        const buttons = page.locator('button:visible, a[role="button"]:visible').first()
        if (await buttons.isVisible()) {
          const buttonBox = await buttons.boundingBox()
          if (buttonBox) {
            // WCAG recommends minimum 44x44px touch targets
            const minTouchSize = 44
            if (buttonBox.height >= minTouchSize && buttonBox.width >= minTouchSize) {
              console.log(`✅ Touch targets meet minimum size on ${viewport.name}`)
            } else {
              console.log(`⚠️ Touch targets may be too small on ${viewport.name}: ${buttonBox.width}x${buttonBox.height}`)
            }
          }
        }
      }
    }
  })

  test('Content Overflow and Visibility', async ({ page }) => {
    await page.goto('/')
    await waitForPageLoad(page)

    // Test that text content doesn't overflow containers
    const textElements = page.locator('p, h1, h2, h3, h4, h5, h6, span').filter({ hasText: /.+/ })
    const elementCount = Math.min(await textElements.count(), 10) // Test first 10 elements

    for (let i = 0; i < elementCount; i++) {
      const element = textElements.nth(i)
      if (await element.isVisible()) {
        const elementBox = await element.boundingBox()
        const parent = element.locator('..').first()
        const parentBox = await parent.boundingBox()

        if (elementBox && parentBox) {
          // Text should not overflow its container horizontally
          const overflowsHorizontally = elementBox.x + elementBox.width > parentBox.x + parentBox.width + 5 // 5px tolerance
          
          if (!overflowsHorizontally) {
            console.log(`✅ Text element ${i + 1} fits within container`)
          } else {
            console.log(`⚠️ Text element ${i + 1} may overflow container`)
          }
        }
      }
    }
  })

  test('Visual Error Detection', async ({ page }) => {
    // Test for common visual errors that indicate UI problems
    await page.goto('/')
    await waitForPageLoad(page)

    // Check for elements with zero dimensions (often indicates layout issues)
    const zeroSizeElements = await page.evaluate(() => {
      const elements = document.querySelectorAll('*')
      const zeroSize = []
      
      for (const el of elements) {
        const rect = el.getBoundingClientRect()
        if (rect.width === 0 && rect.height === 0 && el.textContent && el.textContent.trim().length > 0) {
          zeroSize.push({
            tag: el.tagName,
            class: el.className,
            text: el.textContent.substring(0, 50)
          })
        }
      }
      
      return zeroSize.slice(0, 5) // Return first 5 issues
    })

    if (zeroSizeElements.length === 0) {
      console.log('✅ No elements with zero dimensions found')
    } else {
      console.log(`⚠️ Found ${zeroSizeElements.length} elements with zero dimensions:`, zeroSizeElements)
    }

    // Check for overlapping elements that might indicate z-index issues
    const overlappingElements = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('button, a, input, [role="button"]'))
      const overlaps = []
      
      for (let i = 0; i < Math.min(elements.length, 20); i++) {
        const el1 = elements[i] as HTMLElement
        const rect1 = el1.getBoundingClientRect()
        
        if (rect1.width === 0 || rect1.height === 0) {continue}
        
        for (let j = i + 1; j < Math.min(elements.length, 20); j++) {
          const el2 = elements[j] as HTMLElement
          const rect2 = el2.getBoundingClientRect()
          
          if (rect2.width === 0 || rect2.height === 0) {continue}
          
          // Check if rectangles overlap
          const overlap = !(rect1.right < rect2.left || 
                           rect2.right < rect1.left || 
                           rect1.bottom < rect2.top || 
                           rect2.bottom < rect1.top)
          
          if (overlap) {
            overlaps.push({
              el1: { tag: el1.tagName, class: el1.className },
              el2: { tag: el2.tagName, class: el2.className }
            })
          }
        }
      }
      
      return overlaps.slice(0, 3) // Return first 3 overlaps
    })

    if (overlappingElements.length === 0) {
      console.log('✅ No overlapping interactive elements detected')
    } else {
      console.log(`⚠️ Found ${overlappingElements.length} potentially overlapping elements:`, overlappingElements)
    }
  })

  test('Scroll Behavior Quality', async ({ page }) => {
    await page.goto('/')
    await waitForPageLoad(page)

    // Test smooth scrolling behavior
    const initialScrollY = await page.evaluate(() => window.scrollY)
    
    // Scroll down
    await page.evaluate(() => window.scrollTo({ top: 500, behavior: 'smooth' }))
    await page.waitForTimeout(1000) // Wait for smooth scroll to complete
    
    const newScrollY = await page.evaluate(() => window.scrollY)
    
    if (newScrollY > initialScrollY) {
      console.log('✅ Page scrolling works correctly')
    } else {
      console.log('⚠️ Page scrolling may not be working')
    }

    // Test that scroll doesn't break layout
    const bodyWidth = await page.evaluate(() => document.body.scrollWidth)
    const viewportWidth = page.viewportSize()?.width || 1024
    
    if (bodyWidth <= viewportWidth + 20) { // 20px tolerance for scrollbars
      console.log('✅ No horizontal scroll overflow detected')
    } else {
      console.log(`⚠️ Horizontal scroll overflow detected: body width ${bodyWidth}px > viewport ${viewportWidth}px`)
    }
  })

  test('Focus Management and Keyboard Navigation', async ({ page }) => {
    await page.goto('/')
    await waitForPageLoad(page)

    // Test tab navigation
    let focusableElements = 0
    let currentElement = null
    
    // Tab through first 10 focusable elements
    for (let i = 0; i < 10; i++) {
      await page.keyboard.press('Tab')
      await page.waitForTimeout(100)
      
      const focusedElement = await page.evaluate(() => {
        const el = document.activeElement
        return el ? {
          tag: el.tagName,
          type: (el as HTMLInputElement).type || null,
          role: el.getAttribute('role'),
          visible: (el as HTMLElement).offsetWidth > 0 && (el as HTMLElement).offsetHeight > 0
        } : null
      })
      
      if (focusedElement && focusedElement.visible) {
        focusableElements++
        currentElement = focusedElement
      }
    }
    
    if (focusableElements >= 3) {
      console.log(`✅ Keyboard navigation works - found ${focusableElements} focusable elements`)
    } else {
      console.log(`⚠️ Limited keyboard navigation - only ${focusableElements} focusable elements found`)
    }
  })
})
