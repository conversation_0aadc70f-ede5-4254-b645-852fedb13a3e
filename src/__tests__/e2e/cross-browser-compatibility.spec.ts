/**
 * Cross-Browser Compatibility Tests
 * 
 * These tests specifically target Safari and mobile browser compatibility issues
 * that might not be caught by standard functional tests.
 */

import { test, expect, Page, BrowserContext } from '@playwright/test'
import { signInUser, waitForPageLoad } from './auth-helpers'

test.describe('Cross-Browser Compatibility Tests', () => {
  
  test('Safari Filter Dropdown Functionality', async ({ page, browserName }) => {
    // This test specifically targets Safari issues with dropdowns
    await page.goto('/')
    await waitForPageLoad(page)

    // Dismiss cookie banner if present to avoid interference
    try {
      const cookieBanner = page.locator('div[class*="fixed bottom-0"]').filter({ hasText: /privacy|cookie/i })
      if (await cookieBanner.isVisible({ timeout: 2000 })) {
        const acceptButton = cookieBanner.locator('button').filter({ hasText: /accept|ok|dismiss/i }).first()
        if (await acceptButton.isVisible()) {
          await acceptButton.click()
          await page.waitForTimeout(500)
        }
      }
    } catch (e) {
      // Cookie banner handling is optional
    }

    // Test each filter dropdown with correct selectors for div-based dropdowns
    const filterTests = [
      { name: 'Company Size', icon: 'lucide-users', placeholder: 'Select Company Sizes', selector: 'div:has(svg[class*="lucide-users"])' },
      { name: 'Industry', icon: 'lucide-building2', placeholder: 'Select Industries', selector: 'div:has(svg[class*="lucide-building2"])' },
      { name: 'Benefits', icon: 'lucide-funnel', placeholder: 'Select Benefits', selector: 'div:has(svg[class*="lucide-funnel"])' }
    ]

    for (const filter of filterTests) {
      console.log(`Testing ${filter.name} filter on ${browserName}`)

      // Try multiple selector strategies - start with div-based selectors
      let filterContainer = page.locator(filter.selector).first()

      if (!(await filterContainer.isVisible({ timeout: 2000 }))) {
        filterContainer = page.locator('div').filter({ hasText: filter.placeholder }).first()
      }

      if (!(await filterContainer.isVisible({ timeout: 2000 }))) {
        filterContainer = page.locator(`text=${filter.placeholder}`).first()
      }

      if (await filterContainer.isVisible()) {
        // Scroll into view and ensure not covered
        await filterContainer.scrollIntoViewIfNeeded()
        await page.waitForTimeout(300)

        // Force click to avoid interception issues
        await filterContainer.click({ force: true })
        await page.waitForTimeout(500)

        // Verify dropdown opened - use correct selector for SearchableMultiSelect
        const dropdown = page.locator('div[class*="absolute"][class*="z-50"]').first()
        const dropdownVisible = await dropdown.isVisible({ timeout: 3000 })

        console.log(`${filter.name} dropdown visible: ${dropdownVisible}`)

        if (dropdownVisible) {
          // Test search functionality within dropdown
          const searchInput = dropdown.locator('input[placeholder="Search..."]')
          if (await searchInput.isVisible()) {
            await searchInput.fill('test')
            await page.waitForTimeout(300)
            await searchInput.clear()
          }

          // Test selecting an option
          const options = dropdown.locator('div[class*="cursor-pointer"]')
          const optionCount = await options.count()

          if (optionCount > 0) {
            const firstOption = options.first()
            await firstOption.click()
            console.log(`✅ Successfully selected option in ${filter.name} filter`)

            // Verify selection was applied
            await page.waitForTimeout(500)
            const selectedTag = page.locator('span').filter({ hasText: /.+/ }).locator('button')
            if (await selectedTag.count() > 0) {
              // Clear the selection
              await selectedTag.first().click()
              await page.waitForTimeout(300)
            }
          }

          // Close dropdown by clicking outside
          await page.click('body')
          await page.waitForTimeout(300)
          console.log(`✅ ${filter.name} filter test completed successfully`)
        } else {
          console.log(`⚠️ ${filter.name} dropdown did not open`)
        }
      } else {
        console.log(`⚠️ ${filter.name} filter container not found`)
      }
    }
  })

  test('Mobile Touch Event Handling', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip()
    }

    await page.goto('/')
    await waitForPageLoad(page)

    // Test touch events on search input
    const searchInput = page.locator('input[placeholder*="Search for benefits or companies"]')
    await expect(searchInput).toBeVisible()
    
    // Test tap without zoom (iOS Safari issue)
    await searchInput.tap()
    await page.waitForTimeout(300)
    
    // Verify input is focused and no zoom occurred
    const isFocused = await searchInput.evaluate(el => document.activeElement === el)
    expect(isFocused).toBe(true)
    
    // Test typing
    await searchInput.fill('Tech')
    await page.waitForTimeout(500)
    
    // Test touch on filter dropdowns - now with proper role attribute
    const filterButton = page.locator('div[role="button"]').filter({ hasText: 'Select Company Sizes' }).first()
    if (await filterButton.isVisible()) {
      await filterButton.tap()
      await page.waitForTimeout(500)

      // Verify dropdown opened - use correct selector for SearchableMultiSelect
      const dropdown = page.locator('div[class*="absolute"][class*="z-50"]')
      if (await dropdown.first().isVisible({ timeout: 3000 })) {
        // Test scrolling within dropdown
        const scrollableArea = dropdown.locator('div[class*="overflow-y-auto"]').first()
        if (await scrollableArea.isVisible()) {
          // Test touch scrolling
          await scrollableArea.evaluate(el => {
            el.scrollTop = 50
          })
          await page.waitForTimeout(300)

          const scrollTop = await scrollableArea.evaluate(el => el.scrollTop)
          if (scrollTop > 0) {
            console.log('✅ Touch scrolling working in dropdown')
          } else {
            console.log('⚠️ Touch scrolling may not be working or no content to scroll')
          }
        }

        // Close dropdown
        await page.click('body')
        await page.waitForTimeout(300)
      } else {
        console.log('⚠️ Filter dropdown did not open on mobile')
      }
    } else {
      console.log('⚠️ Filter button not found on mobile')
    }
    
    console.log('✅ Mobile touch events working correctly')
  })

  test('Modal Positioning and Scrolling', async ({ page, isMobile }) => {
    await signInUser(page, 'user1@techcorp.e2e')
    await page.goto('/dashboard')
    await waitForPageLoad(page)

    // Try to open a modal (benefit selection)
    const addBenefitsButton = page.locator('button').filter({ hasText: /Add Benefits/ }).first()
    if (await addBenefitsButton.isVisible()) {
      await addBenefitsButton.click()
      await page.waitForTimeout(1000)

      const modal = page.locator('[role="dialog"]')
      await expect(modal).toBeVisible()

      // Test modal positioning
      const modalBox = await modal.boundingBox()
      const viewportSize = page.viewportSize()
      
      if (modalBox && viewportSize) {
        // Modal should fit within viewport
        expect(modalBox.height).toBeLessThanOrEqual(viewportSize.height)
        expect(modalBox.width).toBeLessThanOrEqual(viewportSize.width)
        
        // Modal should be properly centered
        const centerX = modalBox.x + modalBox.width / 2
        const centerY = modalBox.y + modalBox.height / 2
        const viewportCenterX = viewportSize.width / 2
        const viewportCenterY = viewportSize.height / 2
        
        // Allow some tolerance for centering
        expect(Math.abs(centerX - viewportCenterX)).toBeLessThan(50)
        expect(Math.abs(centerY - viewportCenterY)).toBeLessThan(100)
      }

      // Test scrolling within modal
      const scrollableContent = modal.locator('.overflow-y-auto, .overflow-auto').first()
      if (await scrollableContent.isVisible()) {
        // Test scrolling
        await scrollableContent.evaluate(el => el.scrollTo(0, 100))
        await page.waitForTimeout(500)
        
        const scrollTop = await scrollableContent.evaluate(el => el.scrollTop)
        expect(scrollTop).toBeGreaterThan(0)
        
        console.log('✅ Modal scrolling working correctly')
      }

      // Close modal
      const closeButton = modal.locator('button').filter({ hasText: /close|cancel|×/i }).first()
      if (await closeButton.isVisible()) {
        await closeButton.click()
      } else {
        await page.keyboard.press('Escape')
      }
    }
  })

  test('CSS Compatibility and Rendering', async ({ page, browserName }) => {
    await page.goto('/')
    await waitForPageLoad(page)

    // Test that critical elements are properly rendered
    const criticalElements = [
      { selector: 'header', name: 'Header' },
      { selector: 'main', name: 'Main content' },
      { selector: 'form', name: 'Search form' },
      { selector: 'input[type="text"]:visible', name: 'Search input' },
      { selector: 'button:visible', name: 'Buttons' }
    ]

    for (const element of criticalElements) {
      const el = page.locator(element.selector).first()
      if (await el.isVisible({ timeout: 3000 })) {
        // Check that element has proper dimensions
        const box = await el.boundingBox()
        if (box) {
          expect(box.width).toBeGreaterThan(0)
          expect(box.height).toBeGreaterThan(0)
        }
        console.log(`✅ ${element.name} rendered correctly`)
      } else {
        console.log(`⚠️ ${element.name} not visible, skipping`)
      }
    }

    // Test button touch targets on mobile (exclude development tools)
    if (page.viewportSize()?.width && page.viewportSize()!.width <= 768) {
      const buttons = page.locator('button:visible:not([aria-label*="Next.js"]):not([aria-label*="Dev Tools"])')
      const buttonCount = await buttons.count()
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i)
        const box = await button.boundingBox()
        
        if (box) {
          // WCAG 2.1 AA requires minimum 44x44px touch targets
          expect(box.height).toBeGreaterThanOrEqual(44)
          expect(box.width).toBeGreaterThanOrEqual(44)
        }
      }
    }

    console.log(`✅ CSS rendering working correctly on ${browserName}`)
  })

  test('Form Submission Cross-Browser', async ({ page, browserName }) => {
    await page.goto('/')
    await waitForPageLoad(page)

    // Test search form submission
    const searchInput = page.locator('input[placeholder*="Search for benefits or companies"]')
    const searchButton = page.locator('button[type="submit"]').filter({ hasText: /search/i }).first()
    
    await searchInput.fill('Tech')
    
    // Test both Enter key and button click
    await searchInput.press('Enter')
    await waitForPageLoad(page)
    
    // Verify search results
    const results = page.locator('.company-card, [data-testid="company-card"]')
    await expect(results.first()).toBeVisible({ timeout: 10000 })
    
    console.log(`✅ Form submission working correctly on ${browserName}`)
  })

  test('Responsive Design Consistency', async ({ page }) => {
    const viewports = [
      { width: 375, height: 667, name: 'iPhone SE' },
      { width: 414, height: 896, name: 'iPhone 11' },
      { width: 768, height: 1024, name: 'iPad' },
      { width: 1024, height: 768, name: 'iPad Landscape' },
      { width: 1440, height: 900, name: 'Desktop' }
    ]

    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      await page.goto('/')
      await waitForPageLoad(page)

      console.log(`Testing ${viewport.name} (${viewport.width}x${viewport.height})`)

      // Test that header is visible and properly sized
      const header = page.locator('header')
      await expect(header).toBeVisible()
      
      // Test that main content doesn't overflow
      const main = page.locator('main')
      const mainBox = await main.boundingBox()
      
      if (mainBox) {
        expect(mainBox.width).toBeLessThanOrEqual(viewport.width)
      }

      // Test that buttons are properly sized for touch on mobile
      if (viewport.width <= 768) {
        const buttons = page.locator('button:visible').first()
        if (await buttons.isVisible()) {
          const buttonBox = await buttons.boundingBox()
          if (buttonBox) {
            expect(buttonBox.height).toBeGreaterThanOrEqual(44)
          }
        }
      }

      console.log(`✅ ${viewport.name} layout working correctly`)
    }
  })
})
