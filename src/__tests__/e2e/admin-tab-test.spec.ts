import { test, expect } from '@playwright/test'
import { signInAdmin, clearAuth, waitForPageLoad } from './auth-helpers'

test.describe('Admin Tab Navigation', () => {
  test.beforeEach(async ({ page }) => {
    await clearAuth(page)
    await page.waitForTimeout(45000)
  })

  test('Admin Tab Clicking', async ({ page, isMobile }) => {
    // 1. Sign in as admin
    await signInAdmin(page, '<EMAIL>')

    // 2. Navigate to admin page
    await page.goto('/admin')
    await waitForPageLoad(page)

    // 3. Verify admin page loads
    await expect(page.locator('text=Platform Administration')).toBeVisible({ timeout: 15000 })
    expect(page.url()).toContain('/admin')

    // 4. Click Companies tab
    // Use more specific selector to avoid ambiguity with other "Companies" text on page
    if (isMobile) {
      await page.click('button:has-text("Cos")')
    } else {
      // Target the tab button specifically, not other "Companies" text
      await page.click('button[type="button"]:has-text("Companies")')
    }
    await page.waitForTimeout(1000)

    // Verify the tab click worked (should stay on admin page or navigate appropriately)
    expect(page.url()).toMatch(/\/admin/)
  })

  test('Test Admin Tab Without UI Expectations', async ({ page, isMobile }) => {
    // 1. Sign in as admin
    await signInAdmin(page, '<EMAIL>')

    // 2. Navigate to admin page
    await page.goto('/admin')
    await waitForPageLoad(page)

    // 3. Verify admin page loads
    await expect(page.locator('text=Platform Administration')).toBeVisible({ timeout: 10000 })
    expect(page.url()).toContain('/admin')

    // 4. Click Companies tab (mobile vs desktop)
    // Use more specific selector to avoid ambiguity with other "Companies" text on page
    if (isMobile) {
      await page.click('button:has-text("Cos")')
    } else {
      // Target the tab button specifically, not other "Companies" text
      await page.click('button[type="button"]:has-text("Companies")')
    }

    // 5. Wait for any async operations
    await page.waitForTimeout(500)

    // 6. Verify we're still on admin page
    expect(page.url()).toMatch(/\/admin/)

    // 7. Test admin API still works
    const apiTest = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/admin/companies?limit=5')
        return {
          status: response.status,
          success: response.ok
        }
      } catch (error) {
        return { error: error instanceof Error ? error.message : String(error), success: false }
      }
    })

    expect(apiTest.success).toBe(true)
  })
})
