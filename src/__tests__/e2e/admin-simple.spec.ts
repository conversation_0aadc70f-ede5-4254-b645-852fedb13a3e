import { test, expect } from '@playwright/test'
import { signInAdmin, clearAuth, waitForPageLoad } from './auth-helpers'

test.describe('Admin Simple E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await clearAuth(page)
    // Add aggressive delay between tests to prevent rate limiting
    await page.waitForTimeout(45000)
  })

  test('Admin Page Loads and APIs Work', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    console.log('✅ Admin signed in successfully')

    // 2. Navigate to admin page
    await page.goto('/admin')
    await waitForPageLoad(page) // Use helper instead of fixed timeout

    // 3. Verify admin page loads
    await expect(page.locator('text=Platform Administration')).toBeVisible({ timeout: 15000 })
    expect(page.url()).toContain('/admin')
    console.log('✅ Admin page loads successfully')

    // 4. Test all admin APIs work
    const apiTests = await page.evaluate(async () => {
      const results = []

      // Test companies API
      try {
        const companiesResponse = await fetch('/api/admin/companies')
        const companiesData = await companiesResponse.json()
        results.push({
          api: 'companies',
          status: companiesResponse.status,
          count: companiesData.companies?.length || 0,
          success: companiesResponse.ok
        })
      } catch (error) {
        results.push({ api: 'companies', error: error instanceof Error ? error.message : String(error), success: false })
      }

      // Test users API
      try {
        const usersResponse = await fetch('/api/admin/users')
        const usersData = await usersResponse.json()
        results.push({
          api: 'users',
          status: usersResponse.status,
          count: usersData.users?.length || 0,
          success: usersResponse.ok
        })
      } catch (error) {
        results.push({ api: 'users', error: error instanceof Error ? error.message : String(error), success: false })
      }

      // Test benefits API
      try {
        const benefitsResponse = await fetch('/api/admin/benefits')
        const benefitsData = await benefitsResponse.json()
        results.push({
          api: 'benefits',
          status: benefitsResponse.status,
          count: benefitsData.benefits?.length || 0,
          success: benefitsResponse.ok
        })
      } catch (error) {
        results.push({ api: 'benefits', error: error instanceof Error ? error.message : String(error), success: false })
      }

      return results
    })

    // Verify all APIs work
    for (const result of apiTests) {
      expect(result.success).toBe(true)
      expect(result.status).toBe(200)
      expect(result.count).toBeGreaterThan(0)
      console.log(`✅ ${result.api} API: ${result.count} items`)
    }

    // 5. Verify page remains stable
    expect(page.url()).toContain('/admin')
    console.log('✅ Admin page remains stable')

    console.log('🎉 All admin functionality verified successfully!')
  })

  test('Admin Authentication and Authorization', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page, '<EMAIL>')

    // 2. Verify admin authentication
    const authCheck = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/auth/me')
        const data = await response.json()
        return {
          status: response.status,
          user: data.user,
          success: response.ok
        }
      } catch (error) {
        return { error: error instanceof Error ? error.message : String(error), success: false }
      }
    })

    expect(authCheck.success).toBe(true)
    expect(authCheck.status).toBe(200)
    expect(authCheck.user.role).toBe('admin')
    expect(authCheck.user.email).toBe('<EMAIL>')
    console.log('✅ Admin authentication verified')

    // 3. Test admin-only API access
    const adminApiCheck = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/admin/companies')
        return {
          status: response.status,
          success: response.ok
        }
      } catch (error) {
        return { error: error instanceof Error ? error.message : String(error), success: false }
      }
    })

    expect(adminApiCheck.success).toBe(true)
    expect(adminApiCheck.status).toBe(200)
    console.log('✅ Admin API access verified')

    console.log('🎉 Admin authentication and authorization working!')
  })

  test('Admin Page Content and Navigation', async ({ page, isMobile }) => {
    // 1. Sign in as admin
    await signInAdmin(page, '<EMAIL>')

    // 2. Navigate to admin page
    await page.goto('/admin')
    await waitForPageLoad(page)

    // 3. Verify admin page content
    await expect(page.locator('text=Platform Administration')).toBeVisible()
    await expect(page.locator('text=Quick Actions')).toBeVisible()

    // Check for tab navigation elements (mobile vs desktop)
    if (isMobile) {
      // On mobile, check for mobile labels
      await expect(page.locator('text=Home').first()).toBeVisible()
      await expect(page.locator('text=Cos').first()).toBeVisible()
      await expect(page.locator('text=Users').first()).toBeVisible()
      await expect(page.locator('text=Bens').first()).toBeVisible()
    } else {
      // On desktop, check for full labels
      await expect(page.locator('text=Overview').first()).toBeVisible()
      await expect(page.locator('text=Companies').first()).toBeVisible()
      await expect(page.locator('text=Users').first()).toBeVisible()
      await expect(page.locator('text=Benefits').first()).toBeVisible()
    }

    console.log('✅ Admin page content verified')

    // 4. Test that page stays stable without tab clicks
    await page.waitForTimeout(1000)
    expect(page.url()).toContain('/admin')
    console.log('✅ Admin page navigation stable')

    console.log('🎉 Admin page content and navigation working!')
  })
})
