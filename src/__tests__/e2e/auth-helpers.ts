/**
 * E2E Test Authentication Helpers
 * Provides proper authentication flow for e2e tests
 */

import { Page, expect } from '@playwright/test'
import { Pool } from 'pg'

// Database connection for creating unique tokens
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
})

/**
 * Create a unique magic link token for testing
 */
async function createUniqueToken(email: string): Promise<string> {
  // Create a highly unique token with timestamp, random components, worker ID, and process ID
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2)
  const workerId = process.env.PLAYWRIGHT_WORKER_INDEX || '0'
  const processId = process.pid
  const emailHash = email.replace(/[^a-zA-Z0-9]/g, '')
  const uniqueToken = `test-token-${timestamp}-${random}-${workerId}-${processId}-${emailHash}`
  const futureExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now

  // Use a transaction to ensure atomic cleanup and creation
  const client = await pool.connect()
  try {
    await client.query('BEGIN')

    // First, clean up any existing tokens for this email to prevent conflicts
    await client.query(`
      DELETE FROM magic_link_tokens
      WHERE email = $1
    `, [email])

    // Insert the new token
    await client.query(`
      INSERT INTO magic_link_tokens (token, email, expires_at, used_at)
      VALUES ($1, $2, $3, null)
    `, [uniqueToken, email, futureExpiry])

    await client.query('COMMIT')

    // Add a small delay to ensure the transaction is fully committed
    await new Promise(resolve => setTimeout(resolve, 200))

    return uniqueToken
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('Error creating unique token:', error)
    throw error
  } finally {
    client.release()
  }
}

/**
 * Simple and reliable sign in function using magic links
 */
export async function signInUser(page: Page, email: string = 'user1@techcorp.e2e') {
  console.log(`🔐 Signing in user: ${email}`)

  return await safePageOperation(page, async () => {
    // Create a fresh token for this authentication attempt
    const token = await createUniqueToken(email)
    console.log(`🎫 Created token: ${token}`)

    // Go directly to the magic link URL (simulating clicking the email link)
    await page.goto(`/auth/magic-link#${token}`)

    // Give the page time to fully load and start validation in CI environment
    await page.waitForTimeout(2000)

  try {
    // Wait for the page to load and check for different possible states
    await page.waitForLoadState('networkidle', { timeout: 10000 }).catch(() => {
      console.log('Network idle timeout, continuing...')
    })

    // Check for error states first
    const errorElement = page.locator('.bg-red-50, text=Invalid, text=expired').first()
    if (await errorElement.isVisible({ timeout: 2000 }).catch(() => false)) {
      throw new Error('Magic link validation failed - error state detected')
    }

    // Wait for either the confirmation button or validation to complete
    const confirmButton = page.locator('button:has-text("Complete Sign In")').first()

    // Extended wait for CI environment
    await expect(confirmButton).toBeVisible({ timeout: 45000 })

    console.log('✅ Confirmation button found, clicking...')
    await confirmButton.click()

    // Wait for redirect with extended timeout
    await page.waitForURL(/\/(dashboard|admin)/, { timeout: 20000 })
    console.log('✅ User authentication successful')

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.log(`⚠️ Initial authentication failed: ${errorMessage}`)
    console.log('🔄 Creating new token and retrying...')

    // Create a new token and retry once
    const newToken = await createUniqueToken(email)
    console.log(`🎫 Retry token: ${newToken}`)
    await page.goto(`/auth/magic-link#${newToken}`)

    // Wait for retry in CI environment
    await page.waitForTimeout(2000)

    // Wait for page to stabilize
    await page.waitForLoadState('networkidle', { timeout: 10000 }).catch(() => {
      console.log('Network idle timeout on retry, continuing...')
    })

    // Check for error states on retry
    const retryErrorElement = page.locator('.bg-red-50, text=Invalid, text=expired').first()
    if (await retryErrorElement.isVisible({ timeout: 2000 }).catch(() => false)) {
      throw new Error('Magic link validation failed on retry - error state detected')
    }

    const retryConfirmButton = page.locator('button:has-text("Complete Sign In")').first()
    await expect(retryConfirmButton).toBeVisible({ timeout: 45000 })

    console.log('✅ Retry confirmation button found, clicking...')
    await retryConfirmButton.click()

    await page.waitForURL(/\/(dashboard|admin)/, { timeout: 20000 })
    console.log('✅ User authentication successful on retry')
  }
  }, 'user sign in')
}

/**
 * Simple and reliable admin sign in function using magic links
 */
export async function signInAdmin(page: Page, email: string = '<EMAIL>') {
  console.log(`🔐 Signing in admin: ${email}`)

  return await safePageOperation(page, async () => {
    // Create a fresh token for this authentication attempt
    const token = await createUniqueToken(email)
    console.log(`🎫 Created admin token: ${token}`)

    // Go directly to the magic link URL with fragment (simulating clicking the email link)
    await page.goto(`/auth/magic-link#${token}`)

    // Give the page more time to fully load and start validation in CI environment
    await page.waitForTimeout(3000)

  try {
    // Wait for the page to load and check for different possible states
    await page.waitForLoadState('networkidle', { timeout: 10000 }).catch(() => {
      console.log('Admin auth: Network idle timeout, continuing...')
    })

    // Check for error states first
    const errorElement = page.locator('.bg-red-50, text=Invalid, text=expired').first()
    if (await errorElement.isVisible({ timeout: 2000 }).catch(() => false)) {
      throw new Error('Admin magic link validation failed - error state detected')
    }

    // Wait for either the confirmation button or validation to complete
    const confirmButton = page.locator('button:has-text("Complete Sign In")').first()

    // Extended wait for CI environment
    await expect(confirmButton).toBeVisible({ timeout: 45000 })

    console.log('✅ Admin confirmation button found, clicking...')
    await confirmButton.click()

    // Wait for redirect with extended timeout
    await page.waitForURL(/\/(admin|dashboard)/, { timeout: 20000 })
    console.log('✅ Admin authentication successful')

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.log(`⚠️ Initial admin authentication failed: ${errorMessage}`)
    console.log('🔄 Creating new admin token and retrying...')

    // Create a new token and retry once
    const newToken = await createUniqueToken(email)
    console.log(`🎫 Admin retry token: ${newToken}`)
    await page.goto(`/auth/magic-link#${newToken}`)

    // Wait for retry in CI environment
    await page.waitForTimeout(2000)

    // Wait for page to stabilize
    await page.waitForLoadState('networkidle', { timeout: 10000 }).catch(() => {
      console.log('Admin retry: Network idle timeout, continuing...')
    })

    // Check for error states on retry
    const retryErrorElement = page.locator('.bg-red-50, text=Invalid, text=expired').first()
    if (await retryErrorElement.isVisible({ timeout: 2000 }).catch(() => false)) {
      throw new Error('Admin magic link validation failed on retry - error state detected')
    }

    const retryConfirmButton = page.locator('button:has-text("Complete Sign In")').first()
    await expect(retryConfirmButton).toBeVisible({ timeout: 45000 })

    console.log('✅ Admin retry confirmation button found, clicking...')
    await retryConfirmButton.click()

    await page.waitForURL(/\/(admin|dashboard)/, { timeout: 20000 })
    console.log('✅ Admin authentication successful on retry')
  }
  }, 'admin sign in')
}



/**
 * Authenticate using a magic link token via the API
 */
async function authenticateWithToken(page: Page, token: string) {
  // Call the magic link verification API directly
  const response = await page.evaluate(async (authToken) => {
    const res = await fetch('/api/auth/magic-link', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token: authToken }),
    })
    
    return {
      ok: res.ok,
      status: res.status,
      data: await res.json()
    }
  }, token)
  
  if (!response.ok) {
    throw new Error(`Authentication failed: ${response.status} - ${JSON.stringify(response.data)}`)
  }
  
  // Wait a moment for the session to be established
  await page.waitForTimeout(1000)
}

/**
 * Sign up a new user using the proper API flow
 */
export async function signUpUser(page: Page, email: string, firstName: string, lastName: string) {
  // Navigate to sign-up page
  await page.goto('/sign-up')

  // Fill in the form
  await page.fill('input[name="email"]', email)
  await page.fill('input[name="firstName"]', firstName)
  await page.fill('input[name="lastName"]', lastName)
  await page.click('button[type="submit"]')

  // Wait for the magic link sent message
  await page.waitForSelector('text=Magic link sent', { timeout: 10000 })

  // For testing, we'll create a sign-up token and authenticate directly
  // In a real scenario, this would be handled by clicking the email link
  const signUpToken = `mock-signup-${email.replace('@', '-').replace('.', '-')}`

  // Create the sign-up token in the database via direct API call
  await page.evaluate(async (tokenData) => {
    // Create a magic link token for sign-up with user data
    const response = await fetch('/api/test/create-signup-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(tokenData),
    })

    if (!response.ok) {
      console.warn('Failed to create sign-up token, using fallback')
    }
  }, { token: signUpToken, email, firstName, lastName })

  // Authenticate with the sign-up token
  await authenticateWithToken(page, signUpToken)

  // Wait for redirect to dashboard
  await page.waitForURL(/\/dashboard/, { timeout: 10000 })
}

/**
 * Wait for page to fully load with optimized strategy
 */
export async function waitForPageLoad(page: Page) {
  try {
    // Check if page is still valid before proceeding
    if (page.isClosed()) {
      throw new Error('Page is closed')
    }

    // Wait for network to be mostly idle instead of fixed timeout
    await page.waitForLoadState('domcontentloaded')
    try {
      await page.waitForLoadState('networkidle', { timeout: 10000 })
    } catch (error) {
      // If networkidle times out, fall back to a longer wait for stability
      console.log('Network idle timeout, falling back to basic wait')
      await page.waitForTimeout(2000)
    }
  } catch (error) {
    console.log('⚠️ Page load error:', error)
    throw error
  }
}

/**
 * Safe page operation wrapper that handles closed page contexts
 */
export async function safePageOperation<T>(
  page: Page,
  operation: () => Promise<T>,
  operationName: string
): Promise<T> {
  try {
    if (page.isClosed()) {
      throw new Error(`Page is closed before ${operationName}`)
    }
    return await operation()
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    if (errorMessage.includes('Target page, context or browser has been closed')) {
      throw new Error(`Page context closed during ${operationName}`)
    }
    throw error
  }
}

/**
 * Clear all authentication state
 */
export async function clearAuth(page: Page) {
  await page.context().clearCookies()

  // Navigate to a page first to ensure we have access to localStorage
  try {
    await page.goto('/')
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })
  } catch (error) {
    // If localStorage access fails, just clear cookies (which is the main auth mechanism)
    console.warn('Could not clear localStorage/sessionStorage:', error)
  }
}
