/**
 * Admin Stats E2E Tests
 * Tests that the admin overview counters work correctly
 */

import { test, expect } from '@playwright/test'
import { signInUser, waitForPageLoad } from './auth-helpers'

test.describe('Admin Stats Tests', () => {
  test('Admin overview counters should display correct values', async ({ page }) => {
    // Sign in as admin
    await signInUser(page, '<EMAIL>')
    
    // Navigate to admin page
    await page.goto('/admin')
    await waitForPageLoad(page)
    
    // Wait for the overview tab to be active (it should be by default)
    // Check if we can see the overview content instead of checking tab styling
    await expect(page.locator('text=Total Companies')).toBeVisible({ timeout: 10000 })
    
    // Wait for stats to load and check that they are not 0
    await page.waitForTimeout(2000) // Give time for API call to complete
    
    // Check that the counter cards are visible
    const companiesCard = page.locator('text=Total Companies').locator('..').locator('p').nth(1)
    const usersCard = page.locator('text=Total Users').locator('..').locator('p').nth(1)
    const benefitsCard = page.locator('text=Total Benefits').locator('..').locator('p').nth(1)
    
    await expect(companiesCard).toBeVisible()
    await expect(usersCard).toBeVisible()
    await expect(benefitsCard).toBeVisible()
    
    // Get the actual values
    const companiesCount = await companiesCard.textContent()
    const usersCount = await usersCard.textContent()
    const benefitsCount = await benefitsCard.textContent()
    
    console.log('Admin stats counters:')
    console.log(`- Companies: ${companiesCount}`)
    console.log(`- Users: ${usersCount}`)
    console.log(`- Benefits: ${benefitsCount}`)
    
    // Verify that the counts are numbers and greater than 0
    expect(parseInt(companiesCount || '0')).toBeGreaterThan(0)
    expect(parseInt(usersCount || '0')).toBeGreaterThan(0)
    expect(parseInt(benefitsCount || '0')).toBeGreaterThan(0)
    
    console.log('✅ Admin overview counters are working correctly')
  })

  test('Admin stats API endpoint should return correct data', async ({ page }) => {
    // Sign in as admin first to get authentication
    await signInUser(page, '<EMAIL>')
    
    // Test the API endpoint directly
    const response = await page.request.get('/api/admin/stats')
    
    expect(response.status()).toBe(200)
    
    const data = await response.json()
    
    // Check that the response has the expected structure
    expect(data).toHaveProperty('totalCompanies')
    expect(data).toHaveProperty('totalUsers')
    expect(data).toHaveProperty('totalBenefits')
    
    // Check that the values are numbers and greater than 0
    expect(typeof data.totalCompanies).toBe('number')
    expect(typeof data.totalUsers).toBe('number')
    expect(typeof data.totalBenefits).toBe('number')
    
    expect(data.totalCompanies).toBeGreaterThan(0)
    expect(data.totalUsers).toBeGreaterThan(0)
    expect(data.totalBenefits).toBeGreaterThan(0)
    
    console.log('✅ Admin stats API endpoint is working correctly')
    console.log('API response:', data)
  })

  test('Admin stats should update when data changes', async ({ page }) => {
    // Sign in as admin
    await signInUser(page, '<EMAIL>')
    
    // Navigate to admin page
    await page.goto('/admin')
    await waitForPageLoad(page)
    
    // Get initial stats
    await page.waitForTimeout(2000)
    const initialCompaniesCount = await page.locator('text=Total Companies').locator('..').locator('p').nth(1).textContent()

    // Verify the stats are reasonable numbers (greater than 0)
    const companiesNum = parseInt(initialCompaniesCount || '0')
    expect(companiesNum).toBeGreaterThan(0)

    // Verify other stats are also present
    const usersCount = await page.locator('text=Total Users').locator('..').locator('p').nth(1).textContent()
    const benefitsCount = await page.locator('text=Total Benefits').locator('..').locator('p').nth(1).textContent()

    const usersNum = parseInt(usersCount || '0')
    const benefitsNum = parseInt(benefitsCount || '0')

    expect(usersNum).toBeGreaterThan(0)
    expect(benefitsNum).toBeGreaterThan(0)

    console.log(`✅ Admin stats verified - Companies: ${companiesNum}, Users: ${usersNum}, Benefits: ${benefitsNum}`)
  })
})
