/**
 * Mobile Functionality E2E Tests
 * Comprehensive tests for mobile-specific features and responsive design
 */

import { test, expect } from '@playwright/test'
import { waitForPageLoad } from './auth-helpers'

test.describe('Mobile Functionality Tests', () => {
  test('Mobile Navigation Menu', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip()
    }

    await page.goto('/')
    await waitForPageLoad(page)

    // Find and click mobile menu button
    const mobileMenuButton = page.locator('button[aria-label="Toggle mobile menu"]')
    await expect(mobileMenuButton).toBeVisible()
    
    // Test menu toggle
    await mobileMenuButton.click()
    
    // Check that mobile menu is visible
    const mobileMenu = page.locator('.lg\\:hidden').filter({ hasText: 'Companies' })
    await expect(mobileMenu).toBeVisible()
    
    // Test navigation links in mobile menu
    const navLinks = [
      mobileMenu.locator('text=Companies'),
      mobileMenu.locator('text=Benefits'),
      mobileMenu.locator('text=About')
    ]

    for (const link of navLinks) {
      await expect(link).toBeVisible()
    }
    
    // Test closing menu
    await mobileMenuButton.click()
    await expect(mobileMenu).not.toBeVisible()
    
    console.log('✅ Mobile navigation menu working correctly')
  })

  test('Touch Interactions', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip()
    }

    await page.goto('/')
    await waitForPageLoad(page)

    // Test touch scrolling
    await page.evaluate(() => {
      window.scrollTo(0, 100)
    })
    
    // Test touch input on search
    const searchInput = page.locator('input[placeholder*="Search for benefits or companies"]')
    await searchInput.tap()
    await searchInput.fill('E2E Tech Corp')

    // Test touch on buttons
    await searchInput.press('Enter')
    await waitForPageLoad(page)

    // Verify search results
    const results = page.locator('.company-card')
    await expect(results.first()).toBeVisible({ timeout: 10000 })
    
    console.log('✅ Touch interactions working correctly')
  })

  test('Mobile Layout and Responsive Design', async ({ page }) => {
    // Test different mobile viewport sizes
    const viewports = [
      { width: 375, height: 667, name: 'iPhone SE' },
      { width: 414, height: 896, name: 'iPhone 11' },
      { width: 360, height: 640, name: 'Android Small' }
    ]

    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      await page.goto('/')
      await waitForPageLoad(page)
      
      // Check that content is visible and properly laid out
      const header = page.locator('header')
      await expect(header).toBeVisible()
      
      const searchInput = page.locator('input[placeholder*="Search for benefits or companies"]')
      await expect(searchInput).toBeVisible()
      
      // Check that mobile menu button is visible on small screens
      const mobileMenuButton = page.locator('button[aria-label="Toggle mobile menu"]')
      await expect(mobileMenuButton).toBeVisible()
      
      console.log(`✅ Layout working correctly on ${viewport.name} (${viewport.width}x${viewport.height})`)
    }
  })

  test('Mobile Form Interactions', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip()
    }

    await page.goto('/sign-in')
    await waitForPageLoad(page)

    // Test mobile form input
    const emailInput = page.locator('input[type="email"]')
    if (await emailInput.isVisible()) {
      await emailInput.tap()
      await emailInput.fill('<EMAIL>')
      
      // Check that virtual keyboard doesn't break layout
      const submitButton = page.locator('button[type="submit"]')
      await expect(submitButton).toBeVisible()
      
      console.log('✅ Mobile form interactions working correctly')
    }
  })

  test('Mobile Performance and Loading', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip()
    }

    const startTime = Date.now()
    await page.goto('/')
    await waitForPageLoad(page)
    const loadTime = Date.now() - startTime
    
    // Mobile should load within reasonable time (6 seconds for mobile)
    expect(loadTime).toBeLessThan(6000)
    
    // Test that images load properly on mobile
    const images = page.locator('img')
    const imageCount = await images.count()
    
    if (imageCount > 0) {
      // Check first few images
      for (let i = 0; i < Math.min(imageCount, 3); i++) {
        const img = images.nth(i)
        if (await img.isVisible()) {
          await expect(img).toHaveAttribute('src')
        }
      }
      console.log(`✅ ${Math.min(imageCount, 3)} images loading correctly on mobile`)
    }
    
    console.log(`✅ Mobile page loaded in ${loadTime}ms`)
  })

  test('Mobile Search and Filtering', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip()
    }

    await page.goto('/')
    await waitForPageLoad(page)

    // Test mobile search
    const searchInput = page.locator('input[placeholder*="Search for benefits or companies"]')
    await searchInput.tap()
    await searchInput.fill('E2E Tech Corp')
    await searchInput.press('Enter')
    await waitForPageLoad(page)

    // Verify search results display properly on mobile
    const results = page.locator('.company-card')
    await expect(results.first()).toBeVisible({ timeout: 10000 })
    
    // Test mobile filtering (if available)
    const filterButtons = page.locator('button').filter({ hasText: /filter|Filter/ })
    if (await filterButtons.count() > 0) {
      const firstFilter = filterButtons.first()
      if (await firstFilter.isVisible()) {
        await firstFilter.tap()
        console.log('✅ Mobile filtering interactions working')
      }
    }
    
    console.log('✅ Mobile search and filtering working correctly')
  })

  test('Mobile Company Profile View', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip()
    }

    await page.goto('/')
    await waitForPageLoad(page)

    // Search for a company
    const searchInput = page.locator('input[placeholder*="Search for benefits or companies"]')
    await searchInput.fill('E2E Tech Corp')
    await searchInput.press('Enter')
    await waitForPageLoad(page)

    // Click on first company result
    const firstResult = page.locator('.company-card').first()
    await expect(firstResult).toBeVisible({ timeout: 10000 })
    await firstResult.tap()
    await waitForPageLoad(page)
    
    // Check that company profile displays properly on mobile
    const companyName = page.locator('h1')
    await expect(companyName).toBeVisible()
    
    // Check that action buttons are properly sized for mobile
    const actionButtons = page.locator('button').filter({ hasText: /Apply|Save|apply|save/ })
    if (await actionButtons.count() > 0) {
      const firstButton = actionButtons.first()
      if (await firstButton.isVisible()) {
        const boundingBox = await firstButton.boundingBox()
        if (boundingBox) {
          expect(boundingBox.height).toBeGreaterThanOrEqual(44) // Touch target size
        }
      }
    }
    
    console.log('✅ Mobile company profile view working correctly')
  })

  test('Mobile Orientation Changes', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip()
    }

    // Test portrait orientation
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/')
    await waitForPageLoad(page)
    
    const searchInputPortrait = page.locator('input[placeholder*="Search for benefits or companies"]')
    await expect(searchInputPortrait).toBeVisible()
    
    // Test landscape orientation
    await page.setViewportSize({ width: 667, height: 375 })
    await page.waitForTimeout(1000) // Allow layout to adjust
    
    const searchInputLandscape = page.locator('input[placeholder*="Search for benefits or companies"]')
    await expect(searchInputLandscape).toBeVisible()
    
    // Test that mobile menu still works in landscape
    const mobileMenuButton = page.locator('button[aria-label="Toggle mobile menu"]')
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.tap()
      const mobileMenu = page.locator('.lg\\:hidden').filter({ hasText: 'Companies' })
      await expect(mobileMenu).toBeVisible()
    }
    
    console.log('✅ Mobile orientation changes handled correctly')
  })

  test('Mobile Accessibility Features', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip()
    }

    await page.goto('/')
    await waitForPageLoad(page)

    // Test that primary interactive elements meet minimum size requirements
    const primaryButtons = page.locator('button:not(.icon-only-small), a[role="button"]:not(.icon-only-small), input[type="submit"]')
    const elementCount = await primaryButtons.count()

    let validTouchTargets = 0
    for (let i = 0; i < Math.min(elementCount, 5); i++) {
      const element = primaryButtons.nth(i)
      if (await element.isVisible()) {
        const boundingBox = await element.boundingBox()
        if (boundingBox) {
          // WCAG 2.1 AA requires minimum 44x44px touch targets for primary interactive elements
          if (boundingBox.height >= 44 && boundingBox.width >= 44) {
            validTouchTargets++
          }
        }
      }
    }

    // At least some primary buttons should meet touch target requirements
    expect(validTouchTargets).toBeGreaterThan(0)
    
    console.log('✅ Mobile accessibility features verified')
  })
})
