/**
 * User Company Interaction Flow Tests
 * Tests for company search, saving companies, reporting missing companies, and company profile interactions
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock fetch for API calls
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock database operations
const mockQuery = vi.fn()
vi.mock('@/lib/local-db', () => ({
  query: mockQuery,
}))

describe('User Company Interaction Flows', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(mockFetch).mockClear()
    mockQuery.mockReset()
  })

  describe('Company Search and Discovery', () => {
    it('should search companies by name', async () => {
      const mockCompanies = [
        {
          id: 'company-1',
          name: 'Tech Corp',
          location: 'San Francisco, CA',
          industry: 'Technology',
          size: '1000-5000',
          benefits: ['Health Insurance', 'Remote Work']
        },
        {
          id: 'company-2',
          name: 'Tech Solutions',
          location: 'New York, NY',
          industry: 'Technology',
          size: '100-500',
          benefits: ['Health Insurance', 'Flexible Hours']
        }
      ]

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          companies: mockCompanies,
          total: 2,
          page: 1,
          limit: 20
        })
      })

      const response = await fetch('/api/companies?search=tech&page=1&limit=20')

      const result = await response.json()

      expect(result.companies).toHaveLength(2)
      expect(result.companies[0].name).toBe('Tech Corp')
      expect(result.companies[1].name).toBe('Tech Solutions')
      expect(result.total).toBe(2)
    })

    it('should filter companies by location', async () => {
      const mockCompanies = [
        {
          id: 'company-1',
          name: 'SF Tech',
          location: 'San Francisco, CA',
          industry: 'Technology'
        }
      ]

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          companies: mockCompanies,
          total: 1
        })
      })

      const response = await fetch('/api/companies?location=San Francisco')

      const result = await response.json()

      expect(result.companies).toHaveLength(1)
      expect(result.companies[0].location).toBe('San Francisco, CA')
    })

    it('should filter companies by benefits', async () => {
      const mockCompanies = [
        {
          id: 'company-1',
          name: 'Remote First Corp',
          benefits: ['Remote Work', 'Health Insurance']
        }
      ]

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          companies: mockCompanies,
          total: 1
        })
      })

      const response = await fetch('/api/companies?benefits=Remote Work')

      const result = await response.json()

      expect(result.companies).toHaveLength(1)
      expect(result.companies[0].benefits).toContain('Remote Work')
    })

    it('should handle empty search results', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          companies: [],
          total: 0,
          page: 1,
          limit: 20
        })
      })

      const response = await fetch('/api/companies?search=nonexistent')

      const result = await response.json()

      expect(result.companies).toHaveLength(0)
      expect(result.total).toBe(0)
    })
  })

  describe('Company Profile Viewing', () => {
    it('should get company profile details', async () => {
      const mockCompany = {
        id: 'company-123',
        name: 'Tech Corp',
        description: 'Leading technology company',
        location: 'San Francisco, CA',
        industry: 'Technology',
        size: '1000-5000',
        website: 'https://techcorp.com',
        benefits: [
          {
            id: 'benefit-1',
            name: 'Health Insurance',
            category: 'Health',
            isVerified: true
          },
          {
            id: 'benefit-2',
            name: 'Remote Work',
            category: 'Flexibility',
            isVerified: false
          }
        ],
        analytics: {
          totalViews: 1500,
          monthlyViews: 200
        }
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockCompany
      })

      const response = await fetch('/api/companies/company-123')

      const result = await response.json()

      expect(result.id).toBe('company-123')
      expect(result.name).toBe('Tech Corp')
      expect(result.benefits).toHaveLength(2)
      expect(result.analytics.totalViews).toBe(1500)
    })

    it('should handle non-existent company', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ 
          error: 'Company not found' 
        })
      })

      const response = await fetch('/api/companies/nonexistent-company')

      const result = await response.json()

      expect(response.status).toBe(404)
      expect(result.error).toBe('Company not found')
    })
  })

  describe('Saved Companies Management', () => {
    it('should save a company to user favorites', async () => {
      const saveData = {
        companyId: 'company-123'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          savedCompany: {
            id: 'saved-123',
            userId: 'user-123',
            companyId: 'company-123',
            savedAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/user/saved-companies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(saveData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.savedCompany.companyId).toBe('company-123')
    })

    it('should get user saved companies', async () => {
      const mockSavedCompanies = [
        {
          id: 'saved-1',
          company: {
            id: 'company-1',
            name: 'Tech Corp',
            location: 'San Francisco, CA'
          },
          savedAt: '2024-01-01T12:00:00Z'
        },
        {
          id: 'saved-2',
          company: {
            id: 'company-2',
            name: 'Design Studio',
            location: 'New York, NY'
          },
          savedAt: '2024-01-02T12:00:00Z'
        }
      ]

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          savedCompanies: mockSavedCompanies
        })
      })

      const response = await fetch('/api/user/saved-companies')

      const result = await response.json()

      expect(result.savedCompanies).toHaveLength(2)
      expect(result.savedCompanies[0].company.name).toBe('Tech Corp')
      expect(result.savedCompanies[1].company.name).toBe('Design Studio')
    })

    it('should delete saved company', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Company removed from saved list'
        })
      })

      const response = await fetch('/api/user/saved-companies/company-123', {
        method: 'DELETE'
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toBe('Company removed from saved list')
    })

    it('should handle duplicate save attempt', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: async () => ({ 
          error: 'Company already saved' 
        })
      })

      const response = await fetch('/api/user/saved-companies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ companyId: 'company-123' })
      })

      const result = await response.json()

      expect(response.status).toBe(409)
      expect(result.error).toBe('Company already saved')
    })
  })

  describe('Missing Company Reporting', () => {
    it('should report missing company', async () => {
      const reportData = {
        companyName: 'New Startup Inc',
        website: 'https://newstartup.com',
        location: 'Austin, TX',
        industry: 'Technology',
        description: 'Innovative AI startup',
        contactEmail: '<EMAIL>'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          report: {
            id: 'report-123',
            ...reportData,
            status: 'pending',
            submittedAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/missing-companies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(reportData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.report.companyName).toBe('New Startup Inc')
      expect(result.report.status).toBe('pending')
    })

    it('should validate required fields for missing company report', async () => {
      const invalidReportData = {
        companyName: '',
        website: 'invalid-url'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ 
          error: 'Company name is required' 
        })
      })

      const response = await fetch('/api/missing-companies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidReportData)
      })

      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe('Company name is required')
    })

    it('should get user submitted missing company reports', async () => {
      const mockReports = [
        {
          id: 'report-1',
          companyName: 'Startup A',
          status: 'approved',
          submittedAt: '2024-01-01T12:00:00Z'
        },
        {
          id: 'report-2',
          companyName: 'Startup B',
          status: 'pending',
          submittedAt: '2024-01-02T12:00:00Z'
        }
      ]

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          reports: mockReports
        })
      })

      const response = await fetch('/api/user/missing-company-reports')

      const result = await response.json()

      expect(result.reports).toHaveLength(2)
      expect(result.reports[0].status).toBe('approved')
      expect(result.reports[1].status).toBe('pending')
    })
  })
})
