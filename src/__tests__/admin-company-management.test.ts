/**
 * Admin Company Management Flow Tests
 * Tests for admin company CRUD operations, company verification, and company data management
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock fetch for API calls
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock database operations
const mockQuery = vi.fn()
vi.mock('@/lib/local-db', () => ({
  query: mockQuery,
}))

describe('Admin Company Management Flows', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(mockFetch).mockClear()
    mockQuery.mockReset()
  })

  describe('Company Creation', () => {
    it('should create a new company', async () => {
      const companyData = {
        name: 'New Tech Corp',
        description: 'Innovative technology company',
        website: 'https://newtechcorp.com',
        industry: 'Technology',
        size: '100-500',
        location: 'San Francisco, CA',
        benefits: ['Health Insurance', 'Remote Work']
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          company: {
            id: 'company-123',
            ...companyData,
            createdAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/admin/companies', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(companyData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.company.name).toBe('New Tech Corp')
    })

    it('should validate required fields for company creation', async () => {
      const invalidCompanyData = {
        name: '',
        website: 'invalid-url'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ 
          error: 'Company name is required' 
        })
      })

      const response = await fetch('/api/admin/companies', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(invalidCompanyData)
      })

      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe('Company name is required')
    })

    it('should handle duplicate company creation', async () => {
      const companyData = {
        name: 'Existing Corp',
        website: 'https://existing.com'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: async () => ({ 
          error: 'Company with this name already exists' 
        })
      })

      const response = await fetch('/api/admin/companies', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(companyData)
      })

      const result = await response.json()

      expect(response.status).toBe(409)
      expect(result.error).toBe('Company with this name already exists')
    })
  })

  describe('Company Updates', () => {
    it('should update company information', async () => {
      const updateData = {
        name: 'Updated Tech Corp',
        description: 'Updated description',
        industry: 'Software',
        size: '500-1000'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          company: {
            id: 'company-123',
            ...updateData,
            updatedAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/admin/companies/company-123', {
        method: 'PATCH',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(updateData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.company.name).toBe('Updated Tech Corp')
      expect(result.company.industry).toBe('Software')
    })



    it('should handle non-existent company update', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ 
          error: 'Company not found' 
        })
      })

      const response = await fetch('/api/admin/companies/nonexistent', {
        method: 'PATCH',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ name: 'Updated Name' })
      })

      const result = await response.json()

      expect(response.status).toBe(404)
      expect(result.error).toBe('Company not found')
    })
  })

  describe('Company Deletion', () => {
    it('should delete company', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Company deleted successfully'
        })
      })

      const response = await fetch('/api/admin/companies/company-123', {
        method: 'DELETE',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toBe('Company deleted successfully')
    })

    it('should handle deletion of company with dependencies', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: async () => ({ 
          error: 'Cannot delete company with existing user associations',
          dependencies: {
            users: 5,
            savedBy: 12
          }
        })
      })

      const response = await fetch('/api/admin/companies/company-123', {
        method: 'DELETE',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(response.status).toBe(409)
      expect(result.error).toContain('Cannot delete company')
      expect(result.dependencies.users).toBe(5)
    })

    it('should force delete company with dependencies', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Company force deleted successfully',
          cleanupActions: [
            'Removed 5 user associations',
            'Removed 12 saved company entries',
            'Archived 3 benefit verifications'
          ]
        })
      })

      const response = await fetch('/api/admin/companies/company-123?force=true', {
        method: 'DELETE',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.cleanupActions).toHaveLength(3)
    })
  })

  describe('Company Discovery and Notification', () => {
    it('should run discover and notify for matching domain', async () => {
      const discoveryData = {
        domain: 'techcorp.com',
        companyId: 'company-123'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          discovery: {
            domain: 'techcorp.com',
            matchedUsers: [
              {
                id: 'user-1',
                email: '<EMAIL>',
                notified: true
              },
              {
                id: 'user-2',
                email: '<EMAIL>',
                notified: true
              }
            ],
            totalNotified: 2
          }
        })
      })

      const response = await fetch('/api/admin/companies/company-123/discover-notify', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(discoveryData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.discovery.matchedUsers).toHaveLength(2)
      expect(result.discovery.totalNotified).toBe(2)
    })

    it('should handle discovery with no matching users', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          discovery: {
            domain: 'newcompany.com',
            matchedUsers: [],
            totalNotified: 0,
            message: 'No users found with matching email domain'
          }
        })
      })

      const response = await fetch('/api/admin/companies/company-123/discover-notify', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ domain: 'newcompany.com' })
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.discovery.matchedUsers).toHaveLength(0)
      expect(result.discovery.message).toContain('No users found')
    })
  })

  describe('Company Analytics and Reporting', () => {
    it('should get company analytics dashboard', async () => {
      const mockAnalytics = {
        totalCompanies: 150,
        verifiedCompanies: 120,
        pendingVerification: 30,
        recentActivity: [
          {
            type: 'company_created',
            companyName: 'New Startup',
            timestamp: '2024-01-01T12:00:00Z'
          },
          {
            type: 'company_verified',
            companyName: 'Tech Corp',
            timestamp: '2024-01-01T11:00:00Z'
          }
        ],
        topIndustries: [
          { industry: 'Technology', count: 45 },
          { industry: 'Healthcare', count: 32 }
        ]
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockAnalytics
      })

      const response = await fetch('/api/admin/companies/analytics', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.totalCompanies).toBe(150)
      expect(result.verifiedCompanies).toBe(120)
      expect(result.recentActivity).toHaveLength(2)
      expect(result.topIndustries).toHaveLength(2)
    })

    it('should export company data', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          exportUrl: '/api/admin/exports/companies-2024-01-01.csv',
          recordCount: 150
        })
      })

      const response = await fetch('/api/admin/companies/export', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ format: 'csv', includeAnalytics: true })
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.exportUrl).toContain('.csv')
      expect(result.recordCount).toBe(150)
    })
  })

  describe('Authorization and Security', () => {
    it('should require admin authorization', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ 
          error: 'Authentication required' 
        })
      })

      const response = await fetch('/api/admin/companies')

      const result = await response.json()

      expect(response.status).toBe(401)
      expect(result.error).toBe('Authentication required')
    })

    it('should require admin role', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 403,
        json: async () => ({ 
          error: 'Admin access required' 
        })
      })

      const response = await fetch('/api/admin/companies', {
        headers: { 
          'Authorization': 'Bearer user-token'
        }
      })

      const result = await response.json()

      expect(response.status).toBe(403)
      expect(result.error).toBe('Admin access required')
    })
  })
})
