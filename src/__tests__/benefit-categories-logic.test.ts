import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock database operations
const mockQuery = vi.fn()
vi.mock('@/lib/local-db', () => ({
  query: mockQuery,
}))

const mockGetBenefitCategories = vi.fn()
vi.mock('@/lib/database', () => ({
  getBenefitCategories: mockGetBenefitCategories,
}))

describe('Benefit Categories Logic', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return categories that have benefits when includeEmpty is false', async () => {
    // Mock the getBenefitCategories function to return categories with benefits
    mockGetBenefitCategories.mockResolvedValueOnce([
      {
        id: 'category-1',
        name: 'health',
        display_name: 'Health & Wellness',
        description: 'Health benefits',
        icon: '🏥',
        sort_order: 1,
        benefit_count: 5
      },
      {
        id: 'category-2',
        name: 'financial',
        display_name: 'Financial Benefits',
        description: 'Financial benefits',
        icon: '💰',
        sort_order: 2,
        benefit_count: 3
      }
    ])

    const categories = await mockGetBenefitCategories(false)

    expect(categories).toHaveLength(2)
    expect(categories[0].benefit_count).toBeGreaterThan(0)
    expect(categories[1].benefit_count).toBeGreaterThan(0)
  })

  it('should return all categories when includeEmpty is true', async () => {
    // Mock the getBenefitCategories function to return all categories including empty ones
    mockGetBenefitCategories.mockResolvedValueOnce([
      {
        id: 'category-1',
        name: 'health',
        display_name: 'Health & Wellness',
        description: 'Health benefits',
        icon: '🏥',
        sort_order: 1,
        benefit_count: 5
      },
      {
        id: 'category-2',
        name: 'financial',
        display_name: 'Financial Benefits',
        description: 'Financial benefits',
        icon: '💰',
        sort_order: 2,
        benefit_count: 3
      },
      {
        id: 'category-3',
        name: 'empty',
        display_name: 'Empty Category',
        description: 'Category with no benefits',
        icon: '📭',
        sort_order: 3,
        benefit_count: 0
      }
    ])

    const categories = await mockGetBenefitCategories(true)

    expect(categories).toHaveLength(3)

    // Should include empty category
    const emptyCategory = categories.find((cat: any) => cat.benefit_count === 0)
    expect(emptyCategory).toBeDefined()
    expect(emptyCategory.display_name).toBe('Empty Category')
  })

  it('should include benefit_count in the returned data', async () => {
    // Mock the getBenefitCategories function to return categories with benefit_count
    mockGetBenefitCategories.mockResolvedValueOnce([
      {
        id: 'category-1',
        name: 'health',
        display_name: 'Health & Wellness',
        description: 'Health benefits',
        icon: '🏥',
        sort_order: 1,
        benefit_count: 5
      }
    ])

    const categories = await mockGetBenefitCategories(true)
    const testCategory = categories[0]

    expect(testCategory).toBeDefined()
    expect(testCategory.benefit_count).toBeDefined()
    expect(typeof testCategory.benefit_count).toBe('number')
    expect(testCategory.benefit_count).toBe(5)
  })

  it('should order categories by sort_order and display_name', async () => {
    // Mock the getBenefitCategories function to return ordered categories
    mockGetBenefitCategories.mockResolvedValueOnce([
      {
        id: 'category-1',
        name: 'health',
        display_name: 'Health & Wellness',
        description: 'Health benefits',
        icon: '🏥',
        sort_order: 1,
        benefit_count: 5
      },
      {
        id: 'category-2',
        name: 'financial',
        display_name: 'Financial Benefits',
        description: 'Financial benefits',
        icon: '💰',
        sort_order: 2,
        benefit_count: 3
      },
      {
        id: 'category-3',
        name: 'wellness',
        display_name: 'Wellness Programs',
        description: 'Wellness benefits',
        icon: '🧘',
        sort_order: 2,
        benefit_count: 2
      }
    ])

    const categories = await mockGetBenefitCategories(true)

    // Check that categories are ordered
    expect(categories[0].sort_order).toBe(1)
    expect(categories[1].sort_order).toBe(2)
    expect(categories[2].sort_order).toBe(2)

    // When sort_order is the same, should be ordered by display_name
    expect(categories[1].display_name).toBe('Financial Benefits')
    expect(categories[2].display_name).toBe('Wellness Programs')
    expect(categories[1].display_name.localeCompare(categories[2].display_name)).toBeLessThan(0)
  })
})
