/**
 * API Error Logging Tests
 * Tests that API errors are logged appropriately based on log level
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { NextRequest } from 'next/server'
import { logAPIError, handleAPIError } from '@/lib/api-error-handler'
import { APIAccessDeniedError } from '@/lib/api-access-control'

// Mock the logger
vi.mock('@/lib/logger', () => ({
  logger: {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }
}))

describe('API Error Logging', () => {
  let mockRequest: NextRequest
  let originalLogLevel: string | undefined

  beforeEach(() => {
    // Create a mock request
    mockRequest = new NextRequest('http://localhost:3000/api/test', {
      method: 'GET',
      headers: {
        'user-agent': 'Mozilla/5.0 (Test Browser)',
        'x-forwarded-for': '127.0.0.1'
      }
    })

    // Store original log level
    originalLogLevel = process.env.LOG_LEVEL
  })

  afterEach(() => {
    // Restore original log level
    if (originalLogLevel !== undefined) {
      process.env.LOG_LEVEL = originalLogLevel
    } else {
      delete process.env.LOG_LEVEL
    }
    
    vi.clearAllMocks()
  })

  describe('logAPIError', () => {
    it('should log API access denied errors at debug level', async () => {
      const { logger } = await import('@/lib/logger')
      const error = new APIAccessDeniedError()

      logAPIError(error, mockRequest)

      expect(logger.debug).toHaveBeenCalledWith('API access denied', expect.objectContaining({
        url: '/api/test',
        method: 'GET',
        errorType: 'APIAccessDeniedError'
      }))
      expect(logger.error).not.toHaveBeenCalled()
    })

    it('should log authentication errors at info level', async () => {
      const { logger } = await import('@/lib/logger')
      const error = new Error('Authentication required')

      logAPIError(error, mockRequest)

      expect(logger.info).toHaveBeenCalledWith('API authorization error', expect.objectContaining({
        url: '/api/test',
        method: 'GET',
        error: 'Authentication required'
      }))
    })

    it('should log server errors at error level', async () => {
      const { logger } = await import('@/lib/logger')
      const error = new Error('Database connection failed')

      logAPIError(error, mockRequest)

      expect(logger.error).toHaveBeenCalledWith('API server error', expect.objectContaining({
        url: '/api/test',
        method: 'GET',
        errorType: 'Error'
      }))
    })

    it('should truncate user agent when not in debug mode', async () => {
      process.env.LOG_LEVEL = 'warn'
      const { logger } = await import('@/lib/logger')
      const error = new Error('Test error')

      logAPIError(error, mockRequest)

      expect(logger.error).toHaveBeenCalledWith('API server error', expect.objectContaining({
        userAgent: 'Mozilla/5.0 (Test Browser)...'
      }))
    })
  })

  describe('handleAPIError', () => {
    it('should return 403 for API access denied errors', () => {
      const error = new APIAccessDeniedError()
      const response = handleAPIError(error, mockRequest)

      expect(response.status).toBe(403)
    })

    it('should return 401 for authentication errors', () => {
      const error = new Error('Authentication required')
      const response = handleAPIError(error, mockRequest)

      expect(response.status).toBe(401)
    })

    it('should return 403 for admin access errors', () => {
      const error = new Error('Admin access required')
      const response = handleAPIError(error, mockRequest)

      expect(response.status).toBe(403)
    })

    it('should return 500 for other errors', () => {
      const error = new Error('Database error')
      const response = handleAPIError(error, mockRequest)

      expect(response.status).toBe(500)
    })
  })

  describe('Log Level Behavior', () => {
    it('should include stack traces only in debug mode for API access errors', async () => {
      // Test with warn level (should not include stack)
      process.env.LOG_LEVEL = 'warn'
      const { logger } = await import('@/lib/logger')
      const error = new APIAccessDeniedError()
      error.stack = 'Error stack trace...'

      logAPIError(error, mockRequest)

      const debugCall = (logger.debug as any).mock.calls[0]
      expect(debugCall[1]).not.toHaveProperty('error')
    })

    it('should include full context in debug mode', async () => {
      process.env.LOG_LEVEL = 'debug'
      const { logger } = await import('@/lib/logger')
      const error = new Error('Test error')

      logAPIError(error, mockRequest)

      const errorCall = (logger.error as any).mock.calls[0]
      expect(errorCall[1]).toHaveProperty('userAgent', 'Mozilla/5.0 (Test Browser)')
    })
  })
})
