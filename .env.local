# Local Development Database (PostgreSQL in Docker)
DATABASE_URL=postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
POSTGRES_USER=benefitlens_user
POSTGRES_PASSWORD=benefitlens_password
POSTGRES_DB=benefitlens

# Local Authentication
USE_LOCAL_AUTH=true
SESSION_SECRET=your_random_session_secret_here

# Cache Configuration (PostgreSQL-based)
CACHE_TYPE=postgresql

# Email Configuration
# For development (MailHog)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=<EMAIL>
SMTP_PASS=your_mailgun_api_key_here
FROM_EMAIL=<EMAIL>

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
APP_URL=http://localhost:3000
NODE_ENV=development
LOG_LEVEL=warn