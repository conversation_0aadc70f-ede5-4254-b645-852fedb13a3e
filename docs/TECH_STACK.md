# BenefitLens Technology Stack Documentation

This document provides a comprehensive overview of all technologies, frameworks, and libraries used in the BenefitLens application, with links to their official documentation.

## 🚀 Core Framework & Runtime

### **Next.js 15** - React Framework
- **Purpose**: Full-stack React framework providing SSR, routing, and API routes
- **Version**: 15.4.6
- **Official Documentation**: https://nextjs.org/docs
- **Key Features Used**: App Router, API Routes, Server Components, Standalone output for Docker

### **React 19** - UI Library  
- **Purpose**: Frontend user interface library
- **Version**: 19.1.1
- **Official Documentation**: https://react.dev/
- **Key Features Used**: Hooks, Components, JSX

### **Node.js 20** - JavaScript Runtime
- **Purpose**: Server-side JavaScript runtime
- **Version**: 20 (Alpine Linux in Docker)
- **Official Documentation**: https://nodejs.org/docs/

### **TypeScript** - Type Safety
- **Purpose**: Static type checking for JavaScript
- **Version**: 5.9.2
- **Official Documentation**: https://www.typescriptlang.org/docs/
- **Configuration**: Strict mode enabled, path aliases configured

## 🎨 Styling & UI Components

### **Tailwind CSS 4** - Utility-First CSS Framework
- **Purpose**: Rapid UI development with utility classes
- **Version**: 4.x
- **Official Documentation**: https://tailwindcss.com/docs
- **Configuration**: Custom color scheme, responsive design

### **Radix UI** - Accessible UI Primitives
- **Purpose**: Unstyled, accessible UI components
- **Components Used**: Dialog, Dropdown Menu, Select
- **Official Documentation**: https://www.radix-ui.com/primitives/docs/overview/introduction
- **Versions**:
  - @radix-ui/react-dialog: ^1.1.14
  - @radix-ui/react-dropdown-menu: ^2.1.15
  - @radix-ui/react-select: ^2.2.5

### **Lucide React** - Icon Library
- **Purpose**: Beautiful, customizable SVG icons
- **Version**: 0.539.0
- **Official Documentation**: https://lucide.dev/guide/
- **Usage**: Consistent iconography throughout the application

### **PostCSS** - CSS Processing
- **Purpose**: CSS transformation and optimization
- **Official Documentation**: https://postcss.org/docs/
- **Plugins**: @tailwindcss/postcss

## 🗄️ Database & Backend

### **PostgreSQL** - Primary Database
- **Purpose**: Relational database for all application data
- **Version**: 15 (Docker image)
- **Official Documentation**: https://www.postgresql.org/docs/
- **Key Features**: JSONB support, materialized views, custom functions

### **pg** - PostgreSQL Client
- **Purpose**: Node.js PostgreSQL client library
- **Version**: 8.16.3
- **Official Documentation**: https://node-postgres.com/
- **Usage**: Database connections, queries, transactions

### **bcryptjs** - Password Hashing
- **Purpose**: Secure password hashing
- **Version**: 3.0.2
- **Official Documentation**: https://github.com/dcodeIO/bcrypt.js#readme
- **Usage**: Magic link token security

## 📧 Email & Communication

### **Nodemailer** - Email Sending
- **Purpose**: Email delivery for magic link authentication
- **Version**: 7.0.5
- **Official Documentation**: https://nodemailer.com/about/
- **Usage**: Magic link emails, notifications

## 🧪 Testing Framework

### **Vitest** - Unit Testing Framework
- **Purpose**: Fast unit testing with Vite
- **Version**: 3.2.4
- **Official Documentation**: https://vitest.dev/guide/
- **Features**: Coverage reporting, mocking, TypeScript support

### **Playwright** - End-to-End Testing
- **Purpose**: Cross-browser E2E testing
- **Version**: 1.55.0
- **Official Documentation**: https://playwright.dev/docs/intro
- **Browsers**: Chromium, Firefox, WebKit (Safari)

### **Testing Library** - React Testing Utilities
- **Purpose**: Simple and complete testing utilities for React
- **Versions**:
  - @testing-library/react: ^16.3.0
  - @testing-library/jest-dom: ^6.6.4
  - @testing-library/user-event: ^14.6.1
- **Official Documentation**: https://testing-library.com/docs/

### **jsdom** - DOM Simulation
- **Purpose**: DOM implementation for Node.js testing
- **Version**: 26.1.0
- **Official Documentation**: https://github.com/jsdom/jsdom#readme

## 🛠️ Development Tools

### **ESLint** - Code Linting
- **Purpose**: Code quality and consistency
- **Version**: 9.x
- **Official Documentation**: https://eslint.org/docs/latest/
- **Configuration**: Next.js recommended rules, TypeScript support

### **npm** - Package Manager
- **Purpose**: Dependency management and script running
- **Official Documentation**: https://docs.npmjs.com/
- **Lock File**: package-lock.json for reproducible builds

### **Turbopack** - Build Tool
- **Purpose**: Fast bundler for Next.js development
- **Official Documentation**: https://turbo.build/pack/docs
- **Usage**: `npm run dev` uses Turbopack for faster builds

## 🐳 Deployment & Infrastructure

### **Docker** - Containerization
- **Purpose**: Application containerization and deployment
- **Official Documentation**: https://docs.docker.com/
- **Images**: Node.js 20 Alpine, PostgreSQL 15, Playwright

### **Docker Compose** - Multi-Container Orchestration
- **Purpose**: Local development and production deployment
- **Official Documentation**: https://docs.docker.com/compose/
- **Services**: App, PostgreSQL, Adminer, MailHog

## 📦 Utility Libraries

### **@hello-pangea/dnd** - Drag and Drop
- **Purpose**: Beautiful drag and drop for React
- **Version**: 18.0.1
- **Official Documentation**: https://github.com/hello-pangea/dnd
- **Usage**: Benefit ranking functionality

### **country-state-city** - Location Data
- **Purpose**: Comprehensive location database
- **Version**: 3.2.1
- **Official Documentation**: https://github.com/harpreetkhalsagtbit/country-state-city
- **Usage**: Company location management

### **uuid** - Unique Identifiers
- **Purpose**: RFC4122 UUID generation
- **Version**: 11.1.0
- **Official Documentation**: https://github.com/uuidjs/uuid#readme

### **csv-parser** - CSV Processing
- **Purpose**: Streaming CSV parser
- **Version**: 3.2.0
- **Official Documentation**: https://github.com/mafintosh/csv-parser#readme
- **Usage**: Data import functionality

### **class-variance-authority** - CSS Utilities
- **Purpose**: Type-safe CSS class variants
- **Version**: 0.7.1
- **Official Documentation**: https://cva.style/docs

### **clsx** - Conditional Classes
- **Purpose**: Utility for constructing className strings
- **Version**: 2.1.1
- **Official Documentation**: https://github.com/lukeed/clsx#readme

### **tailwind-merge** - Tailwind Class Merging
- **Purpose**: Merge Tailwind CSS classes without conflicts
- **Version**: 3.3.1
- **Official Documentation**: https://github.com/dcastil/tailwind-merge

## 🔧 Configuration Files

- **next.config.js**: Next.js configuration with Docker optimization
- **tsconfig.json**: TypeScript compiler configuration
- **eslint.config.mjs**: ESLint rules and settings
- **vitest.config.ts**: Unit test configuration
- **playwright.config.ts**: E2E test configuration
- **docker-compose.yml**: Local development services
- **Dockerfile**: Production container build

## 🚀 Getting Started

To explore these technologies further:

1. **Start with Next.js**: https://nextjs.org/learn - Comprehensive tutorial
2. **Learn React**: https://react.dev/learn - Official React tutorial  
3. **Master TypeScript**: https://www.typescriptlang.org/docs/handbook/intro.html
4. **Understand Tailwind**: https://tailwindcss.com/docs/installation
5. **Database with PostgreSQL**: https://www.postgresql.org/docs/tutorial/
6. **Testing with Vitest**: https://vitest.dev/guide/
7. **E2E with Playwright**: https://playwright.dev/docs/intro

## 📚 Additional Resources

- **Next.js Examples**: https://github.com/vercel/next.js/tree/canary/examples
- **React Patterns**: https://react.dev/reference/react
- **TypeScript Handbook**: https://www.typescriptlang.org/docs/
- **Tailwind Components**: https://tailwindui.com/
- **PostgreSQL Tutorial**: https://www.postgresqltutorial.com/
