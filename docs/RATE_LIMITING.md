# Rate Limiting Configuration

BenefitLens includes comprehensive rate limiting to protect against abuse and ensure fair usage. This document explains how rate limiting works and how to configure it.

## Overview

Rate limiting is implemented at multiple levels:

1. **Middleware Level** - General API route protection
2. **PostgreSQL Level** - Database-backed rate limiting with sliding windows
3. **Authentication Level** - Magic link request limiting
4. **Feature-Specific Level** - Company verification emails, etc.

## Rate Limiting Layers

### 1. Middleware Rate Limiting (`src/middleware.ts`)
- **Scope**: All API routes (`/api/*`)
- **Default Limit**: 100 requests per minute per IP
- **Storage**: In-memory (Edge Runtime compatible)

### 2. PostgreSQL Rate Limiting (`src/lib/postgresql-rate-limit.ts`)
- **Scope**: Specific API endpoints and user actions
- **Storage**: PostgreSQL database
- **Features**: Sliding window, per-user limits, configurable windows

### 3. Authentication Rate Limiting (`src/lib/magic-link-auth.ts`)
- **Scope**: Magic link sign-in/sign-up requests
- **Default Limit**: 5 requests per hour per email
- **Storage**: PostgreSQL (`magic_link_rate_limits` table)

### 4. Feature-Specific Rate Limiting
- **Company Verification Emails**: 3 emails per 24 hours per user
- **Admin Operations**: 200 requests per minute
- **Search Operations**: 50 requests per minute

## Default Rate Limits

```typescript
const RATE_LIMITS = {
  API_GENERAL: {
    windowMs: 60 * 1000,     // 1 minute
    maxRequests: 100,
  },
  AUTH_MAGIC_LINK: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5,
  },
  ADMIN_API: {
    windowMs: 60 * 1000,     // 1 minute
    maxRequests: 200,
  },
  SEARCH_API: {
    windowMs: 60 * 1000,     // 1 minute
    maxRequests: 50,
  },
  COMPANY_MUTATIONS: {
    windowMs: 60 * 1000,     // 1 minute
    maxRequests: 10,
  },
}
```

## Disabling Rate Limiting for Testing

### Environment Variable

Set the `DISABLE_RATE_LIMITING` environment variable to `true` to disable all rate limiting:

```bash
# .env.local
DISABLE_RATE_LIMITING=true
```

**⚠️ WARNING**: Only disable rate limiting in test environments! Never disable it in production.

### Automatic Test Configuration

Rate limiting is automatically disabled for:

- **E2E Tests**: Set in `src/__tests__/e2e/global-setup.ts`
- **Integration Tests**: Set in `vitest.integration.config.ts`

### Manual Testing

For manual testing or development, you can temporarily disable rate limiting:

```bash
# Start development server with rate limiting disabled
DISABLE_RATE_LIMITING=true npm run dev
```

## Testing Rate Limiting

### Testing with Rate Limiting Enabled

To test rate limiting behavior:

1. Remove or set `DISABLE_RATE_LIMITING=false`
2. Make repeated requests to trigger limits
3. Verify 429 responses are returned

### Testing with Rate Limiting Disabled

For faster test execution:

1. Set `DISABLE_RATE_LIMITING=true`
2. Run tests without rate limiting delays
3. Focus on functional testing rather than rate limiting behavior

## Rate Limiting in Different Environments

### Development
- Rate limiting is typically disabled for faster development
- Can be enabled to test rate limiting behavior

### Testing
- **Unit Tests**: Rate limiting not applicable (mocked dependencies)
- **Integration Tests**: Rate limiting disabled by default
- **E2E Tests**: Rate limiting disabled by default

### Production
- Rate limiting is always enabled
- Never set `DISABLE_RATE_LIMITING=true` in production

## Monitoring Rate Limiting

### Database Tables

Rate limiting data is stored in:
- `rate_limits` - General rate limiting records
- `magic_link_rate_limits` - Authentication rate limiting

### Cleanup

Rate limiting records are automatically cleaned up:
- Expired records are removed during rate limit checks
- Use `cleanupExpiredRateLimits()` for manual cleanup

## Troubleshooting

### Common Issues

1. **Tests timing out due to rate limiting**
   - Solution: Ensure `DISABLE_RATE_LIMITING=true` in test environment

2. **Rate limiting not working in development**
   - Check if `DISABLE_RATE_LIMITING=true` is set
   - Verify middleware is properly configured

3. **429 errors in legitimate usage**
   - Review rate limits for specific endpoints
   - Consider increasing limits for heavy usage scenarios

### Debugging

Enable rate limiting debugging:

```typescript
// Add to rate limiting functions
console.log('Rate limit check:', { identifier, allowed, remaining })
```

## Configuration Examples

### Stricter Rate Limiting

```typescript
const STRICT_RATE_LIMITS = {
  API_GENERAL: {
    windowMs: 60 * 1000,     // 1 minute
    maxRequests: 30,         // Reduced from 100
  },
  AUTH_MAGIC_LINK: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,          // Reduced from 5
  },
}
```

### More Permissive Rate Limiting

```typescript
const PERMISSIVE_RATE_LIMITS = {
  API_GENERAL: {
    windowMs: 60 * 1000,     // 1 minute
    maxRequests: 500,        // Increased from 100
  },
  ADMIN_API: {
    windowMs: 60 * 1000,     // 1 minute
    maxRequests: 1000,       // Increased from 200
  },
}
```

## Best Practices

1. **Always test with rate limiting disabled** for functional tests
2. **Test rate limiting behavior separately** with dedicated tests
3. **Monitor rate limiting metrics** in production
4. **Adjust limits based on actual usage patterns**
5. **Never disable rate limiting in production**
6. **Use different limits for different user types** (free vs. paying users)
