# Analytics API Guide for Paying Customers

This guide explains how paying customers can access BenefitLens analytics data through our APIs.

## Overview

BenefitLens provides comprehensive analytics APIs for paying customers to integrate benefit insights into their own systems, dashboards, and applications.

## Access Levels

- **Free Users**: Demo data with limited insights
- **Paying Customers**: Full access to real analytics data
- **Unauthenticated Users**: Preview data with upgrade prompts

## Authentication

### Method 1: Session-Based Authentication (Web Users)

1. **Sign in** to your BenefitLens account
2. **Extract session token** from browser cookies
3. **Use in API requests**

```bash
# Get session token from browser console
document.cookie.split(';').find(c => c.trim().startsWith('session_token='))
```

### Method 2: Magic Link Authentication

```bash
# Request magic link for your account
curl -X POST https://benefitlens.de/api/auth/magic-link \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>"}'

# Click the magic link, then extract session token
```

## Available Analytics Endpoints

### 1. Overview Analytics
Get high-level benefit statistics and trends.

```bash
GET /api/analytics/overview
```

**Response Example:**
```json
{
  "totalCompanies": 1250,
  "totalBenefits": 45,
  "averageBenefitsPerCompany": 8.2,
  "topBenefits": [
    {"name": "Health Insurance", "count": 1100, "percentage": 88},
    {"name": "Remote Work", "count": 950, "percentage": 76},
    {"name": "Flexible Hours", "count": 800, "percentage": 64}
  ],
  "industryDistribution": [
    {"industry": "Technology", "count": 320, "percentage": 25.6},
    {"industry": "Finance", "count": 180, "percentage": 14.4}
  ]
}
```

### 2. Benefit Analytics
Detailed insights about specific benefits.

```bash
GET /api/analytics/benefits
GET /api/analytics/benefits?benefit=Remote%20Work
```

**Response Example:**
```json
{
  "benefitName": "Remote Work",
  "totalCompanies": 950,
  "adoptionRate": 76.0,
  "industryBreakdown": [
    {"industry": "Technology", "count": 280, "percentage": 87.5},
    {"industry": "Marketing", "count": 120, "percentage": 75.0}
  ],
  "companySizeBreakdown": [
    {"size": "Large (1000+)", "count": 400, "percentage": 85.0},
    {"size": "Medium (100-999)", "count": 350, "percentage": 70.0}
  ],
  "trends": {
    "monthlyGrowth": 5.2,
    "yearOverYear": 45.8
  }
}
```

### 3. Industry Analytics
Industry-specific benefit insights.

```bash
GET /api/analytics/industries
GET /api/analytics/industries?industry=Technology
```

**Response Example:**
```json
{
  "industry": "Technology",
  "totalCompanies": 320,
  "averageBenefits": 12.5,
  "topBenefits": [
    {"name": "Remote Work", "count": 280, "percentage": 87.5},
    {"name": "Stock Options", "count": 240, "percentage": 75.0}
  ],
  "uniqueBenefits": [
    "Sabbatical Leave",
    "Conference Budget",
    "Home Office Stipend"
  ],
  "benchmarking": {
    "aboveAverage": ["Remote Work", "Learning Budget"],
    "belowAverage": ["Company Car", "Meal Vouchers"]
  }
}
```

### 4. Company Size Analytics
Benefit patterns by company size.

```bash
GET /api/analytics/company-sizes
GET /api/analytics/company-sizes?size=Large
```

### 5. Location Analytics
Geographic benefit distribution.

```bash
GET /api/analytics/locations
GET /api/analytics/locations?location=Berlin
```

### 6. Trend Analytics
Historical benefit adoption trends.

```bash
GET /api/analytics/trends
GET /api/analytics/trends?period=12months
```

## Example Implementations

### Python Integration
```python
import requests
import pandas as pd

class BenefitLensAnalytics:
    def __init__(self, session_token):
        self.session_token = session_token
        self.base_url = 'https://benefitlens.de/api/analytics'
        self.headers = {'Cookie': f'session_token={session_token}'}
    
    def get_overview(self):
        response = requests.get(f'{self.base_url}/overview', headers=self.headers)
        return response.json()
    
    def get_benefit_insights(self, benefit_name):
        params = {'benefit': benefit_name}
        response = requests.get(f'{self.base_url}/benefits', 
                              headers=self.headers, params=params)
        return response.json()
    
    def get_industry_comparison(self, industry):
        params = {'industry': industry}
        response = requests.get(f'{self.base_url}/industries', 
                              headers=self.headers, params=params)
        return response.json()

# Usage
analytics = BenefitLensAnalytics('your_session_token')

# Get overview
overview = analytics.get_overview()
print(f"Total companies: {overview['totalCompanies']}")

# Analyze specific benefit
remote_work = analytics.get_benefit_insights('Remote Work')
print(f"Remote work adoption: {remote_work['adoptionRate']}%")

# Industry comparison
tech_insights = analytics.get_industry_comparison('Technology')
print(f"Tech companies average benefits: {tech_insights['averageBenefits']}")
```

### JavaScript/React Integration
```javascript
import axios from 'axios';

class BenefitLensAPI {
  constructor(sessionToken) {
    this.api = axios.create({
      baseURL: 'https://benefitlens.de/api/analytics',
      headers: {
        'Cookie': `session_token=${sessionToken}`
      }
    });
  }

  async getOverview() {
    const response = await this.api.get('/overview');
    return response.data;
  }

  async getBenefitTrends(benefit) {
    const response = await this.api.get('/benefits', {
      params: { benefit }
    });
    return response.data;
  }

  async getIndustryInsights(industry) {
    const response = await this.api.get('/industries', {
      params: { industry }
    });
    return response.data;
  }
}

// React component example
function AnalyticsDashboard() {
  const [analytics, setAnalytics] = useState(null);
  const api = new BenefitLensAPI(sessionToken);

  useEffect(() => {
    api.getOverview().then(setAnalytics);
  }, []);

  return (
    <div>
      <h2>Benefit Analytics</h2>
      {analytics && (
        <div>
          <p>Total Companies: {analytics.totalCompanies}</p>
          <p>Average Benefits: {analytics.averageBenefitsPerCompany}</p>
        </div>
      )}
    </div>
  );
}
```

## Rate Limits and Best Practices

### Rate Limits
- **100 requests per minute** for authenticated users
- **10 requests per minute** for unauthenticated users (demo data only)

### Best Practices
1. **Cache responses** - Analytics data doesn't change frequently
2. **Batch requests** - Combine multiple queries when possible
3. **Use appropriate time periods** - Don't request real-time data unnecessarily
4. **Handle errors gracefully** - Implement retry logic with exponential backoff

### Error Handling
```python
import time
import random

def make_api_request_with_retry(url, headers, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 429:  # Rate limited
                wait_time = (2 ** attempt) + random.uniform(0, 1)
                time.sleep(wait_time)
                continue
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            if attempt == max_retries - 1:
                raise e
            time.sleep(2 ** attempt)
    
    raise Exception("Max retries exceeded")
```

## Support and Documentation

- **API Documentation**: https://benefitlens.de/docs/api
- **Support Email**: <EMAIL>
- **Status Page**: https://status.benefitlens.de

## Pricing and Limits

- **Starter Plan**: 1,000 API calls/month
- **Professional Plan**: 10,000 API calls/month
- **Enterprise Plan**: Unlimited API calls

Contact <EMAIL> for custom enterprise solutions.
