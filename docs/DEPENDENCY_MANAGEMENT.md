# Dependency Management Guide

This guide explains how to manage and update dependencies in the BenefitLens project.

## Quick Commands

The following npm scripts are available for dependency management:

```bash
# Check for outdated dependencies
npm run deps:check

# Update dependencies within semver ranges
npm run deps:update

# Update to latest versions (including major version bumps)
npm run deps:update-major
```

## Understanding Dependency Updates

### 1. Check for Outdated Dependencies

```bash
npm run deps:check
# or directly:
npm outdated
```

This command shows which packages have newer versions available. The output includes:
- **Current**: Currently installed version
- **Wanted**: Maximum version satisfying semver range in package.json
- **Latest**: Latest published version

### 2. Safe Updates (Recommended)

```bash
npm run deps:update
# or directly:
npm update
```

This updates dependencies to the latest versions that satisfy the semver ranges specified in package.json. This is the safest approach as it respects:
- `^1.2.3` - Updates to latest 1.x.x (no breaking changes)
- `~1.2.3` - Updates to latest 1.2.x (no new features)
- `1.2.3` - No updates (exact version)

### 3. Major Version Updates (Use with <PERSON>aut<PERSON>)

```bash
npm run deps:update-major
# or directly:
npx npm-check-updates -u && npm install
```

This updates all dependencies to their latest versions, including major version bumps that may contain breaking changes.

**⚠️ Warning**: Always test thoroughly after major updates as they may introduce breaking changes.

## Best Practices

### Before Updating Dependencies

1. **Backup your current state**:
   ```bash
   git add . && git commit -m "Backup before dependency updates"
   ```

2. **Check current test status**:
   ```bash
   npm run test:full
   ```

3. **Review the changelog** of packages you're updating, especially for major version bumps.

### Update Process

1. **Start with safe updates**:
   ```bash
   npm run deps:update
   ```

2. **Test after safe updates**:
   ```bash
   npm run test:full
   npm run build
   ```

3. **For major updates, do them selectively**:
   ```bash
   # Update specific packages
   npx npm-check-updates -u package-name
   npm install
   ```

4. **Test after each major update**:
   ```bash
   npm run test:full
   npm run build
   npm run dev  # Test locally
   ```

### After Updates

1. **Run comprehensive tests**:
   ```bash
   npm run test:full
   npm run test:e2e
   ```

2. **Test in development environment**:
   ```bash
   npm run dev:container:reset
   npm run dev:container:start
   ```

3. **Check for any deprecation warnings**:
   ```bash
   npm audit
   ```

## Handling Specific Scenarios

### Security Vulnerabilities

```bash
# Check for security issues
npm audit

# Fix automatically fixable issues
npm audit fix

# Force fix (may introduce breaking changes)
npm audit fix --force
```

### Peer Dependency Warnings

If you see peer dependency warnings:

1. Check if the warning affects functionality
2. Install missing peer dependencies manually:
   ```bash
   npm install peer-dependency-name
   ```
3. Or update the package that requires the peer dependency

### Lock File Issues

If you encounter package-lock.json conflicts:

```bash
# Delete lock file and node_modules
rm package-lock.json
rm -rf node_modules

# Reinstall
npm install
```

## Development Dependencies vs Production Dependencies

- **Production dependencies** (`dependencies`): Required for the app to run
- **Development dependencies** (`devDependencies`): Only needed for development/testing

Update them separately if needed:

```bash
# Update only production dependencies
npm update --production

# Update only development dependencies
npm update --dev
```

## Monitoring Dependencies

### Regular Maintenance Schedule

- **Weekly**: Run `npm run deps:check` to stay aware of available updates
- **Monthly**: Apply safe updates with `npm run deps:update`
- **Quarterly**: Review and selectively apply major updates

### Automated Monitoring

Consider setting up automated dependency monitoring:

1. **GitHub Dependabot**: Already configured (see `docs/DEPENDABOT_SETUP.md`)
2. **npm audit**: Run regularly to check for security issues

## Troubleshooting

### Permission Issues (EACCES errors)

If you encounter permission errors like:
```
npm error code EACCES
npm error syscall rename
npm error path /home/<USER>/project/node_modules/.bin/eslint
```

**Solution 1: Fix permissions (Recommended)**
```bash
# Fix permissions for the current project
sudo chown -R $(whoami) node_modules/
sudo chmod -R 755 node_modules/

# Then try the update again
npm run deps:update
```

**Solution 2: Clean install**
```bash
# Remove node_modules and package-lock.json
rm -rf node_modules/
rm package-lock.json

# Reinstall with proper permissions
npm install

# Then update
npm run deps:update
```

**Solution 3: Use sudo (Not recommended for security reasons)**
```bash
sudo npm run deps:update
```

**Solution 4: Fix npm permissions globally (One-time setup)**
```bash
# Create a directory for global packages
mkdir ~/.npm-global

# Configure npm to use the new directory
npm config set prefix '~/.npm-global'

# Add to your shell profile (~/.bashrc, ~/.zshrc, etc.)
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# Then try updating again
npm run deps:update
```

### Common Issues

1. **Build failures after updates**:
   - Check for breaking changes in updated packages
   - Review TypeScript errors for type changes
   - Update import statements if package exports changed

2. **Test failures**:
   - Update test dependencies if needed
   - Check for changes in testing library APIs
   - Update test configurations

3. **Runtime errors**:
   - Check browser console for errors
   - Verify all peer dependencies are installed
   - Check for changes in package APIs

### Rollback Strategy

If updates cause issues:

```bash
# Revert to previous commit
git reset --hard HEAD~1

# Or restore specific files
git checkout HEAD~1 -- package.json package-lock.json
npm install
```

## Related Documentation

- [Local Development](LOCAL_DEVELOPMENT.md) - Setting up development environment
- [Testing](TESTING.md) - Running tests after updates
- [Deployment](DEPLOYMENT.md) - Deploying updated dependencies
- [Dependabot Setup](DEPENDABOT_SETUP.md) - Automated dependency updates
