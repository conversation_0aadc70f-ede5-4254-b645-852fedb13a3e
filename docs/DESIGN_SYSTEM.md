# BenefitLens Design System

## Text Color Guidelines

### Primary Text Colors
To ensure consistent readability and accessibility across the application, follow these text color guidelines:

#### Labels and Headings
- **Primary labels**: Use `text-gray-900` or `text-black` for maximum contrast
- **Secondary labels**: Use `text-gray-800` for slightly less emphasis
- **Never use**: `text-gray-700`, `text-gray-600`, or lighter grays for labels on white backgrounds

#### Body Text
- **Primary body text**: Use `text-gray-900`
- **Secondary body text**: Use `text-gray-800`
- **Muted text**: Use `text-gray-600` (only for non-essential information)

#### Admin Interface Specific Rules
- **All form labels**: Must use `text-gray-900` or darker
- **Filter labels**: Must use `text-gray-900` or darker
- **Section headings**: Must use `text-gray-900` or darker
- **Data labels**: Must use `text-gray-900` or darker

### Accessibility Requirements
- Ensure minimum contrast ratio of 4.5:1 for normal text
- Ensure minimum contrast ratio of 3:1 for large text (18pt+)
- Test all text colors on white backgrounds for readability

### Common Mistakes to Avoid
1. **Using light gray for labels**: `text-gray-700` and lighter are too light for labels
2. **Inconsistent label colors**: All labels in the same section should use the same color
3. **Poor contrast**: Always test text visibility on the intended background

### Implementation Examples

#### ✅ Correct Usage
```tsx
// Form labels
<label className="block text-sm font-medium text-gray-900 mb-2">
  Field Name
</label>

// Section headings
<h3 className="text-lg font-semibold text-gray-900">Section Title</h3>

// Data labels
<span className="font-medium text-gray-900">Data Label:</span>
```

#### ❌ Incorrect Usage
```tsx
// Too light for labels
<label className="block text-sm font-medium text-gray-700 mb-2">
  Field Name
</label>

// Inconsistent colors in same section
<label className="text-gray-900">Field 1</label>
<label className="text-gray-700">Field 2</label>
```

### Color Palette Reference
- `text-black`: #000000 (Maximum contrast)
- `text-gray-900`: #111827 (Primary text)
- `text-gray-800`: #1f2937 (Secondary text)
- `text-gray-700`: #374151 (Too light for labels)
- `text-gray-600`: #4b5563 (Muted text only)

### Enforcement
- All new components must follow these guidelines
- Code reviews should check for text color consistency
- E2E tests should verify text contrast ratios
- When in doubt, use `text-gray-900` for labels and headings

### Button Color Guidelines
- **Search/Filter actions**: Blue (`bg-blue-600`)
- **Delete actions**: Red (`bg-red-600`)
- **Warning/Removal requests**: Yellow (`bg-yellow-500`)
- **Avoid**: Black or gray button backgrounds

### Status and Badge Colors
- **Success/Verified**: Green (`bg-green-100 text-green-800`)
- **Warning/Pending**: Yellow (`bg-yellow-100 text-yellow-800`)
- **Error/Rejected**: Red (`bg-red-100 text-red-800`)
- **Neutral/Cancelled**: Gray (`bg-gray-100 text-gray-800`)

## Implementation Checklist
When creating or updating admin interface components:

- [ ] All labels use `text-gray-900` or darker
- [ ] Text contrast meets accessibility requirements
- [ ] Colors are consistent within the same section
- [ ] Button colors follow the established scheme
- [ ] Status indicators use appropriate color coding
