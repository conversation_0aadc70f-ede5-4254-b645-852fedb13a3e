# Company Ranking System Documentation

## Overview

BenefitLens uses a sophisticated composite ranking system to order companies on user-facing pages. This system prioritizes companies based on their actual value to job seekers rather than arbitrary alphabetical ordering.

## Problem Solved

Previously, companies were sorted alphabetically, which created an unfair advantage for companies with names starting with numbers or early letters (e.g., "1&1 AG" always appeared first). This new system ranks companies by merit and relevance.

## Ranking Algorithm

The ranking system uses a **three-factor composite score**:

### 1. Benefits Quality Score (50% weight)
- **Verified Benefits**: Each verified benefit adds 12 points
- **Total Benefits**: Each benefit (verified or unverified) adds 7 points
- **Rationale**: Verified benefits are more valuable to users, so they receive higher weight

### 2. User Benefit Preference Score (30% weight)
- **Based on User Rankings**: Uses data from the user benefit ranking system where users rank benefits 1-10 (1 = most important, 10 = least important)
- **Formula**: SUM(11 - AVG(user_ranking)) × 3 for each verified benefit the company offers
- **Only Verified Benefits**: Only applies to verified benefits to ensure data quality
- **Rationale**: Rewards companies offering benefits that users actually value most, making rankings more relevant to user preferences

### 3. User Engagement Score (20% weight)
- **Page Views**: Recent 30-day page views × 0.0002 multiplier
- **Benefit Interactions**: Recent 30-day benefit interactions × 0.002 multiplier
- **Rationale**: Companies that generate user interest and engagement are more relevant

### Tie-Breaking
When companies have identical scores, they are ordered by:
1. **RANDOM()** - Ensures fair rotation and prevents bias
2. **Company name (alphabetical)** - Final fallback for consistency

## Implementation Details

### Database Query Structure
```sql
ORDER BY (
  -- Benefits Quality Score (50% weight)
  (SELECT COUNT(*) FROM company_benefits cb_verified
   WHERE cb_verified.company_id = c.id AND cb_verified.is_verified = true) * 12 +
  (SELECT COUNT(*) FROM company_benefits cb_total
   WHERE cb_total.company_id = c.id) * 7 +

  -- User Benefit Preference Score (30% weight)
  -- Higher score for benefits users rank as more important (lower ranking number = more important)
  COALESCE((
    SELECT SUM(11 - AVG(ubr.ranking))
    FROM company_benefits cb_pref
    JOIN user_benefit_rankings ubr ON cb_pref.benefit_id = ubr.benefit_id
    WHERE cb_pref.company_id = c.id AND cb_pref.is_verified = true
    GROUP BY cb_pref.company_id
  ), 0) * 3 +

  -- User Engagement Score (20% weight)
  COALESCE((SELECT SUM(page_views) FROM company_analytics_summary cas
           WHERE cas.company_id = c.id AND cas.date >= CURRENT_DATE - INTERVAL '30 days'), 0) * 0.0002 +
  COALESCE((SELECT SUM(benefit_interactions) FROM company_analytics_summary cas
           WHERE cas.company_id = c.id AND cas.date >= CURRENT_DATE - INTERVAL '30 days'), 0) * 0.002
) DESC, RANDOM(), c.name ASC
```

### Performance Optimization
Two composite indexes support efficient ranking:
- `idx_company_benefits_company_verified` - Optimizes benefits counting
- `idx_company_analytics_summary_company_date` - Optimizes engagement metrics

## Applied Endpoints

### User-Facing (Smart Ranking Applied)
- `/api/companies` - Main company directory
- `/api/search` - Company search results

### Admin-Only (Administrative Sorting)
- `/api/admin/companies` - Maintains alphabetical/date sorting for management

## Design Decisions

### Why Profile Completeness Was Removed
- **Company Control**: Companies cannot edit their profiles (admin-only)
- **Fairness**: Ranking should reflect company-controlled factors
- **Focus**: Emphasizes benefits and user engagement over admin-managed data

### Why Randomization for Ties
- **Fairness**: Prevents systematic bias toward any company
- **Rotation**: Ensures all companies get equal exposure opportunities
- **User Experience**: Provides variety in repeated visits

## Example Ranking Scenarios

### High-Ranking Company (e.g., BMW Group)
- **Benefits Quality**: 5 verified benefits = 60 points, 5 total benefits = 35 points
- **User Preferences**: High-value benefits (avg user ranking 3.0) = SUM(11-3.0) × 3 × 5 = 120 points
- **User Engagement**: 1000 monthly page views = 0.2 points, 50 benefit interactions = 0.1 points
- **Total Score**: ~215.3 points

### Medium-Ranking Company (e.g., SAP SE)
- **Benefits Quality**: 2 verified benefits = 24 points, 3 total benefits = 21 points
- **User Preferences**: Mixed-value benefits (avg user ranking 5.0) = SUM(11-5.0) × 3 × 2 = 36 points
- **User Engagement**: 500 monthly page views = 0.1 points, 25 benefit interactions = 0.05 points
- **Total Score**: ~81.15 points

### Low-Ranking Company (e.g., 1&1 AG)
- **Benefits Quality**: 0 verified benefits = 0 points, 1 total benefit = 7 points
- **User Preferences**: No verified benefits = 0 points
- **User Engagement**: 100 monthly page views = 0.02 points, 5 benefit interactions = 0.01 points
- **Total Score**: ~7.03 points

## Monitoring and Maintenance

### Key Metrics to Track
- Distribution of company scores
- User engagement with top-ranked companies
- Benefits verification rates
- Search result relevance

### Potential Adjustments
- Weight ratios between benefits and engagement
- Multipliers for engagement metrics
- Time window for engagement data (currently 30 days)

## Future Considerations

### User Preference Toggle
**Recommendation**: Yes, adding a sorting toggle would be valuable for user choice and accessibility.

**Suggested Implementation:**
```jsx
<select onChange={handleSortChange} value={sortOption}>
  <option value="smart">Smart Ranking (Recommended)</option>
  <option value="alphabetical">A-Z</option>
  <option value="newest">Newest First</option>
</select>
```

**Benefits:**
- **User Choice**: Some users prefer predictable alphabetical ordering
- **Accessibility**: Helps users with different browsing preferences
- **Transparency**: Makes the ranking system more obvious to users
- **Fallback**: Provides alternatives if smart ranking has issues

**Implementation Notes:**
- Keep "Smart Ranking" as default for optimal discovery
- Store preference in localStorage for returning users
- Add URL parameter support for bookmarking/sharing
- Consider analytics to track usage patterns

This would provide user choice while maintaining the improved default experience.

## User Sorting Options

**✅ IMPLEMENTED**: Users can now choose between three sorting options:

### Available Sort Options

1. **Smart Ranking (Default)**
   - Uses the full three-factor composite scoring algorithm
   - Provides the most relevant results based on benefits quality, user preferences, and engagement
   - Best for discovering high-quality companies with valuable benefits

2. **Alphabetical**
   - Simple A-Z sorting by company name
   - Useful for finding specific companies when you know the name
   - Provides predictable, consistent ordering

3. **Newest First**
   - Orders companies by creation date (newest first)
   - Helpful for discovering recently added companies
   - Falls back to alphabetical for companies created on the same date

### Implementation Features

- **URL Parameter Support**: Sort preference is stored in URL (`?sort=alphabetical`) for bookmarking and sharing
- **Persistent State**: Sort selection persists across page navigation and refreshes
- **Smart Defaults**: Defaults to "Smart Ranking" when no sort parameter is specified
- **Responsive UI**: Sort toggle appears only when there are results to sort
- **API Integration**: Both `/api/companies` and `/api/search` endpoints support the sort parameter

## Technical Notes

### Database Requirements
- PostgreSQL with analytics tracking enabled
- Materialized views for performance (if applicable)
- Regular analytics data collection

### Cache Considerations
- Ranking calculations are performed in real-time
- Consider caching for high-traffic scenarios
- Analytics data updates affect rankings

## Migration History

- **Migration 029**: Added performance indexes for ranking system
- **Removed**: Profile completeness scoring (admin-only data)
- **Added**: Randomization for fair tie-breaking
