# Admin API Authentication Guide

This guide explains how administrators can authenticate to access BenefitLens APIs directly.

## Overview

BenefitLens APIs are protected to prevent unauthorized scraping while allowing legitimate access:

- **Web UI**: Always accessible to all users (no authentication required)
- **Direct API Access**: Requires admin authentication
- **Analytics APIs**: Available to paying customers with proper authentication

## Admin Authentication Methods

### Method 1: Session-Based Authentication (Recommended)

1. **Sign in through the web interface** as an admin user
2. **Extract the session token** from your browser cookies
3. **Use the session token** in API requests

#### Step 1: Get Your Session Token

After signing in as admin, open browser developer tools:

```javascript
// In browser console:
document.cookie.split(';').find(c => c.trim().startsWith('session_token='))
```

Or check the `session_token` cookie in your browser's developer tools.

#### Step 2: Use Session Token in API Requests

```bash
# Example: Get all companies
curl -H "Cookie: session_token=YOUR_SESSION_TOKEN_HERE" \
     https://benefitlens.de/api/companies

# Example: Search companies
curl -H "Cookie: session_token=YOUR_SESSION_TOKEN_HERE" \
     "https://benefitlens.de/api/search?q=SAP"

# Example: Get benefits
curl -H "Cookie: session_token=YOUR_SESSION_TOKEN_HERE" \
     https://benefitlens.de/api/benefits
```

### Method 2: Magic Link Authentication

1. **Request a magic link** for your admin email
2. **Use the session** created after clicking the magic link

```bash
# Request magic link
curl -X POST https://benefitlens.de/api/auth/magic-link \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>"}'

# After clicking the magic link, extract session token and use as above
```

## Available Admin API Endpoints

### Company Data
```bash
# Get all companies (with pagination)
GET /api/companies?page=1&limit=30

# Search companies
GET /api/search?q=search_term

# Get specific company
GET /api/companies/{company_id}

# Get company benefits
GET /api/companies/{company_id}/benefits

# Get company locations
GET /api/companies/{company_id}/locations
```

### Benefits Data
```bash
# Get all benefits
GET /api/benefits

# Get benefits by category
GET /api/benefits?category=health

# Get benefit categories
GET /api/benefit-categories
```

### Filter Options
```bash
# Get industry options
GET /api/filter-options/industries

# Get company size options
GET /api/filter-options/sizes

# Get benefit filter options
GET /api/filter-options/benefits
```

### Location Data
```bash
# Get location suggestions
GET /api/locations/suggestions?q=Berlin
```

## Example Admin API Usage

### Python Example
```python
import requests

# Your session token from browser
SESSION_TOKEN = "your_session_token_here"

headers = {
    'Cookie': f'session_token={SESSION_TOKEN}'
}

# Get all companies
response = requests.get('https://benefitlens.de/api/companies', headers=headers)
companies = response.json()

print(f"Found {companies['total']} companies")
for company in companies['companies']:
    print(f"- {company['name']} ({company['industry']})")
```

### JavaScript/Node.js Example
```javascript
const axios = require('axios');

const SESSION_TOKEN = 'your_session_token_here';

const api = axios.create({
  baseURL: 'https://benefitlens.de/api',
  headers: {
    'Cookie': `session_token=${SESSION_TOKEN}`
  }
});

// Get companies with benefits filter
async function getCompaniesWithBenefit(benefit) {
  try {
    const response = await api.get(`/companies?benefits=${benefit}`);
    return response.data;
  } catch (error) {
    console.error('API Error:', error.response?.data);
  }
}

// Usage
getCompaniesWithBenefit('Remote Work').then(data => {
  console.log(`Found ${data.total} companies offering Remote Work`);
});
```

## Security Notes

- **Session tokens expire** - you'll need to re-authenticate periodically
- **Keep tokens secure** - don't share or commit them to version control
- **Use HTTPS** - always use secure connections for API requests
- **Rate limiting** - APIs are rate limited (100 requests per minute)

## Troubleshooting

### 401 Unauthorized
- Your session token has expired - sign in again
- You're not signed in as an admin user

### 500 Internal Server Error
- You're trying to access a protected endpoint without proper authentication
- Check that you're including the session token correctly

### 429 Too Many Requests
- You've exceeded the rate limit (100 requests per minute)
- Wait before making more requests

## Admin Dashboard Access

For web-based admin operations, use the admin dashboard:
- **URL**: https://benefitlens.de/admin
- **Features**: Company management, user management, analytics, etc.
