# Logging Improvements

This document describes the improvements made to reduce log verbosity and provide more appropriate logging based on log levels.

## Problem

Previously, API access control errors (like "Direct API access not allowed") were generating huge log entries with full stack traces, even when the log level was set to WARN. These are expected security events and shouldn't generate verbose logs.

## Solution

### 1. Enhanced API Access Control Logging

- **Custom Error Class**: Created `APIAccessDeniedError` for API access control errors
- **Structured Logging**: API access control now uses the structured logger instead of throwing generic errors
- **Appropriate Log Levels**: API access denied events are logged at DEBUG level since they're expected security events

### 2. API Error Handler

Created a new `api-error-handler.ts` module that provides:

- **Smart Error Classification**: Different error types are logged at appropriate levels
- **Log Level Awareness**: Stack traces and verbose context only included when appropriate
- **Consistent Error Responses**: Standardized error response format

### 3. Logger Enhancements

Enhanced the structured logger to be more concise based on log level:

- **WARN Level**: Shows minimal context (url, method, errorType) and no stack traces for warnings
- **DEBUG Level**: Shows full context and stack traces
- **Production**: Creates concise JSON entries for WARN level logs

## Log Level Behavior

### DEBUG Level
- Full context information
- Complete stack traces
- Detailed user agent strings
- All error details

### INFO Level
- Standard context information
- Stack traces for actual errors only
- Full user agent strings

### WARN Level
- Minimal context (url, method, errorType)
- No stack traces for warnings
- Truncated user agent strings (first 50 chars)
- Concise JSON format in production

### ERROR Level
- Full context for actual server errors
- Complete stack traces
- All error details

## Error Classification

### API Access Denied (DEBUG level)
```
Direct API access not allowed. Please use the web interface.
```
- Expected security event
- Logged at DEBUG level
- Minimal context in WARN mode

### Authentication Errors (INFO level)
```
Authentication required
Admin access required
```
- Normal authorization flow
- Logged at INFO level
- Standard context

### Server Errors (ERROR level)
```
Database connection failed
Internal server error
```
- Actual application problems
- Logged at ERROR level
- Full context and stack traces

## Usage

### Setting Log Level

Set the `LOG_LEVEL` environment variable:

```bash
# Development (verbose)
LOG_LEVEL=debug

# Production (minimal)
LOG_LEVEL=warn

# Standard
LOG_LEVEL=info
```

### Using Error Handler in API Routes

```typescript
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (request: NextRequest) => {
  // Your API logic here
  await requireWebUIOrAdmin(request)
  // ... rest of handler
})
```

### Manual Error Logging

```typescript
import { logAPIError } from '@/lib/api-error-handler'

try {
  // API logic
} catch (error) {
  if (error instanceof Error) {
    logAPIError(error, request, { operation: 'fetch_data' })
  }
  // Handle error appropriately
}
```

## Benefits

1. **Reduced Log Noise**: Expected security events don't generate verbose logs
2. **Appropriate Verbosity**: Log detail matches the configured log level
3. **Better Production Logs**: Concise, structured logs for log aggregation
4. **Consistent Error Handling**: Standardized error responses across API routes
5. **Security Event Tracking**: API access attempts are still logged but at appropriate levels

## Migration Status

### ✅ Updated Routes (Using New Error Handling)
**High-Traffic Routes:**
- `/api/benefit-categories` - Uses withErrorHandling wrapper
- `/api/benefits` - Uses withErrorHandling wrapper (GET and POST)
- `/api/companies` - Uses enhanced logAPIError for error logging
- `/api/companies/[id]` - Uses withErrorHandling wrapper
- `/api/filter-options/benefits` - Uses withErrorHandling wrapper
- `/api/filter-options/industries` - Uses withErrorHandling wrapper
- `/api/filter-options/sizes` - Uses withErrorHandling wrapper
- `/api/search` - Uses withErrorHandling wrapper

**Admin Routes:**
- `/api/admin/auth-logs` - Uses withErrorHandling wrapper
- `/api/admin/cache/clear` - Uses withErrorHandling wrapper
- `/api/admin/stats` - Uses withErrorHandling wrapper

**Auth Routes:**
- `/api/auth/me` - Uses withErrorHandling wrapper

**Progress: 11/91 routes updated (12%)**

### 🔄 Remaining Routes (Still Using Old Error Handling)

**HIGH PRIORITY** (Use `requireWebUIOrAdmin` - generate verbose logs):
- `/api/companies/[id]/benefits` - Company benefit management
- `/api/companies/[id]/locations` - Company location data
- `/api/companies` - Main companies endpoint (partially updated)
- `/api/benefit-removal-disputes/[companyBenefitId]` - Benefit disputes
- `/api/benefit-removal-disputes` - Benefit disputes listing
- `/api/user/saved-companies` - User saved companies
- `/api/saved-companies` - Saved companies management
- `/api/locations/suggestions` - Location autocomplete

**MEDIUM PRIORITY** (Admin routes - 25 remaining):
- All `/api/admin/*` routes except the 3 already updated
- These affect admin user experience but have lower traffic

**LOWER PRIORITY** (Auth & Analytics - 17 remaining):
- `/api/auth/*` routes (8 routes) - Authentication flows
- `/api/analytics/*` routes (9 routes) - Analytics and tracking

**Total remaining: 80/91 routes (88%)**

### Migration Steps

To migrate existing API routes:

1. Import the error handler: `import { withErrorHandling } from '@/lib/api-error-handler'`
2. Wrap your handler: `export const GET = withErrorHandling(async (request) => { ... })`
3. Remove manual try/catch blocks for error logging
4. The error handler will automatically log and respond appropriately

### Finding Routes to Update

Use the provided scripts to analyze and update routes:

```bash
# Get detailed migration status report
./scripts/update-remaining-routes.sh

# Find specific error handling patterns
./scripts/find-old-error-handling.sh
```

### Automated Migration Helper

For bulk updates, you can use this pattern to quickly update multiple routes:

```bash
# Example: Update a specific route
sed -i 's/export async function GET/export const GET = withErrorHandling(async/' src/app/api/path/route.ts
# Add import and remove try/catch manually
```

## Testing

The logging improvements are covered by comprehensive tests in `src/__tests__/api-error-logging.test.ts` that verify:

- Correct log levels for different error types
- Appropriate context inclusion based on log level
- Stack trace inclusion rules
- Error response status codes
