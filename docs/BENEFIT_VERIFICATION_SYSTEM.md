# BenefitLens Verification System Documentation

## Overview

BenefitLens employs a sophisticated three-tier verification system that combines crowdsourced validation with administrative oversight to ensure the accuracy and reliability of company benefit data. This system balances automation with human judgment to maintain high data quality while scaling efficiently.

## System Architecture

### 1. User Benefit Verifications (`benefit_verifications` table)

**Purpose**: Crowdsourced validation from employees who have direct experience with company benefits.

**Database Schema**:
```sql
CREATE TABLE benefit_verifications (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_benefit_id uuid NOT NULL,
    user_id varchar(255) NOT NULL,
    status varchar(50) NOT NULL CHECK (status IN ('confirmed', 'disputed')),
    comment text,
    created_at timestamp with time zone DEFAULT now()
);
```

**Key Features**:
- Users can submit `'confirmed'` or `'disputed'` verifications
- Multiple users can verify the same benefit
- Requires domain authorization (user email domain must match company domain)
- One verification per user per benefit
- Comments provide additional context

### 2. Admin Verification (`company_benefits.is_verified` field)

**Purpose**: Official verification status with administrative oversight and automatic promotion capability.

**Database Schema**:
```sql
CREATE TABLE company_benefits (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id uuid NOT NULL,
    benefit_id uuid NOT NULL,
    added_by varchar(255),
    is_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);
```

**Key Features**:
- Boolean field indicating official verification status
- Can be manually controlled by administrators
- Automatically updated based on user verification consensus
- Used for public display and filtering

### 3. Benefit Removal Disputes (`benefit_removal_disputes` table)

**Purpose**: Challenge mechanism for removing incorrectly verified benefits from companies.

**Database Schema**:
```sql
CREATE TABLE benefit_removal_disputes (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_benefit_id uuid NOT NULL,
    user_id uuid NOT NULL,
    reason text NOT NULL,
    status varchar(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
    admin_user_id uuid,
    admin_comment text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);
```

**Key Features**:
- Only verified benefits can be disputed
- Requires detailed reason for dispute
- Admin review and approval process
- Automatic benefit removal upon consensus

## Verification Workflows

### User Verification Process

1. **Eligibility Check**:
   - User must be authenticated
   - User email domain must match company domain
   - User cannot have already verified this specific benefit

2. **Submission**:
   ```typescript
   POST /api/benefit-verifications
   {
     "companyBenefitId": "uuid",
     "status": "confirmed",
     "comment": "Optional additional context"
   }
   ```

3. **Automatic Status Update**:
   - System recalculates verification status
   - Updates `company_benefits.is_verified` if consensus reached

### Automatic Verification Logic

```typescript
function updateCompanyBenefitStatus(companyBenefitId: string) {
  // Count verifications
  const confirmedCount = verifications.filter(v => v.status === 'confirmed').length
  const disputedCount = verifications.filter(v => v.status === 'disputed').length
  
  // Verification criteria
  const isVerified = confirmedCount >= 2 && confirmedCount > disputedCount
  
  // Update official status
  await query('UPDATE company_benefits SET is_verified = $1 WHERE id = $2', 
              [isVerified, companyBenefitId])
}
```

**Verification Criteria**:
- Minimum 2 confirmed verifications required
- Confirmed verifications must outnumber disputed verifications
- Automatically promotes to verified status when criteria met

### Admin Override Process

Administrators can manually verify or unverify benefits regardless of user input:

```typescript
PATCH /api/admin/companies/{companyId}/benefits/verify
{
  "benefitIds": ["uuid1", "uuid2"],
  "action": "verify", // or "unverify"
  "reason": "Manual verification based on company documentation"
}
```

### Benefit Removal Dispute Process

1. **Dispute Submission**:
   - Only for verified benefits (`is_verified = true`)
   - User must be from same company domain
   - One dispute per user per benefit

2. **Admin Review**:
   ```typescript
   POST /api/admin/benefit-removal-disputes
   {
     "disputeId": "uuid",
     "action": "approve", // or "reject"
     "adminComment": "Reason for decision"
   }
   ```

3. **Automatic Removal**:
   - Triggered when 2+ disputes from different users are approved
   - Benefit is completely removed from company
   - Activity logged for audit trail

## API Endpoints

### User Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/benefit-verifications` | Submit benefit verification |
| GET | `/api/benefit-verifications/user/{companyBenefitId}` | Check user's verification status |
| POST | `/api/benefit-removal-disputes` | Submit removal dispute |
| GET | `/api/benefit-removal-disputes/{companyBenefitId}` | Get disputes for benefit |

### Admin Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| PATCH | `/api/admin/companies/{companyId}/benefits/verify` | Bulk verify/unverify benefits |
| POST | `/api/admin/recalculate-verifications` | Recalculate all verification statuses |
| GET | `/api/admin/benefit-removal-disputes` | Get pending disputes |
| POST | `/api/admin/benefit-removal-disputes` | Approve/reject disputes |

## Security and Authorization

### Domain Authorization
- Users can only verify benefits for companies matching their email domain
- Prevents cross-company verification abuse
- Implemented via `checkDomainAuthorization()` function

### Admin Permissions
- All admin endpoints require `requireAdmin()` middleware
- Admin actions are logged with user attribution
- Admin comments required for dispute decisions

### Rate Limiting
- One verification per user per benefit
- One dispute per user per benefit
- Prevents spam and manipulation

## Data Integrity

### Verification Status Consistency
- `updateCompanyBenefitStatus()` ensures consistency between user verifications and admin status
- Recalculation endpoint allows fixing inconsistencies
- Cache invalidation ensures UI reflects current state

### Audit Trail
- All verifications logged with timestamps and user attribution
- Activity logging for benefit verification, disputes, and removals
- Admin actions tracked with reasoning

## Benefits of This System

1. **Scalability**: Crowdsourced verification reduces admin workload
2. **Accuracy**: Multiple validation layers catch errors
3. **Flexibility**: Admin override capability for edge cases
4. **Transparency**: Complete audit trail for all actions
5. **Quality Control**: Dispute mechanism removes incorrect data
6. **User Trust**: Clear verification indicators build confidence

## Monitoring and Maintenance

### Key Metrics
- Verification completion rates by company
- Admin override frequency
- Dispute resolution times
- Verification accuracy (confirmed vs disputed ratios)

### Maintenance Tasks
- Regular recalculation of verification statuses
- Review of pending disputes
- Monitoring for verification gaming or abuse
- Performance optimization of verification queries

## Future Enhancements

- Machine learning models to detect verification patterns
- Reputation scoring for frequent verifiers
- Automated dispute detection based on verification patterns
- Integration with external benefit databases for cross-validation
