# Dependabot Configuration and Setup

This document explains how BenefitLens handles automated dependency updates through Dependabot and ensures all updates pass tests before being merged.

## Overview

BenefitLens uses GitHub Dependabot to automatically create pull requests for dependency updates. The system is configured to:

1. **Create weekly PRs** for npm, GitHub Actions, and Docker dependency updates
2. **Group related updates** to reduce PR noise
3. **Require all tests to pass** before PRs can be merged
4. **Auto-merge safe updates** (patch updates for dev dependencies)
5. **Require manual review** for major version updates

## Configuration Files

### 1. Dependabot Configuration (`.github/dependabot.yml`)

The main Dependabot configuration includes:

- **Weekly schedule** for dependency updates (Mondays at 9 AM CET)
- **Grouped updates** for production and development dependencies
- **Automatic labeling** and reviewer assignment
- **Ignored major updates** for critical dependencies (Next.js, React, etc.)

### 2. Dependabot PR Workflow (`.github/workflows/dependabot-pr.yml`)

A specialized workflow that:

- **Detects Dependabot PRs** automatically
- **Runs full test suite** (unit tests, integration tests, build)
- **Comments on PR status** with test results
- **Auto-approves safe updates** (dev dependency patches)
- **Enables auto-merge** for approved safe updates

### 3. Main Test Pipeline (`.github/workflows/test-pipeline.yml`)

The main test pipeline runs on all PRs (including Dependabot PRs) and includes:

- **Unit tests** with coverage reporting
- **Integration tests** with real database
- **E2E tests** with Playwright
- **Build verification**
- **Security scanning**

## Required GitHub Repository Settings

To ensure Dependabot PRs cannot be merged without passing tests, configure these branch protection rules:

### Branch Protection Rules for `main` branch:

1. **Go to Repository Settings** → Branches → Add rule for `main`

2. **Enable these settings:**
   ```
   ✅ Require a pull request before merging
   ✅ Require approvals (1 approval minimum)
   ✅ Dismiss stale PR approvals when new commits are pushed
   ✅ Require review from code owners (if CODEOWNERS file exists)
   ✅ Require status checks to pass before merging
   ✅ Require branches to be up to date before merging
   ✅ Require conversation resolution before merging
   ✅ Include administrators
   ```

3. **Required status checks:**
   ```
   - Unit Tests
   - Integration Tests  
   - E2E Tests
   - Dependabot Tests (for Dependabot PRs)
   ```

4. **Additional settings:**
   ```
   ✅ Restrict pushes that create files larger than 100 MB
   ✅ Allow force pushes: Everyone (for emergency fixes)
   ✅ Allow deletions: Disabled
   ```

### Branch Protection Rules for `develop` branch:

Apply the same rules as `main` but with slightly relaxed requirements:

```
✅ Require a pull request before merging
✅ Require status checks to pass before merging
✅ Require branches to be up to date before merging
- Approvals: Not required (for faster development)
```

## Dependabot PR Workflow

### Automatic Process

1. **Dependabot creates PR** with dependency updates
2. **Dependabot PR workflow triggers** automatically
3. **Full test suite runs** (unit, integration, build)
4. **PR gets commented** with test results
5. **Safe updates get auto-approved** (dev dependency patches)
6. **Auto-merge enabled** for approved safe updates

### Manual Review Process

For updates requiring manual review:

1. **Review the PR** for breaking changes
2. **Check test results** in workflow logs
3. **Test locally** if needed:
   ```bash
   git checkout dependabot/npm_and_yarn/package-name
   npm install
   npm run test
   npm run build
   ```
4. **Approve and merge** if everything looks good

### Types of Updates

#### Auto-merged (No manual review needed):
- **Patch updates** for development dependencies
- **Minor updates** for development tools (ESLint, Prettier, etc.)
- **GitHub Actions** minor/patch updates

#### Manual review required:
- **Major version updates** for any dependency
- **Minor/patch updates** for production dependencies
- **Security updates** (always review carefully)

## Monitoring and Maintenance

### Weekly Tasks

1. **Review auto-merged PRs** in the weekly summary
2. **Check for failed Dependabot PRs** and investigate issues
3. **Update ignored dependencies** if needed

### Monthly Tasks

1. **Review major version updates** that were ignored
2. **Plan upgrade strategy** for critical dependencies
3. **Update Dependabot configuration** if needed

### Emergency Procedures

If a Dependabot update breaks the application:

1. **Immediately revert** the problematic PR
2. **Create hotfix branch** with the revert
3. **Update Dependabot ignore rules** to prevent the update
4. **Investigate and fix** the underlying issue
5. **Remove ignore rule** once fixed

## Troubleshooting

### Common Issues

#### Dependabot PR fails tests:
1. Check if it's a breaking change in the dependency
2. Update code to be compatible with new version
3. Add migration notes to the PR

#### Auto-merge not working:
1. Verify branch protection rules are correctly configured
2. Check that all required status checks are passing
3. Ensure the PR is from Dependabot (not a fork)

#### Too many Dependabot PRs:
1. Adjust `open-pull-requests-limit` in dependabot.yml
2. Improve grouping configuration
3. Add more dependencies to ignore list temporarily

### Getting Help

- **GitHub Dependabot docs**: https://docs.github.com/en/code-security/dependabot
- **Workflow logs**: Check the Actions tab for detailed error messages
- **Team discussion**: Use GitHub Discussions for questions

## Security Considerations

- **Always review security updates** manually, even if they're patch updates
- **Monitor for supply chain attacks** in dependency updates
- **Use `npm audit`** regularly to check for vulnerabilities
- **Keep the ignore list minimal** to ensure security updates are applied

## Configuration Updates

When modifying Dependabot configuration:

1. **Test changes** in a feature branch first
2. **Update documentation** if behavior changes
3. **Notify team members** of significant changes
4. **Monitor first few PRs** after configuration changes
