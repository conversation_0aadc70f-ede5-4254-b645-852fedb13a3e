# Safari/WebKit Testing with Docker

This document explains how to run Safari/WebKit tests using Docker containers to solve dependency issues on local machines.

## Problem

Safari/WebKit testing with <PERSON><PERSON> requires specific system dependencies that may not be available on all development machines. The error typically looks like:

```
MiniBrowser: symbol lookup error: /usr/lib/libgudev-1.0.so.0: undefined symbol: g_once_init_enter_pointer
```

## Solution

We use Docker containers with all necessary dependencies pre-installed to run Safari/WebKit tests.

## Quick Start

### 1. Run Safari Tests (Easiest)

```bash
# Run all Safari tests
npm run test:e2e:safari

# Run specific Safari test pattern
./scripts/test-safari.sh "Filter Dropdown"

# Run Safari tests in headed mode (visible browser)
npm run test:e2e:safari:headed
```

### 2. Manual Setup and Testing

```bash
# Setup test infrastructure
npm run test:docker:setup

# Run Safari tests
npm run test:e2e:safari

# Cleanup when done
npm run test:docker:teardown
```

## Available Commands

### NPM Scripts

| Command | Description |
|---------|-------------|
| `npm run test:e2e:safari` | Run Safari tests in Docker |
| `npm run test:e2e:safari:headed` | Run Safari tests with visible browser |
| `npm run test:e2e:all-browsers` | Run all browser tests in Docker |
| `npm run test:docker:setup` | Setup test infrastructure only |
| `npm run test:docker:teardown` | Cleanup test infrastructure |
| `npm run test:docker:reset` | Reset test infrastructure |

### Shell Script Options

The `./scripts/test-safari.sh` script provides more control:

```bash
# Show help
./scripts/test-safari.sh --help

# Build Docker image and run tests
./scripts/test-safari.sh --build

# Run specific test pattern
./scripts/test-safari.sh "Cross-Browser Compatibility"

# Run tests in headed mode
./scripts/test-safari.sh --headed

# Setup infrastructure only (don't run tests)
./scripts/test-safari.sh --setup-only

# Cleanup infrastructure
./scripts/test-safari.sh --cleanup

# Reset everything (cleanup + rebuild)
./scripts/test-safari.sh --reset
```

## Architecture

### Docker Containers

1. **postgres-test**: PostgreSQL database for testing
2. **app-test**: BenefitLens application server
3. **mailhog-test**: Email testing service
4. **playwright**: Test runner with Safari support

### Key Files

- `Dockerfile.playwright`: Playwright container with WebKit dependencies
- `docker-compose.playwright.yml`: Test infrastructure setup
- `playwright.docker.config.ts`: Playwright config for Docker
- `scripts/test-safari.sh`: Convenient testing script

## Configuration

### Environment Variables

The Docker setup uses these environment variables:

```env
NODE_ENV=test
DATABASE_URL=*********************************************************************/benefitlens_test
NEXTAUTH_SECRET=test-secret-key-for-playwright-testing
NEXTAUTH_URL=http://app-test:3000
APP_URL=http://app-test:3000
RATE_LIMIT_ENABLED=false
PLAYWRIGHT_BASE_URL=http://app-test:3000
```

### Ports

- **3000**: Application server
- **5433**: PostgreSQL test database
- **1026**: SMTP server (MailHog)
- **8026**: MailHog web UI

## Troubleshooting

### Docker Issues

```bash
# Check if Docker is running
docker info

# Check container status
docker-compose -f docker-compose.playwright.yml ps

# View container logs
docker-compose -f docker-compose.playwright.yml logs app-test
docker-compose -f docker-compose.playwright.yml logs postgres-test
```

### Application Issues

```bash
# Check if application is responding
curl -f http://localhost:3000/api/health

# View application logs
docker-compose -f docker-compose.playwright.yml logs -f app-test
```

### Test Issues

```bash
# Run tests with debug output
docker-compose -f docker-compose.playwright.yml --profile testing run --rm playwright npm run test:e2e -- --config=playwright.docker.config.ts --project=webkit --debug

# Check test reports
ls -la playwright-report-docker/
```

### Cleanup Issues

```bash
# Force cleanup everything
docker-compose -f docker-compose.playwright.yml down -v
docker system prune -f

# Remove all test containers and volumes
docker-compose -f docker-compose.playwright.yml down -v --remove-orphans
```

## Development Workflow

### 1. Local Development (Chrome/Firefox)

For regular development, use local testing:

```bash
npm run test:e2e  # Local Chrome/Firefox tests
```

### 2. Safari Testing (Docker)

When you need to test Safari compatibility:

```bash
npm run test:e2e:safari  # Safari tests in Docker
```

### 3. Full Cross-Browser Testing

For comprehensive testing:

```bash
npm run test:e2e:all-browsers  # All browsers in Docker
```

## Performance Tips

### 1. Persistent Volumes

The setup uses Docker volumes to cache:
- Playwright browsers (`playwright_cache`)
- PostgreSQL data (`postgres_test_data`)

### 2. Image Caching

Build the Playwright image once:

```bash
./scripts/test-safari.sh --build
```

Then reuse it for multiple test runs.

### 3. Parallel Testing

The Docker config supports parallel testing:

```bash
# Run tests in parallel
docker-compose -f docker-compose.playwright.yml --profile testing run --rm playwright npm run test:e2e -- --config=playwright.docker.config.ts --workers=2
```

## CI/CD Integration

### GitHub Actions Example

```yaml
- name: Run Safari Tests
  run: |
    ./scripts/test-safari.sh --build
    npm run test:e2e:safari
```

### Local CI Testing

```bash
# Simulate CI environment
CI=true ./scripts/test-safari.sh --build
```

## Maintenance

### Update Playwright Version

1. Update `Dockerfile.playwright` with new Playwright version
2. Rebuild image: `./scripts/test-safari.sh --build`
3. Test: `npm run test:e2e:safari`

### Update Dependencies

```bash
# Rebuild with latest dependencies
./scripts/test-safari.sh --reset
```

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review container logs
3. Ensure Docker has sufficient resources (4GB+ RAM recommended)
4. Try resetting the environment: `./scripts/test-safari.sh --reset`
