# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env.prod

# typescript
*.tsbuildinfo
next-env.d.ts

# test results
/test-results
/playwright-report**
/playwright-report-*

# temporary files
*.tmp
*.temp
*.log
.actrc

# local environment files
.env.local.secrets

# prod backups
backups/*
database/schema.sql.backup.*
