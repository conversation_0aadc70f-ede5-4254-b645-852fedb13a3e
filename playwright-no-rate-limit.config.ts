import { defineConfig, devices } from '@playwright/test'

/**
 * Playwright configuration for testing with rate limiting disabled
 * This config includes iPhone, MacBook, and other devices for comprehensive testing
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './src/__tests__/e2e',
  /* Run tests in files in parallel */
  fullyParallel: false, // Sequential for E2E tests to avoid conflicts
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : 1, // Single worker for E2E tests
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html', { outputFolder: 'playwright-report-no-rate-limit' }],
    ['json', { outputFile: 'test-results/e2e-results-no-rate-limit.json' }],
    ['list']
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: process.env.E2E_BASE_URL || 'http://localhost:3000',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    
    /* Take screenshot on failure */
    screenshot: 'only-on-failure',
    
    /* Record video on failure */
    video: 'retain-on-failure',
    
    /* Reduced timeouts since rate limiting is disabled */
    actionTimeout: 15000, // Reduced from 30000
    navigationTimeout: 15000, // Reduced from 30000
  },

  /* Configure projects for major browsers and devices */
  projects: [
    {
      name: 'Desktop Chrome',
      use: { ...devices['Desktop Chrome'] },
    },

    {
      name: 'Desktop Safari',
      use: { ...devices['Desktop Safari'] },
    },

    /* Test against mobile viewports */
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },

    /* Test against iPhone devices */
    {
      name: 'iPhone 14',
      use: { ...devices['iPhone 14'] },
    },
    {
      name: 'iPhone 14 Pro',
      use: { ...devices['iPhone 14 Pro'] },
    },
    {
      name: 'iPhone 13',
      use: { ...devices['iPhone 13'] },
    },
    {
      name: 'iPhone SE',
      use: { ...devices['iPhone SE'] },
    },

    /* Test against iPad devices */
    {
      name: 'iPad Pro',
      use: { ...devices['iPad Pro'] },
    },
    {
      name: 'iPad Mini',
      use: { ...devices['iPad Mini'] },
    },

    /* Test against MacBook resolutions */
    {
      name: 'MacBook Pro 14"',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1512, height: 982 }, // MacBook Pro 14" native resolution
      },
    },
    {
      name: 'MacBook Pro 16"',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1728, height: 1117 }, // MacBook Pro 16" native resolution
      },
    },
    {
      name: 'MacBook Air 13"',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1440, height: 900 }, // MacBook Air 13" resolution
      },
    },
    {
      name: 'MacBook Air 15"',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1440, height: 932 }, // MacBook Air 15" resolution
      },
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'DISABLE_RATE_LIMITING=true npm run dev:no-turbo',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 60000, // Reduced from 120000 since rate limiting is disabled
    env: {
      DISABLE_RATE_LIMITING: 'true',
    },
  },

  /* Global setup and teardown */
  globalSetup: './src/__tests__/e2e/global-setup.ts',
  globalTeardown: './src/__tests__/e2e/global-teardown.ts',

  /* Test timeout - reduced since rate limiting is disabled */
  timeout: 60000, // 1 minute per test (reduced from 2 minutes)

  /* Expect timeout */
  expect: {
    timeout: 15000, // 15 seconds for assertions (reduced from 30 seconds)
  },

  /* Output directory for test artifacts */
  outputDir: 'test-results/e2e-artifacts-no-rate-limit',
})
