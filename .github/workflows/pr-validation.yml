name: PR Validation

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
    branches: [main, master]

permissions:
  contents: read
  pull-requests: read
  checks: write

concurrency:
  group: pr-validation-${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  # Basic validation for all PRs
  validate-pr:
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    outputs:
      is-dependabot: ${{ steps.check-author.outputs.is-dependabot }}
      
    steps:
      - uses: actions/checkout@v4
      
      - name: Check if PR is from Dependabot
        id: check-author
        run: |
          if [[ "${{ github.event.pull_request.user.login }}" == "dependabot[bot]" ]]; then
            echo "is-dependabot=true" >> $GITHUB_OUTPUT
            echo "This is a Dependabot PR"
          else
            echo "is-dependabot=false" >> $GITHUB_OUTPUT
            echo "This is a regular PR"
          fi

  # Run full test suite for all PRs (required for merge)
  run-tests:
    needs: validate-pr
    uses: ./.github/workflows/test-pipeline.yml
    secrets: inherit
    if: github.event.pull_request.draft == false

  # Build validation (ensure the app can be built)
  build-validation:
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: benefitlens
          POSTGRES_USER: benefitlens_user
          POSTGRES_PASSWORD: benefitlens_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready -U benefitlens_user -d benefitlens"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=10

    env:
      DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
      CACHE_TYPE: postgresql
      NEXT_PUBLIC_APP_URL: http://localhost:3000
      SESSION_SECRET: pr_validation_secret
      NODE_ENV: production
      USE_LOCAL_AUTH: true

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: package-lock.json
          
      - name: Install dependencies
        run: npm ci --legacy-peer-deps --include=dev

      - name: Install PostgreSQL client
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client

      - name: Wait for Postgres
        env:
          PGPASSWORD: benefitlens_password
        run: |
          for i in {1..30}; do
            if pg_isready -h localhost -p 5432 -U benefitlens_user -d benefitlens; then
              echo "Postgres is ready"; break
            fi
            echo "Waiting for Postgres... ($i)"; sleep 2
          done

      - name: Initialize database schema
        env:
          PGPASSWORD: benefitlens_password
        run: |
          psql -h localhost -U benefitlens_user -d benefitlens -f database/init/01-init.sql

      - name: Build application
        run: npm run build

  # Auto-approve and merge Dependabot PRs if tests pass
  auto-merge-dependabot:
    needs: [validate-pr, run-tests, build-validation]
    runs-on: ubuntu-latest
    if: |
      needs.validate-pr.outputs.is-dependabot == 'true' &&
      github.event.pull_request.draft == false &&
      needs.run-tests.result == 'success' &&
      needs.build-validation.result == 'success'
    
    permissions:
      contents: write
      pull-requests: write
      
    steps:
      - name: Auto-approve Dependabot PR
        uses: hmarr/auto-approve-action@v4
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Enable auto-merge for Dependabot PRs
        run: |
          gh pr merge --auto --squash "${{ github.event.pull_request.number }}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_REPO: ${{ github.repository }}

  # Status check for branch protection
  pr-validation-complete:
    needs: [validate-pr, run-tests, build-validation]
    runs-on: ubuntu-latest
    if: always() && github.event.pull_request.draft == false
    
    steps:
      - name: Check all jobs status
        run: |
          if [[ "${{ needs.run-tests.result }}" == "success" && "${{ needs.build-validation.result }}" == "success" ]]; then
            echo "✅ All PR validation checks passed"
            exit 0
          else
            echo "❌ PR validation failed"
            echo "Tests result: ${{ needs.run-tests.result }}"
            echo "Build result: ${{ needs.build-validation.result }}"
            exit 1
          fi
