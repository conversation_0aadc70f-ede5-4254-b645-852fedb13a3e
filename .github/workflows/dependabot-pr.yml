name: Dependabot PR Validation

# This workflow runs specifically on Dependabot PRs to ensure they pass all tests
# before being eligible for merge. It runs the same test suite as the main pipeline
# but with specific handling for automated dependency updates.

on:
  pull_request:
    branches: [ main, develop ]
    # Only run on Dependabot PRs
    types: [opened, synchronize, reopened]

permissions:
  contents: read
  security-events: write
  actions: read
  pull-requests: write  # Allow commenting on PRs

env:
  NODE_VERSION: '18'

jobs:
  # Check if this is a Dependabot PR
  check-dependabot:
    name: Check if Dependabot PR
    runs-on: ubuntu-latest
    outputs:
      is-dependabot: ${{ steps.check.outputs.is-dependabot }}
    steps:
      - name: Check if <PERSON> is from Dependabot
        id: check
        run: |
          if [[ "${{ github.actor }}" == "dependabot[bot]" ]]; then
            echo "is-dependabot=true" >> $GITHUB_OUTPUT
            echo "This is a Dependabot PR"
          else
            echo "is-dependabot=false" >> $GITHUB_OUTPUT
            echo "This is not a Dependabot PR"
          fi

  # Run tests for Dependabot PRs
  dependabot-tests:
    name: Dependabot Tests
    runs-on: ubuntu-latest
    needs: check-dependabot
    if: needs.check-dependabot.outputs.is-dependabot == 'true'
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: benefitlens_user
          POSTGRES_PASSWORD: benefitlens_password
          POSTGRES_DB: benefitlens
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Setup test database
      env:
        PGPASSWORD: benefitlens_password
      run: |
        # Wait for PostgreSQL to be ready
        until pg_isready -h localhost -p 5432 -U benefitlens_user; do
          echo "Waiting for PostgreSQL..."
          sleep 2
        done
        
        # Initialize database schema
        psql -h localhost -U benefitlens_user -d benefitlens -f database/schema.sql
        
    - name: Run unit tests
      run: npm run test:unit -- --run
      
    - name: Run integration tests
      env:
        DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
        PGPASSWORD: benefitlens_password
        NEXT_PUBLIC_APP_URL: http://localhost:3000
        APP_URL: http://localhost:3000
        LOG_LEVEL: warn
        USE_LOCAL_AUTH: true
        SESSION_SECRET: test-session-secret-for-ci-integration-tests
        CACHE_TYPE: postgresql
      run: npm run test:integration -- --run
      
    - name: Build application
      run: npm run build
      
    - name: Comment on PR
      if: success()
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '✅ **Dependabot PR Tests Passed**\n\nAll tests have passed successfully. This dependency update is ready for review and merge.\n\n- ✅ Unit tests passed\n- ✅ Integration tests passed\n- ✅ Build successful'
          })
          
    - name: Comment on PR failure
      if: failure()
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '❌ **Dependabot PR Tests Failed**\n\nSome tests failed for this dependency update. Please review the workflow logs and address any issues before merging.\n\n- Check the [workflow run](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}) for details'
          })

  # Auto-approve and enable auto-merge for patch updates of dev dependencies
  auto-merge:
    name: Auto-merge safe updates
    runs-on: ubuntu-latest
    needs: [check-dependabot, dependabot-tests]
    if: |
      needs.check-dependabot.outputs.is-dependabot == 'true' && 
      needs.dependabot-tests.result == 'success'
    
    steps:
    - name: Check if safe for auto-merge
      id: check-safe
      run: |
        # Check if this is a patch update for dev dependencies
        if [[ "${{ github.event.pull_request.title }}" =~ ^deps-dev.*patch ]]; then
          echo "safe-for-auto-merge=true" >> $GITHUB_OUTPUT
          echo "This is a safe dev dependency patch update"
        else
          echo "safe-for-auto-merge=false" >> $GITHUB_OUTPUT
          echo "This requires manual review"
        fi
        
    - name: Auto-approve safe updates
      if: steps.check-safe.outputs.safe-for-auto-merge == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.pulls.createReview({
            owner: context.repo.owner,
            repo: context.repo.repo,
            pull_number: context.issue.number,
            event: 'APPROVE',
            body: '✅ Auto-approved: Safe dev dependency patch update that passed all tests.'
          })
          
    - name: Enable auto-merge for safe updates
      if: steps.check-safe.outputs.safe-for-auto-merge == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.pulls.update({
            owner: context.repo.owner,
            repo: context.repo.repo,
            pull_number: context.issue.number,
            auto_merge: {
              merge_method: 'squash'
            }
          })
