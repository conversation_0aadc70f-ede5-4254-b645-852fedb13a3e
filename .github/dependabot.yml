version: 2
updates:
  # Enable version updates for npm dependencies
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "Europe/Berlin"
    open-pull-requests-limit: 10
    reviewers:
      - "rgarcia89"
    assignees:
      - "rgarcia89"
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "automated"
    # Group minor and patch updates together to reduce PR noise
    groups:
      production-dependencies:
        patterns:
          - "*"
        exclude-patterns:
          - "@types/*"
          - "eslint*"
          - "prettier*"
          - "typescript"
          - "vitest*"
          - "playwright*"
          - "@playwright/*"
        update-types:
          - "minor"
          - "patch"
      development-dependencies:
        patterns:
          - "@types/*"
          - "eslint*"
          - "prettier*"
          - "typescript"
          - "vitest*"
          - "playwright*"
          - "@playwright/*"
        update-types:
          - "minor"
          - "patch"
    # Allow automatic merging for patch updates of dev dependencies
    allow:
      - dependency-type: "development"
        update-type: "version-update:semver-patch"
    # Ignore specific packages that require manual review
    ignore:
      - dependency-name: "next"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-dom"
        update-types: ["version-update:semver-major"]
      - dependency-name: "@types/node"
        update-types: ["version-update:semver-major"]

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "Europe/Berlin"
    open-pull-requests-limit: 5
    reviewers:
      - "rgarcia89"
    assignees:
      - "rgarcia89"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "github-actions"
      - "automated"
    groups:
      github-actions:
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"

  # Enable version updates for Docker dependencies
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "Europe/Berlin"
    open-pull-requests-limit: 3
    reviewers:
      - "rgarcia89"
    assignees:
      - "rgarcia89"
    commit-message:
      prefix: "docker"
      include: "scope"
    labels:
      - "docker"
      - "automated"
