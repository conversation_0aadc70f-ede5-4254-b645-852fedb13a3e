# Dockerfile for Playwright testing with full browser support including Safari/WebKit
FROM mcr.microsoft.com/playwright:v1.48.0-jammy

# Set working directory
WORKDIR /app

# Install additional system dependencies for WebKit
RUN apt-get update && apt-get install -y \
    libgudev-1.0-0 \
    libglib2.0-0 \
    libglib2.0-dev \
    libgtk-3-0 \
    libgtk-3-dev \
    libgstreamer1.0-0 \
    libgstreamer-plugins-base1.0-0 \
    libgstreamer-plugins-bad1.0-0 \
    libgstreamer-plugins-good1.0-0 \
    libgstreamer-gl1.0-0 \
    libegl1-mesa \
    libgl1-mesa-glx \
    libxss1 \
    libasound2 \
    fonts-liberation \
    fonts-noto-color-emoji \
    && rm -rf /var/lib/apt/lists/*

# Copy package files
COPY package.json package-lock.json ./

# Install Node.js dependencies
RUN npm ci

# Install Playwright browsers (including WebKit with all dependencies)
RUN npx playwright install --with-deps

# Copy the rest of the application
COPY . .

# Create directories for test results with proper permissions
RUN mkdir -p /app/test-results /app/playwright-report-docker \
    && chmod 777 /app/test-results /app/playwright-report-docker

# Set environment variables for testing
ENV CI=true
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
ENV NODE_ENV=test

# Expose port for development server
EXPOSE 3000

# Default command to run tests
CMD ["npm", "run", "test:e2e"]
