version: '3.8'

services:
  # PostgreSQL Database for testing
  postgres-test:
    image: postgres:15
    container_name: benefitlens-postgres-test
    environment:
      POSTGRES_DB: benefitlens_test
      POSTGRES_USER: benefitlens_user
      POSTGRES_PASSWORD: benefitlens_password
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U benefitlens_user -d benefitlens_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - playwright-network

  # BenefitLens application for testing
  app-test:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: benefitlens-app-test
    environment:
      - NODE_ENV=test
      - DATABASE_URL=*********************************************************************/benefitlens_test
      - NEXTAUTH_SECRET=test-secret-key-for-playwright-testing
      - NEXTAUTH_URL=http://localhost:3000
      - APP_URL=http://localhost:3000
      - SMTP_HOST=mailhog-test
      - SMTP_PORT=1025
      - SMTP_USER=
      - SMTP_PASS=
      - SMTP_FROM=<EMAIL>
      - RATE_LIMIT_ENABLED=false
    ports:
      - "3000:3000"
    depends_on:
      postgres-test:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    networks:
      - playwright-network
    command: npm run dev

  # MailHog for email testing
  mailhog-test:
    image: mailhog/mailhog:latest
    container_name: benefitlens-mailhog-test
    ports:
      - "1026:1025"  # SMTP server (different port)
      - "8026:8025"  # Web UI (different port)
    networks:
      - playwright-network

  # Playwright test runner with Safari support
  playwright:
    build:
      context: .
      dockerfile: Dockerfile.playwright
    container_name: benefitlens-playwright
    environment:
      - NODE_ENV=test
      - DATABASE_URL=*********************************************************************/benefitlens_test
      - NEXTAUTH_SECRET=test-secret-key-for-playwright-testing
      - NEXTAUTH_URL=http://app-test:3000
      - APP_URL=http://app-test:3000
      - SMTP_HOST=mailhog-test
      - SMTP_PORT=1025
      - SMTP_USER=
      - SMTP_PASS=
      - SMTP_FROM=<EMAIL>
      - RATE_LIMIT_ENABLED=false
      - CI=true
      - PLAYWRIGHT_BASE_URL=http://app-test:3000
    depends_on:
      - app-test
      - postgres-test
    volumes:
      - .:/app
      - /app/node_modules
      - playwright_cache:/ms-playwright
    user: "1000:1000"  # Use host user ID to avoid permission issues
    networks:
      - playwright-network
    # Override command for different test types
    profiles:
      - testing

volumes:
  postgres_test_data:
  playwright_cache:

networks:
  playwright-network:
    driver: bridge
