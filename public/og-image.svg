<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg)"/>
  
  <!-- Main Title -->
  <text x="600" y="280" font-family="Arial, sans-serif" font-size="72" font-weight="bold" text-anchor="middle" fill="white">
    BenefitLens
  </text>
  
  <!-- Subtitle -->
  <text x="600" y="340" font-family="Arial, sans-serif" font-size="32" text-anchor="middle" fill="#E5E7EB">
    Compare Companies by Benefits
  </text>
  
  <!-- Description -->
  <text x="600" y="420" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" fill="#D1D5DB">
    Find companies offering the benefits you care about
  </text>
  
  <!-- Benefits icons/text -->
  <text x="300" y="520" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="#F3F4F6">
    🏥 Health Benefits
  </text>
  <text x="600" y="520" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="#F3F4F6">
    🏠 Remote Work
  </text>
  <text x="900" y="520" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="#F3F4F6">
    ⏰ Flexible Hours
  </text>
</svg>
